/**
 * TinyMCE version 6.7.0 (2023-08-30)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(n=o=e,(r=String).prototype.isPrototypeOf(n)||(null===(s=o.constructor)||void 0===s?void 0:s.name)===r.name)?"string":t;var n,o,r,s})(t)===e,n=e=>t=>typeof t===e,o=t("string"),r=t("object"),s=t("array"),i=n("boolean"),l=e=>!(e=>null==e)(e),a=n("function"),d=n("number"),c=()=>{},u=(e,t)=>e===t,m=e=>t=>!e(t),p=(!1,()=>false);class g{constructor(e,t){this.tag=e,this.value=t}static some(e){return new g(!0,e)}static none(){return g.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?g.some(e(this.value)):g.none()}bind(e){return this.tag?e(this.value):g.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:g.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return l(e)?g.some(e):g.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}g.singletonNone=new g(!1);const h=Array.prototype.slice,f=Array.prototype.indexOf,y=Array.prototype.push,v=(e,t)=>{return n=e,o=t,f.call(n,o)>-1;var n,o},C=(e,t)=>{for(let n=0,o=e.length;n<o;n++)if(t(e[n],n))return!0;return!1},b=(e,t)=>{const n=e.length,o=new Array(n);for(let r=0;r<n;r++){const n=e[r];o[r]=t(n,r)}return o},S=(e,t)=>{for(let n=0,o=e.length;n<o;n++)t(e[n],n)},N=(e,t)=>{const n=[];for(let o=0,r=e.length;o<r;o++){const r=e[o];t(r,o)&&n.push(r)}return n},L=(e,t,n)=>(S(e,((e,o)=>{n=t(n,e,o)})),n),O=(e,t,n)=>{for(let o=0,r=e.length;o<r;o++){const r=e[o];if(t(r,o))return g.some(r);if(n(r,o))break}return g.none()},k=(e,t)=>O(e,t,p),A=(e,t)=>(e=>{const t=[];for(let n=0,o=e.length;n<o;++n){if(!s(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);y.apply(t,e[n])}return t})(b(e,t)),T=e=>{const t=h.call(e,0);return t.reverse(),t},x=(e,t)=>t>=0&&t<e.length?g.some(e[t]):g.none(),E=e=>x(e,0),w=e=>x(e,e.length-1),D=(e,t)=>{const n=[],o=a(t)?e=>C(n,(n=>t(n,e))):e=>v(n,e);for(let t=0,r=e.length;t<r;t++){const r=e[t];o(r)||n.push(r)}return n},B=(e,t,n=u)=>e.exists((e=>n(e,t))),I=(e,t,n)=>e.isSome()&&t.isSome()?g.some(n(e.getOrDie(),t.getOrDie())):g.none(),P=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},M=(e,t)=>{const n=(t||document).createElement(e);return P(n)},R=P,U=(e,t)=>e.dom===t.dom;"undefined"!=typeof window?window:Function("return this;")();const $=e=>e.dom.nodeName.toLowerCase(),_=(1,e=>1===(e=>e.dom.nodeType)(e));const F=e=>t=>_(t)&&$(t)===e,H=e=>g.from(e.dom.parentNode).map(R),V=e=>b(e.dom.childNodes,R),j=(e,t)=>{const n=e.dom.childNodes;return g.from(n[t]).map(R)},K=e=>j(e,0),z=e=>j(e,e.dom.childNodes.length-1),Q=(e,t,n)=>{let o=e.dom;const r=a(n)?n:p;for(;o.parentNode;){o=o.parentNode;const e=R(o);if(t(e))return g.some(e);if(r(e))break}return g.none()},W=(e,t,n)=>((e,t,n,o,r)=>o(n)?g.some(n):a(r)&&r(n)?g.none():t(n,o,r))(0,Q,e,t,n),q=(e,t)=>{H(e).each((n=>{n.dom.insertBefore(t.dom,e.dom)}))},Z=(e,t)=>{e.dom.appendChild(t.dom)},G=(e,t)=>{S(t,(t=>{Z(e,t)}))},J=e=>{e.dom.textContent="",S(V(e),(e=>{X(e)}))},X=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)};var Y=tinymce.util.Tools.resolve("tinymce.dom.RangeUtils"),ee=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),te=tinymce.util.Tools.resolve("tinymce.util.VK");const ne=e=>b(e,R),oe=Object.keys,re=(e,t)=>{const n=oe(e);for(let o=0,r=n.length;o<r;o++){const r=n[o];t(e[r],r)}},se=(e,t)=>{const n=e.dom;re(t,((e,t)=>{((e,t,n)=>{if(!(o(n)||i(n)||d(n)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",n,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,n+"")})(n,t,e)}))},ie=e=>L(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),le=e=>((e,t)=>R(e.dom.cloneNode(!0)))(e),ae=(e,t)=>{const n=((e,t)=>{const n=M(t),o=ie(e);return se(n,o),n})(e,t);var o,r;r=n,(e=>g.from(e.dom.nextSibling).map(R))(o=e).fold((()=>{H(o).each((e=>{Z(e,r)}))}),(e=>{q(e,r)}));const s=V(e);return G(n,s),X(e),n};var de=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),ce=tinymce.util.Tools.resolve("tinymce.util.Tools");const ue=e=>t=>l(t)&&t.nodeName.toLowerCase()===e,me=e=>t=>l(t)&&e.test(t.nodeName),pe=e=>l(e)&&3===e.nodeType,ge=e=>l(e)&&1===e.nodeType,he=me(/^(OL|UL|DL)$/),fe=me(/^(OL|UL)$/),ye=ue("ol"),ve=me(/^(LI|DT|DD)$/),Ce=me(/^(DT|DD)$/),be=me(/^(TH|TD)$/),Se=ue("br"),Ne=(e,t)=>l(t)&&t.nodeName in e.schema.getTextBlockElements(),Le=(e,t)=>l(e)&&e.nodeName in t,Oe=(e,t)=>l(t)&&t.nodeName in e.schema.getVoidElements(),ke=(e,t,n)=>{const o=e.isEmpty(t);return!(n&&e.select("span[data-mce-type=bookmark]",t).length>0)&&o},Ae=(e,t)=>e.isChildOf(t,e.getRoot()),Te=e=>t=>t.options.get(e),xe=Te("lists_indent_on_tab"),Ee=Te("forced_root_block"),we=Te("forced_root_block_attrs"),De=(e,t)=>{const n=e.dom,o=e.schema.getBlockElements(),r=n.createFragment(),s=Ee(e),i=we(e);let l,a,d=!1;for(a=n.create(s,i),Le(t.firstChild,o)||r.appendChild(a);l=t.firstChild;){const e=l.nodeName;d||"SPAN"===e&&"bookmark"===l.getAttribute("data-mce-type")||(d=!0),Le(l,o)?(r.appendChild(l),a=null):(a||(a=n.create(s,i),r.appendChild(a)),a.appendChild(l))}return!d&&a&&a.appendChild(n.create("br",{"data-mce-bogus":"1"})),r},Be=de.DOM,Ie=F("dd"),Pe=F("dt"),Me=(e,t)=>{var n;Ie(t)?ae(t,"dt"):Pe(t)&&(n=t,g.from(n.dom.parentElement).map(R)).each((n=>((e,t,n)=>{const o=Be.select('span[data-mce-type="bookmark"]',t),r=De(e,n),s=Be.createRng();s.setStartAfter(n),s.setEndAfter(t);const i=s.extractContents();for(let t=i.firstChild;t;t=t.firstChild)if("LI"===t.nodeName&&e.dom.isEmpty(t)){Be.remove(t);break}e.dom.isEmpty(i)||Be.insertAfter(i,t),Be.insertAfter(r,t);const l=n.parentElement;l&&ke(e.dom,l)&&(e=>{const t=e.parentNode;t&&ce.each(o,(e=>{t.insertBefore(e,n.parentNode)})),Be.remove(e)})(l),Be.remove(n),ke(e.dom,t)&&Be.remove(t)})(e,n.dom,t.dom)))},Re=e=>{Pe(e)&&ae(e,"dd")},Ue=(e,t)=>{if(pe(e))return{container:e,offset:t};const n=Y.getNode(e,t);return pe(n)?{container:n,offset:t>=e.childNodes.length?n.data.length:0}:n.previousSibling&&pe(n.previousSibling)?{container:n.previousSibling,offset:n.previousSibling.data.length}:n.nextSibling&&pe(n.nextSibling)?{container:n.nextSibling,offset:0}:{container:e,offset:t}},$e=e=>{const t=e.cloneRange(),n=Ue(e.startContainer,e.startOffset);t.setStart(n.container,n.offset);const o=Ue(e.endContainer,e.endOffset);return t.setEnd(o.container,o.offset),t},_e=["OL","UL","DL"],Fe=_e.join(","),He=(e,t)=>{const n=t||e.selection.getStart(!0);return e.dom.getParent(n,Fe,Ke(e,n))},Ve=e=>{const t=e.selection.getSelectedBlocks();return N(((e,t)=>{const n=ce.map(t,(t=>e.dom.getParent(t,"li,dd,dt",Ke(e,t))||t));return D(n)})(e,t),ve)},je=(e,t)=>{const n=e.dom.getParents(t,"TD,TH");return n.length>0?n[0]:e.getBody()},Ke=(e,t)=>{const n=e.dom.getParents(t,e.dom.isBlock),o=k(n,(t=>{return n=e.schema,!he(o=t)&&!ve(o)&&C(_e,(e=>n.isValidChild(o.nodeName,e)));var n,o}));return o.getOr(e.getBody())},ze=(e,t)=>{const n=e.dom.getParents(t,"ol,ul",Ke(e,t));return w(n)},Qe=(e,t)=>{const n=b(t,(t=>ze(e,t).getOr(t)));return D(n)},We=e=>/\btox\-/.test(e.className),qe=(e,t)=>O(e,he,be).exists((e=>e.nodeName===t&&!We(e))),Ze=(e,t)=>null!==t&&!e.dom.isEditable(t),Ge=(e,t)=>{const n=e.dom.getParent(t,"ol,ul,dl");return Ze(e,n)},Je=(e,t)=>{const n=e.selection.getNode();return t({parents:e.dom.getParents(n),element:n}),e.on("NodeChange",t),()=>e.off("NodeChange",t)},Xe=(e,t,n)=>e.dispatch("ListMutation",{action:t,element:n}),Ye=(et=/^\s+|\s+$/g,e=>e.replace(et,""));var et;const tt=(e,t,n)=>{((e,t,n)=>{if(!o(n))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",n,":: Element ",e),new Error("CSS value must be a string: "+n);(e=>void 0!==e.style&&a(e.style.getPropertyValue))(e)&&e.style.setProperty(t,n)})(e.dom,t,n)},nt=(e,t)=>{Z(e.item,t.list)},ot=(e,t)=>{const n={list:M(t,e),item:M("li",e)};return Z(n.list,n.item),n},rt=e=>((e,t)=>{const n=e.dom;if(1!==n.nodeType)return!1;{const e=n;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}})(e,"OL,UL"),st=e=>K(e).exists(rt),it=e=>e.depth>0,lt=e=>e.isSelected,at=e=>{const t=V(e),n=z(e).exists(rt)?t.slice(0,-1):t;return b(n,le)},dt=e=>(S(e,((t,n)=>{((e,t)=>{const n=e[t].depth,o=e=>e.depth===n&&!e.dirty,r=e=>e.depth<n;return O(T(e.slice(0,t)),o,r).orThunk((()=>O(e.slice(t+1),o,r)))})(e,n).fold((()=>{t.dirty&&(e=>{e.listAttributes=((e,t)=>{const n={};var o;return((e,t,n,o)=>{re(e,((e,r)=>{(t(e,r)?n:o)(e,r)}))})(e,t,(o=n,(e,t)=>{o[t]=e}),c),n})(e.listAttributes,((e,t)=>"start"!==t))})(t)}),(e=>{return o=e,(n=t).listType=o.listType,void(n.listAttributes={...o.listAttributes});var n,o}))})),e),ct=(e,t,n,o)=>K(o).filter(rt).fold((()=>{t.each((e=>{U(e.start,o)&&n.set(!0)}));const r=((e,t,n)=>H(e).filter(_).map((o=>({depth:t,dirty:!1,isSelected:n,content:at(e),itemAttributes:ie(e),listAttributes:ie(o),listType:$(o)}))))(o,e,n.get());t.each((e=>{U(e.end,o)&&n.set(!1)}));const s=z(o).filter(rt).map((o=>ut(e,t,n,o))).getOr([]);return r.toArray().concat(s)}),(o=>ut(e,t,n,o))),ut=(e,t,n,o)=>A(V(o),(o=>(rt(o)?ut:ct)(e+1,t,n,o))),mt=(e,t)=>{const n=dt(t);return((e,t)=>{const n=L(t,((t,n)=>n.depth>t.length?((e,t,n)=>{const o=((e,t,n)=>{const o=[];for(let r=0;r<n;r++)o.push(ot(e,t.listType));return o})(e,n,n.depth-t.length);var r;return(e=>{for(let t=1;t<e.length;t++)nt(e[t-1],e[t])})(o),((e,t)=>{for(let t=0;t<e.length-1;t++)tt(e[t].item,"list-style-type","none");w(e).each((e=>{se(e.list,t.listAttributes),se(e.item,t.itemAttributes),G(e.item,t.content)}))})(o,n),r=o,I(w(t),E(r),nt),t.concat(o)})(e,t,n):((e,t,n)=>{const o=t.slice(0,n.depth);return w(o).each((t=>{const o=((e,t,n)=>{const o=M("li",e);return se(o,t),G(o,n),o})(e,n.itemAttributes,n.content);((e,t)=>{Z(e.list,t),e.item=t})(t,o),((e,t)=>{$(e.list)!==t.listType&&(e.list=ae(e.list,t.listType)),se(e.list,t.listAttributes)})(t,n)})),o})(e,t,n)),[]);return E(n).map((e=>e.list))})(e.contentDocument,n).toArray()},pt=(e,t,n)=>{const o=((e,t)=>{const n=(e=>{let t=!1;return{get:()=>t,set:e=>{t=e}}})();return b(e,(e=>({sourceList:e,entries:ut(0,t,n,e)})))})(t,(e=>{const t=b(Ve(e),R);return I(k(t,m(st)),k(T(t),m(st)),((e,t)=>({start:e,end:t})))})(e));S(o,(t=>{((e,t)=>{S(N(e,lt),(e=>((e,t)=>{switch(e){case"Indent":t.depth++;break;case"Outdent":t.depth--;break;case"Flatten":t.depth=0}t.dirty=!0})(t,e)))})(t.entries,n);const o=((e,t)=>A(((e,t)=>{if(0===e.length)return[];{let n=t(e[0]);const o=[];let r=[];for(let s=0,i=e.length;s<i;s++){const i=e[s],l=t(i);l!==n&&(o.push(r),r=[]),n=l,r.push(i)}return 0!==r.length&&o.push(r),o}})(t,it),(t=>E(t).exists(it)?mt(e,t):((e,t)=>{const n=dt(t);return b(n,(t=>{const n=((e,t)=>{const n=document.createDocumentFragment();return S(e,(e=>{n.appendChild(e.dom)})),R(n)})(t.content);return R(De(e,n.dom))}))})(e,t))))(e,t.entries);var r;S(o,(t=>{Xe(e,"Indent"===n?"IndentList":"OutdentList",t.dom)})),r=t.sourceList,S(o,(e=>{q(r,e)})),X(t.sourceList)}))},gt=(e,t)=>{const n=ne((e=>{const t=(e=>{const t=ze(e,e.selection.getStart()),n=N(e.selection.getSelectedBlocks(),fe);return t.toArray().concat(n)})(e);return Qe(e,t)})(e)),o=ne((e=>N(Ve(e),Ce))(e));let r=!1;if(n.length||o.length){const s=e.selection.getBookmark();pt(e,n,t),((e,t,n)=>{S(n,"Indent"===t?Re:t=>Me(e,t))})(e,t,o),e.selection.moveToBookmark(s),e.selection.setRng($e(e.selection.getRng())),e.nodeChanged(),r=!0}return r},ht=(e,t)=>!(e=>{const t=He(e);return Ze(e,t)})(e)&&gt(e,t),ft=e=>ht(e,"Indent"),yt=e=>ht(e,"Outdent"),vt=e=>ht(e,"Flatten"),Ct=e=>"\ufeff"===e;var bt=tinymce.util.Tools.resolve("tinymce.dom.BookmarkManager");const St=de.DOM,Nt=e=>{const t={},n=n=>{let o=e[n?"startContainer":"endContainer"],r=e[n?"startOffset":"endOffset"];if(ge(o)){const e=St.create("span",{"data-mce-type":"bookmark"});o.hasChildNodes()?(r=Math.min(r,o.childNodes.length-1),n?o.insertBefore(e,o.childNodes[r]):St.insertAfter(e,o.childNodes[r])):o.appendChild(e),o=e,r=0}t[n?"startContainer":"endContainer"]=o,t[n?"startOffset":"endOffset"]=r};return n(!0),e.collapsed||n(),t},Lt=e=>{const t=t=>{let n=e[t?"startContainer":"endContainer"],o=e[t?"startOffset":"endOffset"];if(n){if(ge(n)&&n.parentNode){const e=n;o=(e=>{var t;let n=null===(t=e.parentNode)||void 0===t?void 0:t.firstChild,o=0;for(;n;){if(n===e)return o;ge(n)&&"bookmark"===n.getAttribute("data-mce-type")||o++,n=n.nextSibling}return-1})(n),n=n.parentNode,St.remove(e),!n.hasChildNodes()&&St.isBlock(n)&&n.appendChild(St.create("br"))}e[t?"startContainer":"endContainer"]=n,e[t?"startOffset":"endOffset"]=o}};t(!0),t();const n=St.createRng();return n.setStart(e.startContainer,e.startOffset),e.endContainer&&n.setEnd(e.endContainer,e.endOffset),$e(n)},Ot=e=>{switch(e){case"UL":return"ToggleUlList";case"OL":return"ToggleOlList";case"DL":return"ToggleDLList"}},kt=(e,t)=>{ce.each(t,((t,n)=>{e.setAttribute(n,t)}))},At=(e,t,n)=>{((e,t,n)=>{const o=n["list-style-type"]?n["list-style-type"]:null;e.setStyle(t,"list-style-type",o)})(e,t,n),((e,t,n)=>{kt(t,n["list-attributes"]),ce.each(e.select("li",t),(e=>{kt(e,n["list-item-attributes"])}))})(e,t,n)},Tt=(e,t)=>l(t)&&!Le(t,e.schema.getBlockElements()),xt=(e,t,n,o)=>{let r=t[n?"startContainer":"endContainer"];const s=t[n?"startOffset":"endOffset"];ge(r)&&(r=r.childNodes[Math.min(s,r.childNodes.length-1)]||r),!n&&Se(r.nextSibling)&&(r=r.nextSibling);const i=(t,n)=>{var r;const s=new ee(t,(t=>{for(;!e.dom.isBlock(t)&&t.parentNode&&o!==t;)t=t.parentNode;return t})(t)),i=n?"next":"prev";let l;for(;l=s[i]();)if(!Oe(e,l)&&!Ct(l.textContent)&&0!==(null===(r=l.textContent)||void 0===r?void 0:r.length))return g.some(l);return g.none()};if(n&&pe(r))if(Ct(r.textContent))r=i(r,!1).getOr(r);else for(null!==r.parentNode&&Tt(e,r.parentNode)&&(r=r.parentNode);null!==r.previousSibling&&(Tt(e,r.previousSibling)||pe(r.previousSibling));)r=r.previousSibling;if(!n&&pe(r))if(Ct(r.textContent))r=i(r,!0).getOr(r);else for(null!==r.parentNode&&Tt(e,r.parentNode)&&(r=r.parentNode);null!==r.nextSibling&&(Tt(e,r.nextSibling)||pe(r.nextSibling));)r=r.nextSibling;for(;r.parentNode!==o;){const t=r.parentNode;if(Ne(e,r))return r;if(/^(TD|TH)$/.test(t.nodeName))return r;r=t}return r},Et=(e,t,n)=>{const o=e.selection.getRng();let r="LI";const s=Ke(e,((e,t)=>{const n=e.selection.getStart(!0),o=xt(e,t,!0,e.getBody());return r=R(o),s=R(t.commonAncestorContainer),i=r,l=function(e,...t){return(...n)=>{const o=t.concat(n);return e.apply(null,o)}}(U,s),Q(i,l,void 0).isSome()?t.commonAncestorContainer:n;var r,s,i,l})(e,o)),i=e.dom;if("false"===i.getContentEditable(e.selection.getNode()))return;"DL"===(t=t.toUpperCase())&&(r="DT");const l=Nt(o),a=N(((e,t,n)=>{const o=[],r=e.dom,s=xt(e,t,!0,n),i=xt(e,t,!1,n);let l;const a=[];for(let e=s;e&&(a.push(e),e!==i);e=e.nextSibling);return ce.each(a,(t=>{var s;if(Ne(e,t))return o.push(t),void(l=null);if(r.isBlock(t)||Se(t))return Se(t)&&r.remove(t),void(l=null);const i=t.nextSibling;bt.isBookmarkNode(t)&&(he(i)||Ne(e,i)||!i&&t.parentNode===n)?l=null:(l||(l=r.create("p"),null===(s=t.parentNode)||void 0===s||s.insertBefore(l,t),o.push(l)),l.appendChild(t))})),o})(e,o,s),e.dom.isEditable);ce.each(a,(o=>{let s;const l=o.previousSibling,a=o.parentNode;ve(a)||(l&&he(l)&&l.nodeName===t&&((e,t,n)=>{const o=e.getStyle(t,"list-style-type");let r=n?n["list-style-type"]:"";return r=null===r?"":r,o===r})(i,l,n)?(s=l,o=i.rename(o,r),l.appendChild(o)):(s=i.create(t),a.insertBefore(s,o),s.appendChild(o),o=i.rename(o,r)),((e,t,n)=>{ce.each(["margin","margin-right","margin-bottom","margin-left","margin-top","padding","padding-right","padding-bottom","padding-left","padding-top"],(n=>e.setStyle(t,n,"")))})(i,o),At(i,s,n),Dt(e.dom,s))})),e.selection.setRng(Lt(l))},wt=(e,t,n)=>{return((e,t)=>he(e)&&e.nodeName===(null==t?void 0:t.nodeName))(t,n)&&((e,t,n)=>e.getStyle(t,"list-style-type",!0)===e.getStyle(n,"list-style-type",!0))(e,t,n)&&(o=n,t.className===o.className);var o},Dt=(e,t)=>{let n,o=t.nextSibling;if(wt(e,t,o)){const r=o;for(;n=r.firstChild;)t.appendChild(n);e.remove(r)}if(o=t.previousSibling,wt(e,t,o)){const r=o;for(;n=r.lastChild;)t.insertBefore(n,t.firstChild);e.remove(r)}},Bt=(e,t,n,o)=>{if(t.nodeName!==n){const r=e.dom.rename(t,n);At(e.dom,r,o),Xe(e,Ot(n),r)}else At(e.dom,t,o),Xe(e,Ot(n),t)},It=(e,t,n,o)=>{if(t.classList.forEach(((e,n,o)=>{e.startsWith("tox-")&&(o.remove(e),0===o.length&&t.removeAttribute("class"))})),t.nodeName!==n){const r=e.dom.rename(t,n);At(e.dom,r,o),Xe(e,Ot(n),r)}else At(e.dom,t,o),Xe(e,Ot(n),t)},Pt=e=>"list-style-type"in e,Mt=(e,t,n)=>{const o=He(e);if(Ge(e,o))return;const s=(e=>{const t=He(e),n=e.selection.getSelectedBlocks();return((e,t)=>l(e)&&1===t.length&&t[0]===e)(t,n)?(e=>N(e.querySelectorAll(Fe),he))(t):N(n,(e=>he(e)&&t!==e))})(e),i=r(n)?n:{};s.length>0?((e,t,n,o,r)=>{const s=he(t);if(!s||t.nodeName!==o||Pt(r)||We(t)){Et(e,o,r);const i=Nt(e.selection.getRng()),l=s?[t,...n]:n,a=s&&We(t)?It:Bt;ce.each(l,(t=>{a(e,t,o,r)})),e.selection.setRng(Lt(i))}else vt(e)})(e,o,s,t,i):((e,t,n,o)=>{if(t!==e.getBody())if(t)if(t.nodeName!==n||Pt(o)||We(t)){const r=Nt(e.selection.getRng());We(t)&&t.classList.forEach(((e,n,o)=>{e.startsWith("tox-")&&(o.remove(e),0===o.length&&t.removeAttribute("class"))})),At(e.dom,t,o);const s=e.dom.rename(t,n);Dt(e.dom,s),e.selection.setRng(Lt(r)),Et(e,n,o),Xe(e,Ot(n),s)}else vt(e);else Et(e,n,o),Xe(e,Ot(n),t)})(e,o,t,i)},Rt=de.DOM,Ut=(e,t)=>{const n=ce.grep(e.select("ol,ul",t));ce.each(n,(t=>{((e,t)=>{const n=t.parentElement;if(n&&"LI"===n.nodeName&&n.firstChild===t){const o=n.previousSibling;o&&"LI"===o.nodeName?(o.appendChild(t),ke(e,n)&&Rt.remove(n)):Rt.setStyle(n,"listStyleType","none")}if(he(n)){const e=n.previousSibling;e&&"LI"===e.nodeName&&e.appendChild(t)}})(e,t)}))},$t=(e,t,n,o)=>{let r=t.startContainer;const s=t.startOffset;if(pe(r)&&(n?s<r.data.length:s>0))return r;const i=e.schema.getNonEmptyElements();ge(r)&&(r=Y.getNode(r,s));const l=new ee(r,o);n&&((e,t)=>!!Se(t)&&e.isBlock(t.nextSibling)&&!Se(t.previousSibling))(e.dom,r)&&l.next();const a=n?l.next.bind(l):l.prev2.bind(l);for(;r=a();){if("LI"===r.nodeName&&!r.hasChildNodes())return r;if(i[r.nodeName])return r;if(pe(r)&&r.data.length>0)return r}return null},_t=(e,t)=>{const n=t.childNodes;return 1===n.length&&!he(n[0])&&e.isBlock(n[0])},Ft=(e,t,n)=>{let o;const r=t.parentNode;if(!Ae(e,t)||!Ae(e,n))return;he(n.lastChild)&&(o=n.lastChild),r===n.lastChild&&Se(r.previousSibling)&&e.remove(r.previousSibling);const s=n.lastChild;s&&Se(s)&&t.hasChildNodes()&&e.remove(s),ke(e,n,!0)&&J(R(n)),((e,t,n)=>{let o;const r=_t(e,n)?n.firstChild:n;if(((e,t)=>{_t(e,t)&&e.remove(t.firstChild,!0)})(e,t),!ke(e,t,!0))for(;o=t.firstChild;)r.appendChild(o)})(e,t,n),o&&n.appendChild(o);const i=((e,t)=>{const n=e.dom,o=t.dom;return n!==o&&n.contains(o)})(R(n),R(t))?e.getParents(t,he,n):[];e.remove(t),S(i,(t=>{ke(e,t)&&t!==e.getRoot()&&e.remove(t)}))},Ht=(e,t)=>{const n=e.dom,o=e.selection,r=o.getStart(),s=je(e,r),i=n.getParent(o.getStart(),"LI",s);if(i){const r=i.parentElement;if(r===e.getBody()&&ke(n,r))return!0;const l=$e(o.getRng()),a=n.getParent($t(e,l,t,s),"LI",s);if(a&&a!==i)return e.undoManager.transact((()=>{var n,o;t?((e,t,n,o)=>{const r=e.dom;if(r.isEmpty(o))((e,t,n)=>{J(R(n)),Ft(e.dom,t,n),e.selection.setCursorLocation(n,0)})(e,n,o);else{const s=Nt(t);Ft(r,n,o),e.selection.setRng(Lt(s))}})(e,l,a,i):(null===(o=(n=i).parentNode)||void 0===o?void 0:o.firstChild)===n?yt(e):((e,t,n,o)=>{const r=Nt(t);Ft(e.dom,n,o);const s=Lt(r);e.selection.setRng(s)})(e,l,i,a)})),!0;if(!a&&!t&&0===l.startOffset&&0===l.endOffset)return e.undoManager.transact((()=>{vt(e)})),!0}return!1},Vt=e=>{const t=e.selection.getStart(),n=je(e,t);return e.dom.getParent(t,"LI,DT,DD",n)||Ve(e).length>0},jt=(e,t)=>{const n=e.selection;return!Ge(e,n.getNode())&&(n.isCollapsed()?((e,t)=>Ht(e,t)||((e,t)=>{const n=e.dom,o=e.selection.getStart(),r=je(e,o),s=n.getParent(o,n.isBlock,r);if(s&&n.isEmpty(s)){const o=$e(e.selection.getRng()),i=n.getParent($t(e,o,t,r),"LI",r);if(i){const l=e=>v(["td","th","caption"],$(e)),a=e=>e.dom===r;return!!((e,t,n=u)=>I(e,t,n).getOr(e.isNone()&&t.isNone()))(W(R(i),l,a),W(R(o.startContainer),l,a),U)&&(e.undoManager.transact((()=>{const o=i.parentNode;((e,t,n)=>{const o=e.getParent(t.parentNode,e.isBlock,n);e.remove(t),o&&e.isEmpty(o)&&e.remove(o)})(n,s,r),Dt(n,o),e.selection.select(i,!0),e.selection.collapse(t)})),!0)}}return!1})(e,t))(e,t):(e=>!!Vt(e)&&(e.undoManager.transact((()=>{e.execCommand("Delete"),Ut(e.dom,e.getBody())})),!0))(e))},Kt=e=>{const t=T(Ye(e).split("")),n=b(t,((e,t)=>{const n=e.toUpperCase().charCodeAt(0)-"A".charCodeAt(0)+1;return Math.pow(26,t)*n}));return L(n,((e,t)=>e+t),0)},zt=e=>{if(--e<0)return"";{const t=e%26,n=Math.floor(e/26);return zt(n)+String.fromCharCode("A".charCodeAt(0)+t)}},Qt=e=>{const t=parseInt(e.start,10);return B(e.listStyleType,"upper-alpha")?zt(t):B(e.listStyleType,"lower-alpha")?zt(t).toLowerCase():e.start},Wt=(e,t)=>()=>{const n=He(e);return l(n)&&n.nodeName===t},qt=e=>{e.addCommand("mceListProps",(()=>{(e=>{const t=He(e);ye(t)&&!Ge(e,t)&&e.windowManager.open({title:"List Properties",body:{type:"panel",items:[{type:"input",name:"start",label:"Start list at number",inputMode:"numeric"}]},initialData:{start:Qt({start:e.dom.getAttrib(t,"start","1"),listStyleType:g.from(e.dom.getStyle(t,"list-style-type"))})},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],onSubmit:t=>{(e=>{switch((e=>/^[0-9]+$/.test(e)?2:/^[A-Z]+$/.test(e)?0:/^[a-z]+$/.test(e)?1:e.length>0?4:3)(e)){case 2:return g.some({listStyleType:g.none(),start:e});case 0:return g.some({listStyleType:g.some("upper-alpha"),start:Kt(e).toString()});case 1:return g.some({listStyleType:g.some("lower-alpha"),start:Kt(e).toString()});case 3:return g.some({listStyleType:g.none(),start:""});case 4:return g.none()}})(t.getData().start).each((t=>{e.execCommand("mceListUpdate",!1,{attrs:{start:"1"===t.start?"":t.start},styles:{"list-style-type":t.listStyleType.getOr("")}})})),t.close()}})})(e)}))};var Zt=tinymce.util.Tools.resolve("tinymce.html.Node");const Gt=e=>3===e.type,Jt=e=>0===e.length,Xt=e=>{const t=(t,n)=>{const o=Zt.create("li");S(t,(e=>o.append(e))),n?e.insert(o,n,!0):e.append(o)},n=L(e.children(),((e,n)=>Gt(n)?[...e,n]:Jt(e)||Gt(n)?e:(t(e,n),[])),[]);Jt(n)||t(n)},Yt=(e,t)=>n=>(n.setEnabled(e.selection.isEditable()),Je(e,(o=>{n.setActive(qe(o.parents,t)),n.setEnabled(!Ge(e,o.element)&&e.selection.isEditable())}))),en=(e,t)=>n=>Je(e,(o=>n.setEnabled(qe(o.parents,t)&&!Ge(e,o.element))));e.add("lists",(e=>((e=>{(0,e.options.register)("lists_indent_on_tab",{processor:"boolean",default:!0})})(e),(e=>{e.on("PreInit",(()=>{const{parser:t}=e;t.addNodeFilter("ul,ol",(e=>S(e,Xt)))}))})(e),e.hasPlugin("rtc",!0)?qt(e):((e=>{xe(e)&&(e=>{e.on("keydown",(t=>{t.keyCode!==te.TAB||te.metaKeyPressed(t)||e.undoManager.transact((()=>{(t.shiftKey?yt(e):ft(e))&&t.preventDefault()}))}))})(e),(e=>{e.on("ExecCommand",(t=>{const n=t.command.toLowerCase();"delete"!==n&&"forwarddelete"!==n||!Vt(e)||Ut(e.dom,e.getBody())})),e.on("keydown",(t=>{t.keyCode===te.BACKSPACE?jt(e,!1)&&t.preventDefault():t.keyCode===te.DELETE&&jt(e,!0)&&t.preventDefault()}))})(e)})(e),(e=>{e.on("BeforeExecCommand",(t=>{const n=t.command.toLowerCase();"indent"===n?ft(e):"outdent"===n&&yt(e)})),e.addCommand("InsertUnorderedList",((t,n)=>{Mt(e,"UL",n)})),e.addCommand("InsertOrderedList",((t,n)=>{Mt(e,"OL",n)})),e.addCommand("InsertDefinitionList",((t,n)=>{Mt(e,"DL",n)})),e.addCommand("RemoveList",(()=>{vt(e)})),qt(e),e.addCommand("mceListUpdate",((t,n)=>{r(n)&&((e,t)=>{const n=He(e);null===n||Ge(e,n)||e.undoManager.transact((()=>{r(t.styles)&&e.dom.setStyles(n,t.styles),r(t.attrs)&&re(t.attrs,((t,o)=>e.dom.setAttrib(n,o,t)))}))})(e,n)})),e.addQueryStateHandler("InsertUnorderedList",Wt(e,"UL")),e.addQueryStateHandler("InsertOrderedList",Wt(e,"OL")),e.addQueryStateHandler("InsertDefinitionList",Wt(e,"DL"))})(e)),(e=>{const t=t=>()=>e.execCommand(t);e.hasPlugin("advlist")||(e.ui.registry.addToggleButton("numlist",{icon:"ordered-list",active:!1,tooltip:"Numbered list",onAction:t("InsertOrderedList"),onSetup:Yt(e,"OL")}),e.ui.registry.addToggleButton("bullist",{icon:"unordered-list",active:!1,tooltip:"Bullet list",onAction:t("InsertUnorderedList"),onSetup:Yt(e,"UL")}))})(e),(e=>{const t={text:"List properties...",icon:"ordered-list",onAction:()=>e.execCommand("mceListProps"),onSetup:en(e,"OL")};e.ui.registry.addMenuItem("listprops",t),e.ui.registry.addContextMenu("lists",{update:t=>{const n=He(e,t);return ye(n)?["listprops"]:[]}})})(e),(e=>({backspaceDelete:t=>{jt(e,t)}}))(e))))}();