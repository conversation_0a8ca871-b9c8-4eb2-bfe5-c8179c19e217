/**
 * TinyMCE version 6.7.0 (2023-08-30)
 */
!function(){"use strict";const e=Object.getPrototypeOf,t=(e,t,o)=>{var n;return!!o(e,t.prototype)||(null===(n=e.constructor)||void 0===n?void 0:n.name)===t.name},o=e=>o=>(e=>{const o=typeof e;return null===e?"null":"object"===o&&Array.isArray(e)?"array":"object"===o&&t(e,String,((e,t)=>t.isPrototypeOf(e)))?"string":o})(o)===e,n=e=>t=>typeof t===e,s=e=>t=>e===t,r=o("string"),a=o("object"),i=o=>((o,n)=>a(o)&&t(o,n,((t,o)=>e(t)===o)))(o,Object),l=o("array"),c=s(null),d=n("boolean"),u=s(void 0),m=e=>null==e,g=e=>!m(e),p=n("function"),h=n("number"),f=(e,t)=>{if(l(e)){for(let o=0,n=e.length;o<n;++o)if(!t(e[o]))return!1;return!0}return!1},b=()=>{},v=e=>()=>e(),y=(e,t)=>(...o)=>e(t.apply(null,o)),x=e=>()=>e,w=e=>e,S=(e,t)=>e===t;function k(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const C=e=>t=>!e(t),O=e=>()=>{throw new Error(e)},_=e=>e(),T=x(!1),E=x(!0);class A{constructor(e,t){this.tag=e,this.value=t}static some(e){return new A(!0,e)}static none(){return A.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?A.some(e(this.value)):A.none()}bind(e){return this.tag?e(this.value):A.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:A.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return g(e)?A.some(e):A.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}A.singletonNone=new A(!1);const M=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,F=(e,t)=>D.call(e,t),I=(e,t)=>{const o=F(e,t);return-1===o?A.none():A.some(o)},R=(e,t)=>F(e,t)>-1,N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},V=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},z=(e,t)=>{const o=[];for(let n=0;n<e.length;n+=t){const s=M.call(e,n,n+t);o.push(s)}return o},H=(e,t)=>{const o=e.length,n=new Array(o);for(let s=0;s<o;s++){const o=e[s];n[s]=t(o,s)}return n},L=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},P=(e,t)=>{const o=[],n=[];for(let s=0,r=e.length;s<r;s++){const r=e[s];(t(r,s)?o:n).push(r)}return{pass:o,fail:n}},U=(e,t)=>{const o=[];for(let n=0,s=e.length;n<s;n++){const s=e[n];t(s,n)&&o.push(s)}return o},W=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),j=(e,t,o)=>(L(e,((e,n)=>{o=t(o,e,n)})),o),G=(e,t)=>((e,t,o)=>{for(let n=0,s=e.length;n<s;n++){const s=e[n];if(t(s,n))return A.some(s);if(o(s,n))break}return A.none()})(e,t,T),$=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return A.some(o);return A.none()},q=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);B.apply(t,e[o])}return t},X=(e,t)=>q(H(e,t)),Y=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},K=e=>{const t=M.call(e,0);return t.reverse(),t},J=(e,t)=>U(e,(e=>!R(t,e))),Z=(e,t)=>{const o={};for(let n=0,s=e.length;n<s;n++){const s=e[n];o[String(s)]=t(s,n)}return o},Q=e=>[e],ee=(e,t)=>{const o=M.call(e,0);return o.sort(t),o},te=(e,t)=>t>=0&&t<e.length?A.some(e[t]):A.none(),oe=e=>te(e,0),ne=e=>te(e,e.length-1),se=p(Array.from)?Array.from:e=>M.call(e),re=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return A.none()},ae=Object.keys,ie=Object.hasOwnProperty,le=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n];t(e[s],s)}},ce=(e,t)=>de(e,((e,o)=>({k:o,v:t(e,o)}))),de=(e,t)=>{const o={};return le(e,((e,n)=>{const s=t(e,n);o[s.k]=s.v})),o},ue=e=>(t,o)=>{e[o]=t},me=(e,t,o,n)=>{le(e,((e,s)=>{(t(e,s)?o:n)(e,s)}))},ge=(e,t)=>{const o={};return me(e,t,ue(o),b),o},pe=(e,t)=>{const o=[];return le(e,((e,n)=>{o.push(t(e,n))})),o},he=(e,t)=>{const o=ae(e);for(let n=0,s=o.length;n<s;n++){const s=o[n],r=e[s];if(t(r,s,e))return A.some(r)}return A.none()},fe=e=>pe(e,w),be=(e,t)=>ve(e,t)?A.from(e[t]):A.none(),ve=(e,t)=>ie.call(e,t),ye=(e,t)=>ve(e,t)&&void 0!==e[t]&&null!==e[t],xe=(e,t,o=S)=>e.exists((e=>o(e,t))),we=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},Se=(e,t,o)=>e.isSome()&&t.isSome()?A.some(o(e.getOrDie(),t.getOrDie())):A.none(),ke=(e,t)=>null!=e?A.some(t(e)):A.none(),Ce=(e,t)=>e?A.some(t):A.none(),Oe=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,_e=(e,t)=>Ee(e,t)?((e,t)=>e.substring(t))(e,t.length):e,Te=(e,t,o=0,n)=>{const s=e.indexOf(t,o);return-1!==s&&(!!u(n)||s+t.length<=n)},Ee=(e,t)=>Oe(e,t,0),Ae=(e,t)=>Oe(e,t,e.length-t.length),Me=(Ao=/^\s+|\s+$/g,e=>e.replace(Ao,"")),De=e=>e.length>0,Be=e=>void 0!==e.style&&p(e.style.getPropertyValue),Fe=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Ie=(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return Fe(o.childNodes[0])},Re=(e,t)=>{const o=(t||document).createElement(e);return Fe(o)},Ne=(e,t)=>{const o=(t||document).createTextNode(e);return Fe(o)},Ve=Fe,ze="undefined"!=typeof window?window:Function("return this;")(),He=(e,t)=>((e,t)=>{let o=null!=t?t:ze;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),Le=Object.getPrototypeOf,Pe=e=>{const t=He("ownerDocument.defaultView",e);return a(e)&&((e=>((e,t)=>{const o=((e,t)=>He(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Le(e).constructor.name))},Ue=e=>e.dom.nodeName.toLowerCase(),We=e=>t=>(e=>e.dom.nodeType)(t)===e,je=e=>Ge(e)&&Pe(e.dom),Ge=We(1),$e=We(3),qe=We(9),Xe=We(11),Ye=e=>t=>Ge(t)&&Ue(t)===e,Ke=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Je=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Ze=(e,t)=>e.dom===t.dom,Qe=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},et=e=>Ve(e.dom.ownerDocument),tt=e=>qe(e)?e:et(e),ot=e=>Ve(tt(e).dom.documentElement),nt=e=>Ve(tt(e).dom.defaultView),st=e=>A.from(e.dom.parentNode).map(Ve),rt=e=>A.from(e.dom.parentElement).map(Ve),at=e=>A.from(e.dom.offsetParent).map(Ve),it=e=>H(e.dom.childNodes,Ve),lt=(e,t)=>{const o=e.dom.childNodes;return A.from(o[t]).map(Ve)},ct=e=>lt(e,0),dt=(e,t)=>({element:e,offset:t}),ut=(e,t)=>{const o=it(e);return o.length>0&&t<o.length?dt(o[t],0):dt(e,t)},mt=e=>Xe(e)&&g(e.dom.host),gt=p(Element.prototype.attachShadow)&&p(Node.prototype.getRootNode),pt=x(gt),ht=gt?e=>Ve(e.dom.getRootNode()):tt,ft=e=>mt(e)?e:Ve(tt(e).dom.body),bt=e=>{const t=ht(e);return mt(t)?A.some(t):A.none()},vt=e=>Ve(e.dom.host),yt=e=>{const t=$e(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return bt(Ve(t)).fold((()=>o.body.contains(t)),(n=yt,s=vt,e=>n(s(e))));var n,s},xt=()=>wt(Ve(document)),wt=e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return Ve(t)},St=(e,t,o)=>{if(!(r(o)||d(o)||h(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},kt=(e,t,o)=>{St(e.dom,t,o)},Ct=(e,t)=>{const o=e.dom;le(t,((e,t)=>{St(o,t,e)}))},Ot=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},_t=(e,t)=>A.from(Ot(e,t)),Tt=(e,t)=>{const o=e.dom;return!(!o||!o.hasAttribute)&&o.hasAttribute(t)},Et=(e,t)=>{e.dom.removeAttribute(t)},At=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Be(e)&&e.style.setProperty(t,o)},Mt=(e,t)=>{Be(e)&&e.style.removeProperty(t)},Dt=(e,t,o)=>{const n=e.dom;At(n,t,o)},Bt=(e,t)=>{const o=e.dom;le(t,((e,t)=>{At(o,t,e)}))},Ft=(e,t)=>{const o=e.dom;le(t,((e,t)=>{e.fold((()=>{Mt(o,t)}),(e=>{At(o,t,e)}))}))},It=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||yt(e)?n:Rt(o,t)},Rt=(e,t)=>Be(e)?e.style.getPropertyValue(t):"",Nt=(e,t)=>{const o=e.dom,n=Rt(o,t);return A.from(n).filter((e=>e.length>0))},Vt=e=>{const t={},o=e.dom;if(Be(o))for(let e=0;e<o.style.length;e++){const n=o.style.item(e);t[n]=o.style[n]}return t},zt=(e,t,o)=>{const n=Re(e);return Dt(n,t,o),Nt(n,t).isSome()},Ht=(e,t)=>{const o=e.dom;Mt(o,t),xe(_t(e,"style").map(Me),"")&&Et(e,"style")},Lt=e=>e.dom.offsetWidth,Pt=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=It(o,e);return parseFloat(t)||0}return n},n=(e,t)=>j(t,((t,o)=>{const n=It(e,o),s=void 0===n?0:parseInt(n,10);return isNaN(s)?t:t+s}),0);return{set:(t,o)=>{if(!h(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Be(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const s=n(e,o);return t>s?t-s:0}}},Ut=Pt("height",(e=>{const t=e.dom;return yt(e)?t.getBoundingClientRect().height:t.offsetHeight})),Wt=e=>Ut.get(e),jt=e=>Ut.getOuter(e),Gt=(e,t)=>({left:e,top:t,translate:(o,n)=>Gt(e+o,t+n)}),$t=Gt,qt=(e,t)=>void 0!==e?e:void 0!==t?t:0,Xt=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,s=t.documentElement;if(o===e.dom)return $t(o.offsetLeft,o.offsetTop);const r=qt(null==n?void 0:n.pageYOffset,s.scrollTop),a=qt(null==n?void 0:n.pageXOffset,s.scrollLeft),i=qt(s.clientTop,o.clientTop),l=qt(s.clientLeft,o.clientLeft);return Yt(e).translate(a-l,r-i)},Yt=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?$t(o.offsetLeft,o.offsetTop):yt(e)?(e=>{const t=e.getBoundingClientRect();return $t(t.left,t.top)})(t):$t(0,0)},Kt=Pt("width",(e=>e.dom.offsetWidth)),Jt=e=>Kt.get(e),Zt=e=>Kt.getOuter(e),Qt=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},eo=()=>to(0,0),to=(e,t)=>({major:e,minor:t}),oo={nu:to,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?eo():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return to(n(1),n(2))})(e,o)},unknown:eo},no=(e,t)=>{const o=String(t).toLowerCase();return G(e,(e=>e.search(o)))},so=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,ro=e=>t=>Te(t,e),ao=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>Te(e,"edge/")&&Te(e,"chrome")&&Te(e,"safari")&&Te(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,so],search:e=>Te(e,"chrome")&&!Te(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>Te(e,"msie")||Te(e,"trident")},{name:"Opera",versionRegexes:[so,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:ro("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:ro("firefox")},{name:"Safari",versionRegexes:[so,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(Te(e,"safari")||Te(e,"mobile/"))&&Te(e,"applewebkit")}],io=[{name:"Windows",search:ro("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>Te(e,"iphone")||Te(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:ro("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:ro("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:ro("linux"),versionRegexes:[]},{name:"Solaris",search:ro("sunos"),versionRegexes:[]},{name:"FreeBSD",search:ro("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:ro("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],lo={browsers:x(ao),oses:x(io)},co="Edge",uo="Chromium",mo="Opera",go="Firefox",po="Safari",ho=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(co),isChromium:n(uo),isIE:n("IE"),isOpera:n(mo),isFirefox:n(go),isSafari:n(po)}},fo=()=>ho({current:void 0,version:oo.unknown()}),bo=ho,vo=(x(co),x(uo),x("IE"),x(mo),x(go),x(po),"Windows"),yo="Android",xo="Linux",wo="macOS",So="Solaris",ko="FreeBSD",Co="ChromeOS",Oo=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(vo),isiOS:n("iOS"),isAndroid:n(yo),isMacOS:n(wo),isLinux:n(xo),isSolaris:n(So),isFreeBSD:n(ko),isChromeOS:n(Co)}},_o=()=>Oo({current:void 0,version:oo.unknown()}),To=Oo,Eo=(x(vo),x("iOS"),x(yo),x(xo),x(wo),x(So),x(ko),x(Co),e=>window.matchMedia(e).matches);var Ao;let Mo=Qt((()=>((e,t,o)=>{const n=lo.browsers(),s=lo.oses(),r=t.bind((e=>((e,t)=>re(t.brands,(t=>{const o=t.brand.toLowerCase();return G(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:oo.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>no(e,t).map((e=>{const o=oo.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(fo,bo),a=((e,t)=>no(e,t).map((e=>{const o=oo.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(s,e).fold(_o,To),i=((e,t,o,n)=>{const s=e.isiOS()&&!0===/ipad/i.test(o),r=e.isiOS()&&!s,a=e.isiOS()||e.isAndroid(),i=a||n("(pointer:coarse)"),l=s||!r&&a&&n("(min-device-width:768px)"),c=r||a&&!l,d=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),u=!c&&!l&&!d;return{isiPad:x(s),isiPhone:x(r),isTablet:x(l),isPhone:x(c),isTouch:x(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:x(d),isDesktop:x(u)}})(a,r,e,o);return{browser:r,os:a,deviceType:i}})(navigator.userAgent,A.from(navigator.userAgentData),Eo)));const Do=()=>Mo(),Bo=e=>{const t=Ve((e=>{if(pt()&&g(e.target)){const t=Ve(e.target);if(Ge(t)&&(e=>g(e.dom.shadowRoot))(t)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return oe(t)}}return A.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),s=y(n,o);return((e,t,o,n,s,r,a)=>({target:e,x:t,y:o,stop:n,prevent:s,kill:r,raw:a}))(t,e.clientX,e.clientY,o,n,s,e)},Fo=(e,t,o,n,s)=>{const r=((e,t)=>o=>{e(o)&&t(Bo(o))})(o,n);return e.dom.addEventListener(t,r,s),{unbind:k(Io,e,t,r,s)}},Io=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},Ro=(e,t)=>{st(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},No=(e,t)=>{const o=(e=>A.from(e.dom.nextSibling).map(Ve))(e);o.fold((()=>{st(e).each((e=>{zo(e,t)}))}),(e=>{Ro(e,t)}))},Vo=(e,t)=>{ct(e).fold((()=>{zo(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},zo=(e,t)=>{e.dom.appendChild(t.dom)},Ho=(e,t)=>{L(t,(t=>{zo(e,t)}))},Lo=e=>{e.dom.textContent="",L(it(e),(e=>{Po(e)}))},Po=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Uo=e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return $t(o,n)},Wo=(e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollTo(e,t)},jo=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Go=e=>{const t=void 0===e?window:e,o=t.document,n=Uo(Ve(o));return(e=>{const t=void 0===e?window:e;return Do().browser.isFirefox()?A.none():A.from(t.visualViewport)})(t).fold((()=>{const e=t.document.documentElement,o=e.clientWidth,s=e.clientHeight;return jo(n.left,n.top,o,s)}),(e=>jo(Math.max(e.pageLeft,n.left),Math.max(e.pageTop,n.top),e.width,e.height)))},$o=()=>Ve(document),qo=(e,t)=>e.view(t).fold(x([]),(t=>{const o=e.owner(t),n=qo(e,o);return[t].concat(n)}));var Xo=Object.freeze({__proto__:null,view:e=>{var t;return(e.dom===document?A.none():A.from(null===(t=e.dom.defaultView)||void 0===t?void 0:t.frameElement)).map(Ve)},owner:e=>et(e)});const Yo=e=>{const t=$o(),o=Uo(t),n=((e,t)=>{const o=t.owner(e),n=qo(t,o);return A.some(n)})(e,Xo);return n.fold(k(Xt,e),(t=>{const n=Yt(e),s=W(t,((e,t)=>{const o=Yt(t);return{left:e.left+o.left,top:e.top+o.top}}),{left:0,top:0});return $t(s.left+n.left+o.left,s.top+n.top+o.top)}))},Ko=(e,t,o,n)=>({x:e,y:t,width:o,height:n,right:e+o,bottom:t+n}),Jo=e=>{const t=Xt(e),o=Zt(e),n=jt(e);return Ko(t.left,t.top,o,n)},Zo=e=>{const t=Yo(e),o=Zt(e),n=jt(e);return Ko(t.left,t.top,o,n)},Qo=(e,t)=>{const o=Math.max(e.x,t.x),n=Math.max(e.y,t.y),s=Math.min(e.right,t.right),r=Math.min(e.bottom,t.bottom);return Ko(o,n,s-o,r-n)},en=()=>Go(window);var tn=tinymce.util.Tools.resolve("tinymce.ThemeManager");const on=e=>{const t=t=>t(e),o=x(e),n=()=>s,s={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:E,isError:T,map:t=>sn.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>A.some(e)};return s},nn=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:T,isError:E,map:t,mapError:t=>sn.error(t(e)),bind:t,exists:T,forall:E,getOr:w,or:w,getOrThunk:_,orThunk:_,getOrDie:O(String(e)),each:b,toOptional:A.none};return o},sn={value:on,error:nn,fromOption:(e,t)=>e.fold((()=>nn(t)),on)};var rn;!function(e){e[e.Error=0]="Error",e[e.Value=1]="Value"}(rn||(rn={}));const an=(e,t,o)=>e.stype===rn.Error?t(e.serror):o(e.svalue),ln=e=>({stype:rn.Value,svalue:e}),cn=e=>({stype:rn.Error,serror:e}),dn=ln,un=cn,mn=an,gn=(e,t,o,n)=>({tag:"field",key:e,newKey:t,presence:o,prop:n}),pn=(e,t,o)=>{switch(e.tag){case"field":return t(e.key,e.newKey,e.presence,e.prop);case"custom":return o(e.newKey,e.instantiator)}},hn=e=>(...t)=>{if(0===t.length)throw new Error("Can't merge zero objects");const o={};for(let n=0;n<t.length;n++){const s=t[n];for(const t in s)ve(s,t)&&(o[t]=e(o[t],s[t]))}return o},fn=hn(((e,t)=>i(e)&&i(t)?fn(e,t):t)),bn=hn(((e,t)=>t)),vn=e=>({tag:"defaultedThunk",process:e}),yn=e=>vn(x(e)),xn=e=>({tag:"mergeWithThunk",process:e}),wn=e=>{const t=(e=>{const t=[],o=[];return L(e,(e=>{an(e,(e=>o.push(e)),(e=>t.push(e)))})),{values:t,errors:o}})(e);return t.errors.length>0?(o=t.errors,y(un,q)(o)):dn(t.values);var o},Sn=e=>a(e)&&ae(e).length>100?" removed due to size":JSON.stringify(e,null,2),kn=(e,t)=>un([{path:e,getErrorInfo:t}]),Cn=e=>({extract:(t,o)=>((e,t)=>e.stype===rn.Error?t(e.serror):e)(e(o),(e=>((e,t)=>kn(e,x(t)))(t,e))),toString:x("val")}),On=Cn(dn),_n=(e,t,o,n)=>n(be(e,t).getOrThunk((()=>o(e)))),Tn=(e,t,o,n,s)=>{const r=e=>s.extract(t.concat([n]),e),a=e=>e.fold((()=>dn(A.none())),(e=>((e,t)=>e.stype===rn.Value?{stype:rn.Value,svalue:t(e.svalue)}:e)(s.extract(t.concat([n]),e),A.some)));switch(e.tag){case"required":return((e,t,o,n)=>be(t,o).fold((()=>((e,t,o)=>kn(e,(()=>'Could not find valid *required* value for "'+t+'" in '+Sn(o))))(e,o,t)),n))(t,o,n,r);case"defaultedThunk":return _n(o,n,e.process,r);case"option":return((e,t,o)=>o(be(e,t)))(o,n,a);case"defaultedOptionThunk":return((e,t,o,n)=>n(be(e,t).map((t=>!0===t?o(e):t))))(o,n,e.process,a);case"mergeWithThunk":return _n(o,n,x({}),(t=>{const n=fn(e.process(o),t);return r(n)}))}},En=e=>({extract:(t,o)=>e().extract(t,o),toString:()=>e().toString()}),An=e=>ae(ge(e,g)),Mn=e=>{const t=Dn(e),o=W(e,((e,t)=>pn(t,(t=>fn(e,{[t]:!0})),x(e))),{});return{extract:(e,n)=>{const s=d(n)?[]:An(n),r=U(s,(e=>!ye(o,e)));return 0===r.length?t.extract(e,n):((e,t)=>kn(e,(()=>"There are unsupported fields: ["+t.join(", ")+"] specified")))(e,r)},toString:t.toString}},Dn=e=>({extract:(t,o)=>((e,t,o)=>{const n={},s=[];for(const r of o)pn(r,((o,r,a,i)=>{const l=Tn(a,e,t,o,i);mn(l,(e=>{s.push(...e)}),(e=>{n[r]=e}))}),((e,o)=>{n[e]=o(t)}));return s.length>0?un(s):dn(n)})(t,o,e),toString:()=>{const t=H(e,(e=>pn(e,((e,t,o,n)=>e+" -> "+n.toString()),((e,t)=>"state("+e+")"))));return"obj{\n"+t.join("\n")+"}"}}),Bn=e=>({extract:(t,o)=>{const n=H(o,((o,n)=>e.extract(t.concat(["["+n+"]"]),o)));return wn(n)},toString:()=>"array("+e.toString()+")"}),Fn=(e,t)=>{const o=void 0!==t?t:w;return{extract:(t,n)=>{const s=[];for(const r of e){const e=r.extract(t,n);if(e.stype===rn.Value)return{stype:rn.Value,svalue:o(e.svalue)};s.push(e)}return wn(s)},toString:()=>"oneOf("+H(e,(e=>e.toString())).join(", ")+")"}},In=(e,t)=>({extract:(o,n)=>{const s=ae(n),r=((t,o)=>Bn(Cn(e)).extract(t,o))(o,s);return((e,t)=>e.stype===rn.Value?t(e.svalue):e)(r,(e=>{const s=H(e,(e=>gn(e,e,{tag:"required",process:{}},t)));return Dn(s).extract(o,n)}))},toString:()=>"setOf("+t.toString()+")"}),Rn=y(Bn,Dn),Nn=x(On),Vn=(e,t)=>Cn((o=>{const n=typeof o;return e(o)?dn(o):un(`Expected type: ${t} but got: ${n}`)})),zn=Vn(h,"number"),Hn=Vn(r,"string"),Ln=Vn(d,"boolean"),Pn=Vn(p,"function"),Un=e=>{if(Object(e)!==e)return!0;switch({}.toString.call(e).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(e).every((t=>Un(e[t])));default:return!1}},Wn=Cn((e=>Un(e)?dn(e):un("Expected value to be acceptable for sending via postMessage"))),jn=(e,t)=>({extract:(o,n)=>be(n,e).fold((()=>((e,t)=>kn(e,(()=>'Choice schema did not contain choice key: "'+t+'"')))(o,e)),(e=>((e,t,o,n)=>be(o,n).fold((()=>((e,t,o)=>kn(e,(()=>'The chosen schema: "'+o+'" did not exist in branches: '+Sn(t))))(e,o,n)),(o=>o.extract(e.concat(["branch: "+n]),t))))(o,n,t,e))),toString:()=>"chooseOn("+e+"). Possible values: "+ae(t)}),Gn=e=>Cn((t=>e(t).fold(un,dn))),$n=(e,t)=>In((t=>e(t).fold(cn,ln)),t),qn=(e,t,o)=>{return n=((e,t,o)=>((e,t)=>e.stype===rn.Error?{stype:rn.Error,serror:t(e.serror)}:e)(t.extract([e],o),(e=>({input:o,errors:e}))))(e,t,o),an(n,sn.error,sn.value);var n},Xn=e=>e.fold((e=>{throw new Error(Kn(e))}),w),Yn=(e,t,o)=>Xn(qn(e,t,o)),Kn=e=>"Errors: \n"+(e=>{const t=e.length>10?e.slice(0,10).concat([{path:[],getErrorInfo:x("... (only showing first ten failures)")}]):e;return H(t,(e=>"Failed path: ("+e.path.join(" > ")+")\n"+e.getErrorInfo()))})(e.errors).join("\n")+"\n\nInput object: "+Sn(e.input),Jn=(e,t)=>jn(e,ce(t,Dn)),Zn=(e,t)=>((e,t)=>{const o=Qt(t);return{extract:(e,t)=>o().extract(e,t),toString:()=>o().toString()}})(0,t),Qn=gn,es=(e,t)=>({tag:"custom",newKey:e,instantiator:t}),ts=e=>Gn((t=>R(e,t)?sn.value(t):sn.error(`Unsupported value: "${t}", choose one of "${e.join(", ")}".`))),os=e=>Qn(e,e,{tag:"required",process:{}},Nn()),ns=(e,t)=>Qn(e,e,{tag:"required",process:{}},t),ss=e=>ns(e,zn),rs=e=>ns(e,Hn),as=(e,t)=>Qn(e,e,{tag:"required",process:{}},ts(t)),is=e=>ns(e,Pn),ls=(e,t)=>Qn(e,e,{tag:"required",process:{}},Dn(t)),cs=(e,t)=>Qn(e,e,{tag:"required",process:{}},Rn(t)),ds=(e,t)=>Qn(e,e,{tag:"required",process:{}},Bn(t)),us=e=>Qn(e,e,{tag:"option",process:{}},Nn()),ms=(e,t)=>Qn(e,e,{tag:"option",process:{}},t),gs=e=>ms(e,zn),ps=e=>ms(e,Hn),hs=(e,t)=>ms(e,ts(t)),fs=e=>ms(e,Pn),bs=(e,t)=>ms(e,Bn(t)),vs=(e,t)=>ms(e,Dn(t)),ys=(e,t)=>Qn(e,e,yn(t),Nn()),xs=(e,t,o)=>Qn(e,e,yn(t),o),ws=(e,t)=>xs(e,t,zn),Ss=(e,t)=>xs(e,t,Hn),ks=(e,t,o)=>xs(e,t,ts(o)),Cs=(e,t)=>xs(e,t,Ln),Os=(e,t)=>xs(e,t,Pn),_s=(e,t,o)=>xs(e,t,Bn(o)),Ts=(e,t,o)=>xs(e,t,Dn(o)),Es=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},As=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return L(e,((n,s)=>{const r=ae(n);if(1!==r.length)throw new Error("one and only one name per case");const a=r[0],i=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(i))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+i.length+" ("+i+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,o)},match:e=>{const n=ae(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!Y(t,(e=>R(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o};As([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const Ms=(e,t)=>((e,t)=>({[e]:t}))(e,t),Ds=e=>(e=>{const t={};return L(e,(e=>{t[e.key]=e.value})),t})(e),Bs=e=>p(e)?e:T,Fs=(e,t,o)=>{let n=e.dom;const s=Bs(o);for(;n.parentNode;){n=n.parentNode;const e=Ve(n),o=t(e);if(o.isSome())return o;if(s(e))break}return A.none()},Is=(e,t,o)=>{const n=t(e),s=Bs(o);return n.orThunk((()=>s(e)?A.none():Fs(e,t,s)))},Rs=(e,t)=>Ze(e.element,t.event.target),Ns={can:E,abort:T,run:b},Vs=e=>{if(!ye(e,"can")&&!ye(e,"abort")&&!ye(e,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(e,null,2)+" does not have can, abort, or run!");return{...Ns,...e}},zs=x,Hs=zs("touchstart"),Ls=zs("touchmove"),Ps=zs("touchend"),Us=zs("touchcancel"),Ws=zs("mousedown"),js=zs("mousemove"),Gs=zs("mouseout"),$s=zs("mouseup"),qs=zs("mouseover"),Xs=zs("focusin"),Ys=zs("focusout"),Ks=zs("keydown"),Js=zs("keyup"),Zs=zs("input"),Qs=zs("change"),er=zs("click"),tr=zs("transitioncancel"),or=zs("transitionend"),nr=zs("transitionstart"),sr=zs("selectstart"),rr=e=>x("alloy."+e),ar={tap:rr("tap")},ir=rr("focus"),lr=rr("blur.post"),cr=rr("paste.post"),dr=rr("receive"),ur=rr("execute"),mr=rr("focus.item"),gr=ar.tap,pr=rr("longpress"),hr=rr("sandbox.close"),fr=rr("typeahead.cancel"),br=rr("system.init"),vr=rr("system.touchmove"),yr=rr("system.touchend"),xr=rr("system.scroll"),wr=rr("system.resize"),Sr=rr("system.attached"),kr=rr("system.detached"),Cr=rr("system.dismissRequested"),Or=rr("system.repositionRequested"),_r=rr("focusmanager.shifted"),Tr=rr("slotcontainer.visibility"),Er=rr("system.external.element.scroll"),Ar=rr("change.tab"),Mr=rr("dismiss.tab"),Dr=rr("highlight"),Br=rr("dehighlight"),Fr=(e,t)=>{Vr(e,e.element,t,{})},Ir=(e,t,o)=>{Vr(e,e.element,t,o)},Rr=e=>{Fr(e,ur())},Nr=(e,t,o)=>{Vr(e,t,o,{})},Vr=(e,t,o,n)=>{const s={target:t,...n};e.getSystem().triggerEvent(o,t,s)},zr=(e,t,o,n)=>{e.getSystem().triggerEvent(o,t,n.event)},Hr=e=>Ds(e),Lr=(e,t)=>({key:e,value:Vs({abort:t})}),Pr=e=>({key:e,value:Vs({run:(e,t)=>{t.event.prevent()}})}),Ur=(e,t)=>({key:e,value:Vs({run:t})}),Wr=(e,t,o)=>({key:e,value:Vs({run:(e,n)=>{t.apply(void 0,[e,n].concat(o))}})}),jr=e=>t=>({key:e,value:Vs({run:(e,o)=>{Rs(e,o)&&t(e,o)}})}),Gr=(e,t,o)=>((e,t)=>Ur(e,((o,n)=>{o.getSystem().getByUid(t).each((t=>{zr(t,t.element,e,n)}))})))(e,t.partUids[o]),$r=(e,t)=>Ur(e,((e,o)=>{const n=o.event,s=e.getSystem().getByDom(n.target).getOrThunk((()=>Is(n.target,(t=>e.getSystem().getByDom(t).toOptional()),T).getOr(e)));t(e,s,o)})),qr=e=>Ur(e,((e,t)=>{t.cut()})),Xr=e=>Ur(e,((e,t)=>{t.stop()})),Yr=(e,t)=>jr(e)(t),Kr=jr(Sr()),Jr=jr(kr()),Zr=jr(br()),Qr=(ra=ur(),e=>Ur(ra,e)),ea=e=>e.dom.innerHTML,ta=(e,t)=>{const o=et(e).dom,n=Ve(o.createDocumentFragment()),s=((e,t)=>{const o=(t||document).createElement("div");return o.innerHTML=e,it(Ve(o))})(t,o);Ho(n,s),Lo(e),zo(e,n)},oa=e=>mt(e)?"#shadow-root":(e=>{const t=Re("div"),o=Ve(e.dom.cloneNode(!0));return zo(t,o),ea(t)})((e=>((e,t)=>Ve(e.dom.cloneNode(!1)))(e))(e)),na=e=>oa(e),sa=Hr([((e,t)=>({key:e,value:Vs({can:(e,t)=>{const o=t.event,n=o.originator,s=o.target;return!((e,t,o)=>Ze(t,e.element)&&!Ze(t,o))(e,n,s)||(console.warn(ir()+" did not get interpreted by the desired target. \nOriginator: "+na(n)+"\nTarget: "+na(s)+"\nCheck the "+ir()+" event handlers"),!1)}})}))(ir())]);var ra,aa=Object.freeze({__proto__:null,events:sa});let ia=0;const la=e=>{const t=(new Date).getTime(),o=Math.floor(1e9*Math.random());return ia++,e+"_"+o+ia+String(t)},ca=x("alloy-id-"),da=x("data-alloy-id"),ua=ca(),ma=da(),ga=(e,t)=>{Object.defineProperty(e.dom,ma,{value:t,writable:!0})},pa=e=>{const t=Ge(e)?e.dom[ma]:null;return A.from(t)},ha=e=>la(e),fa=w,ba=e=>{const t=t=>`The component must be in a context to execute: ${t}`+(e?"\n"+na(e().element)+" is not in context.":""),o=e=>()=>{throw new Error(t(e))},n=e=>()=>{console.warn(t(e))};return{debugInfo:x("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),build:o("build"),buildOrPatch:o("buildOrPatch"),addToWorld:o("addToWorld"),removeFromWorld:o("removeFromWorld"),addToGui:o("addToGui"),removeFromGui:o("removeFromGui"),getByUid:o("getByUid"),getByDom:o("getByDom"),isConnected:T}},va=ba(),ya=e=>H(e,(e=>Ae(e,"/*")?e.substring(0,e.length-2):e)),xa=(e,t)=>{const o=e.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ya(r)}),e},wa=la("alloy-premade"),Sa=e=>(Object.defineProperty(e.element.dom,wa,{value:e.uid,writable:!0}),Ms(wa,e)),ka=e=>be(e,wa),Ca=e=>((e,t)=>{const o=t.toString(),n=o.indexOf(")")+1,s=o.indexOf("("),r=o.substring(s+1,n-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:"OVERRIDE",parameters:ya(r.slice(1))}),e})(((t,...o)=>e(t.getApis(),t,...o)),e),Oa={init:()=>_a({readState:x("No State required")})},_a=e=>e,Ta=(e,t)=>{const o={};return le(e,((e,n)=>{le(e,((e,s)=>{const r=be(o,s).getOr([]);o[s]=r.concat([t(n,e)])}))})),o},Ea=e=>({classes:u(e.classes)?[]:e.classes,attributes:u(e.attributes)?{}:e.attributes,styles:u(e.styles)?{}:e.styles}),Aa=e=>e.cHandler,Ma=(e,t)=>({name:e,handler:t}),Da=(e,t)=>{const o={};return L(e,(e=>{o[e.name()]=e.handlers(t)})),o},Ba=(e,t,o)=>{const n=t[o];return n?((e,t,o,n)=>{try{const s=ee(o,((o,s)=>{const r=o[t],a=s[t],i=n.indexOf(r),l=n.indexOf(a);if(-1===i)throw new Error("The ordering for "+e+" does not have an entry for "+r+".\nOrder specified: "+JSON.stringify(n,null,2));if(-1===l)throw new Error("The ordering for "+e+" does not have an entry for "+a+".\nOrder specified: "+JSON.stringify(n,null,2));return i<l?-1:l<i?1:0}));return sn.value(s)}catch(e){return sn.error([e])}})("Event: "+o,"name",e,n).map((e=>(e=>{const t=((e,t)=>(...t)=>j(e,((e,o)=>e&&(e=>e.can)(o).apply(void 0,t)),!0))(e),o=((e,t)=>(...t)=>j(e,((e,o)=>e||(e=>e.abort)(o).apply(void 0,t)),!1))(e);return{can:t,abort:o,run:(...t)=>{L(e,(e=>{e.run.apply(void 0,t)}))}}})(H(e,(e=>e.handler))))):((e,t)=>sn.error(["The event ("+e+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(H(t,(e=>e.name)),null,2)]))(o,e)},Fa=(e,t)=>((e,t)=>{const o=(e=>{const t=[],o=[];return L(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{o.push(e)}))})),{errors:t,values:o}})(e);return o.errors.length>0?(n=o.errors,sn.error(q(n))):((e,t)=>0===e.length?sn.value(t):sn.value(fn(t,bn.apply(void 0,e))))(o.values,t);var n})(pe(e,((e,o)=>(1===e.length?sn.value(e[0].handler):Ba(e,t,o)).map((n=>{const s=(e=>{const t=(e=>p(e)?{can:E,abort:T,run:e}:e)(e);return(e,o,...n)=>{const s=[e,o].concat(n);t.abort.apply(void 0,s)?o.stop():t.can.apply(void 0,s)&&t.run.apply(void 0,s)}})(n),r=e.length>1?U(t[o],(t=>N(e,(e=>e.name===t)))).join(" > "):e[0].name;return Ms(o,((e,t)=>({handler:e,purpose:t}))(s,r))})))),{}),Ia="alloy.base.behaviour",Ra=Dn([Qn("dom","dom",{tag:"required",process:{}},Dn([os("tag"),ys("styles",{}),ys("classes",[]),ys("attributes",{}),us("value"),us("innerHtml")])),os("components"),os("uid"),ys("events",{}),ys("apis",{}),Qn("eventOrder","eventOrder",(ii={[ur()]:["disabling",Ia,"toggling","typeaheadevents"],[ir()]:[Ia,"focusing","keying"],[br()]:[Ia,"disabling","toggling","representing"],[Zs()]:[Ia,"representing","streaming","invalidating"],[kr()]:[Ia,"representing","item-events","tooltipping"],[Ws()]:["focusing",Ia,"item-type-events"],[Hs()]:["focusing",Ia,"item-type-events"],[qs()]:["item-type-events","tooltipping"],[dr()]:["receiving","reflecting","tooltipping"]},xn(x(ii))),Nn()),us("domModification")]),Na=e=>e.events,Va=(e,t)=>{const o=Ot(e,t);return void 0===o||""===o?[]:o.split(" ")},za=e=>void 0!==e.dom.classList,Ha=e=>Va(e,"class"),La=(e,t)=>{za(e)?e.dom.classList.add(t):((e,t)=>{((e,t,o)=>{const n=Va(e,t).concat([o]);kt(e,t,n.join(" "))})(e,"class",t)})(e,t)},Pa=(e,t)=>{za(e)?e.dom.classList.remove(t):((e,t)=>{((e,t,o)=>{const n=U(Va(e,t),(e=>e!==o));n.length>0?kt(e,t,n.join(" ")):Et(e,t)})(e,"class",t)})(e,t),(e=>{0===(za(e)?e.dom.classList:Ha(e)).length&&Et(e,"class")})(e)},Ua=(e,t)=>za(e)&&e.dom.classList.contains(t),Wa=(e,t)=>{L(t,(t=>{La(e,t)}))},ja=(e,t)=>{L(t,(t=>{Pa(e,t)}))},Ga=(e,t)=>Y(t,(t=>Ua(e,t))),$a=e=>e.dom.value,qa=(e,t)=>{if(void 0===t)throw new Error("Value.set was undefined");e.dom.value=t},Xa=(e,t,o)=>{o.fold((()=>zo(e,t)),(e=>{Ze(e,t)||(Ro(e,t),Po(e))}))},Ya=(e,t,o)=>{const n=H(t,o),s=it(e);return L(s.slice(n.length),Po),n},Ka=(e,t,o,n)=>{const s=lt(e,t),r=n(o,s),a=((e,t,o)=>lt(e,t).map((e=>{if(o.exists((t=>!Ze(t,e)))){const t=o.map(Ue).getOr("span"),n=Re(t);return Ro(e,n),n}return e})))(e,t,s);return Xa(e,r.element,a),r},Ja=(e,t)=>{const o=ae(e),n=ae(t),s=J(n,o),r=((e,o)=>{const n={},s={};return me(e,((e,o)=>!ve(t,o)||e!==t[o]),ue(n),ue(s)),{t:n,f:s}})(e).t;return{toRemove:s,toSet:r}},Za=(e,t)=>{const{class:o,style:n,...s}=(e=>j(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))(t),{toSet:r,toRemove:a}=Ja(e.attributes,s),i=Vt(t),{toSet:l,toRemove:c}=Ja(e.styles,i),d=(e=>za(e)?(e=>{const t=e.dom.classList,o=new Array(t.length);for(let e=0;e<t.length;e++){const n=t.item(e);null!==n&&(o[e]=n)}return o})(e):Ha(e))(t),u=J(d,e.classes),m=J(e.classes,d);return L(a,(e=>Et(t,e))),Ct(t,r),Wa(t,m),ja(t,u),L(c,(e=>Ht(t,e))),Bt(t,l),e.innerHtml.fold((()=>{const o=e.domChildren;((e,t)=>{Ya(e,t,((t,o)=>{const n=lt(e,o);return Xa(e,t,n),t}))})(t,o)}),(e=>{ta(t,e)})),(()=>{const o=t,n=e.value.getOrUndefined();n!==$a(o)&&qa(o,null!=n?n:"")})(),t},Qa=e=>{const t=(e=>{const t=be(e,"behaviours").getOr({});return X(ae(t),(e=>{const o=t[e];return g(o)?[o.me]:[]}))})(e);return((e,t)=>((e,t)=>{const o=H(t,(e=>vs(e.name(),[os("config"),ys("state",Oa)]))),n=qn("component.behaviours",Dn(o),e.behaviours).fold((t=>{throw new Error(Kn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))}),w);return{list:t,data:ce(n,(e=>{const t=e.map((e=>({config:e.config,state:e.state.init(e.config)})));return x(t)}))}})(e,t))(e,t)},ei=(e,t)=>{const o=()=>m,n=Es(va),s=Xn((e=>qn("custom.definition",Ra,e))(e)),r=Qa(e),a=(e=>e.list)(r),i=(e=>e.data)(r),l=((e,t,o)=>{const n={...(s=e).dom,uid:s.uid,domChildren:H(s.components,(e=>e.element))};var s;const r=(e=>e.domModification.fold((()=>Ea({})),Ea))(e),a={"alloy.base.modification":r},i=t.length>0?((e,t,o,n)=>{const s={...t};L(o,(t=>{s[t.name()]=t.exhibit(e,n)}));const r=Ta(s,((e,t)=>({name:e,modification:t}))),a=e=>W(e,((e,t)=>({...t.modification,...e})),{}),i=W(r.classes,((e,t)=>t.modification.concat(e)),[]),l=a(r.attributes),c=a(r.styles);return Ea({classes:i,attributes:l,styles:c})})(o,a,t,n):r;return l=n,c=i,{...l,attributes:{...l.attributes,...c.attributes},styles:{...l.styles,...c.styles},classes:l.classes.concat(c.classes)};var l,c})(s,a,i),c=((e,t)=>{const o=t.filter((t=>Ue(t)===e.tag&&!(e=>e.innerHtml.isSome()&&e.domChildren.length>0)(e)&&!(e=>ve(e.dom,wa))(t))).bind((t=>((e,t)=>{try{const o=Za(e,t);return A.some(o)}catch(e){return A.none()}})(e,t))).getOrThunk((()=>(e=>{const t=Re(e.tag);Ct(t,e.attributes),Wa(t,e.classes),Bt(t,e.styles),e.innerHtml.each((e=>ta(t,e)));const o=e.domChildren;return Ho(t,o),e.value.each((e=>{qa(t,e)})),t})(e)));return ga(o,e.uid),o})(l,t),d=((e,t,o)=>{const n={"alloy.base.behaviour":Na(e)};return((e,t,o,n)=>{const s=((e,t,o)=>{const n={...o,...Da(t,e)};return Ta(n,Ma)})(e,o,n);return Fa(s,t)})(o,e.eventOrder,t,n).getOrDie()})(s,a,i),u=Es(s.components),m={uid:e.uid,getSystem:n.get,config:t=>{const o=i;return(p(o[t.name()])?o[t.name()]:()=>{throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:e=>p(i[e.name()]),spec:e,readState:e=>i[e]().map((e=>e.state.readState())).getOr("not enabled"),getApis:()=>s.apis,connect:e=>{n.set(e)},disconnect:()=>{n.set(ba(o))},element:c,syncComponents:()=>{const e=it(c),t=X(e,(e=>n.get().getByDom(e).fold((()=>[]),Q)));u.set(t)},components:u.get,events:d};return m},ti=e=>{const t=Ne(e);return oi({element:t})},oi=e=>{const t=Yn("external.component",Mn([os("element"),us("uid")]),e),o=Es(ba()),n=t.uid.getOrThunk((()=>ha("external")));ga(t.element,n);const s={uid:n,getSystem:o.get,config:A.none,hasConfigured:T,connect:e=>{o.set(e)},disconnect:()=>{o.set(ba((()=>s)))},getApis:()=>({}),element:t.element,spec:e,readState:x("No state"),syncComponents:b,components:x([]),events:{}};return Sa(s)},ni=ha,si=(e,t)=>ka(e).getOrThunk((()=>((e,t)=>{const{events:o,...n}=fa(e),s=((e,t)=>{const o=be(e,"components").getOr([]);return t.fold((()=>H(o,ri)),(e=>H(o,((t,o)=>si(t,lt(e,o))))))})(n,t),r={...n,events:{...aa,...o},components:s};return sn.value(ei(r,t))})((e=>ve(e,"uid"))(e)?e:{uid:ni(""),...e},t).getOrDie())),ri=e=>si(e,A.none()),ai=Sa;var ii,li=(e,t,o,n,s)=>e(o,n)?A.some(o):p(s)&&s(o)?A.none():t(o,n,s);const ci=(e,t,o)=>{let n=e.dom;const s=p(o)?o:T;for(;n.parentNode;){n=n.parentNode;const e=Ve(n);if(t(e))return A.some(e);if(s(e))break}return A.none()},di=(e,t,o)=>li(((e,t)=>t(e)),ci,e,t,o),ui=(e,t,o)=>di(e,t,o).isSome(),mi=(e,t,o)=>ci(e,(e=>Ke(e,t)),o),gi=(e,t)=>((e,o)=>G(e.dom.childNodes,(e=>{return o=Ve(e),Ke(o,t);var o})).map(Ve))(e),pi=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?A.none():A.from(o.querySelector(e)).map(Ve)})(t,e),hi=(e,t,o)=>li(((e,t)=>Ke(e,t)),mi,e,t,o),fi="aria-controls",bi=()=>{const e=la(fi);return{id:e,link:t=>{kt(t,fi,e)},unlink:e=>{Et(e,fi)}}},vi=(e,t)=>ui(t,(t=>Ze(t,e.element)),T)||((e,t)=>(e=>di(e,(e=>{if(!Ge(e))return!1;const t=Ot(e,"id");return void 0!==t&&t.indexOf(fi)>-1})).bind((e=>{const t=Ot(e,"id"),o=ht(e);return pi(o,`[${fi}="${t}"]`)})))(t).exists((t=>vi(e,t))))(e,t);var yi;!function(e){e[e.STOP=0]="STOP",e[e.NORMAL=1]="NORMAL",e[e.LOGGING=2]="LOGGING"}(yi||(yi={}));const xi=Es({}),wi=["alloy/data/Fields","alloy/debugging/Debugging"],Si=(e,t,o)=>((e,t,o)=>{switch(be(xi.get(),e).orThunk((()=>{const t=ae(xi.get());return re(t,(t=>e.indexOf(t)>-1?A.some(xi.get()[t]):A.none()))})).getOr(yi.NORMAL)){case yi.NORMAL:return o(ki());case yi.LOGGING:{const n=((e,t)=>{const o=[],n=(new Date).getTime();return{logEventCut:(e,t,n)=>{o.push({outcome:"cut",target:t,purpose:n})},logEventStopped:(e,t,n)=>{o.push({outcome:"stopped",target:t,purpose:n})},logNoParent:(e,t,n)=>{o.push({outcome:"no-parent",target:t,purpose:n})},logEventNoHandlers:(e,t)=>{o.push({outcome:"no-handlers-left",target:t})},logEventResponse:(e,t,n)=>{o.push({outcome:"response",purpose:n,target:t})},write:()=>{const s=(new Date).getTime();R(["mousemove","mouseover","mouseout",br()],e)||console.log(e,{event:e,time:s-n,target:t.dom,sequence:H(o,(e=>R(["cut","stopped","response"],e.outcome)?"{"+e.purpose+"} "+e.outcome+" at ("+na(e.target)+")":e.outcome))})}}})(e,t),s=o(n);return n.write(),s}case yi.STOP:return!0}})(e,t,o),ki=x({logEventCut:b,logEventStopped:b,logNoParent:b,logEventNoHandlers:b,logEventResponse:b,write:b}),Ci=x([os("menu"),os("selectedMenu")]),Oi=x([os("item"),os("selectedItem")]);x(Dn(Oi().concat(Ci())));const _i=x(Dn(Oi())),Ti=ls("initSize",[os("numColumns"),os("numRows")]),Ei=()=>ls("markers",[os("backgroundMenu")].concat(Ci()).concat(Oi())),Ai=e=>ls("markers",H(e,os)),Mi=(e,t,o)=>((()=>{const e=new Error;if(void 0!==e.stack){const t=e.stack.split("\n");G(t,(e=>e.indexOf("alloy")>0&&!N(wi,(t=>e.indexOf(t)>-1)))).getOr("unknown")}})(),Qn(t,t,o,Gn((e=>sn.value(((...t)=>e.apply(void 0,t))))))),Di=e=>Mi(0,e,yn(b)),Bi=e=>Mi(0,e,yn(A.none)),Fi=e=>Mi(0,e,{tag:"required",process:{}}),Ii=e=>Mi(0,e,{tag:"required",process:{}}),Ri=(e,t)=>es(e,x(t)),Ni=e=>es(e,w),Vi=x(Ti),zi=(e,t,o,n,s,r,a,i=!1)=>({x:e,y:t,bubble:o,direction:n,placement:s,restriction:r,label:`${a}-${s}`,alwaysFit:i}),Hi=As([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Li=Hi.southeast,Pi=Hi.southwest,Ui=Hi.northeast,Wi=Hi.northwest,ji=Hi.south,Gi=Hi.north,$i=Hi.east,qi=Hi.west,Xi=(e,t,o,n)=>{const s=e+t;return s>n?o:s<o?n:s},Yi=(e,t,o)=>Math.min(Math.max(e,t),o),Ki=(e,t)=>Z(["left","right","top","bottom"],(o=>be(t,o).map((t=>((e,t)=>{switch(t){case 1:return e.x;case 0:return e.x+e.width;case 2:return e.y;case 3:return e.y+e.height}})(e,t))))),Ji="layout",Zi=e=>e.x,Qi=(e,t)=>e.x+e.width/2-t.width/2,el=(e,t)=>e.x+e.width-t.width,tl=(e,t)=>e.y-t.height,ol=e=>e.y+e.height,nl=(e,t)=>e.y+e.height/2-t.height/2,sl=(e,t,o)=>zi(Zi(e),ol(e),o.southeast(),Li(),"southeast",Ki(e,{left:1,top:3}),Ji),rl=(e,t,o)=>zi(el(e,t),ol(e),o.southwest(),Pi(),"southwest",Ki(e,{right:0,top:3}),Ji),al=(e,t,o)=>zi(Zi(e),tl(e,t),o.northeast(),Ui(),"northeast",Ki(e,{left:1,bottom:2}),Ji),il=(e,t,o)=>zi(el(e,t),tl(e,t),o.northwest(),Wi(),"northwest",Ki(e,{right:0,bottom:2}),Ji),ll=(e,t,o)=>zi(Qi(e,t),tl(e,t),o.north(),Gi(),"north",Ki(e,{bottom:2}),Ji),cl=(e,t,o)=>zi(Qi(e,t),ol(e),o.south(),ji(),"south",Ki(e,{top:3}),Ji),dl=(e,t,o)=>zi((e=>e.x+e.width)(e),nl(e,t),o.east(),$i(),"east",Ki(e,{left:0}),Ji),ul=(e,t,o)=>zi(((e,t)=>e.x-t.width)(e,t),nl(e,t),o.west(),qi(),"west",Ki(e,{right:1}),Ji),ml=()=>[sl,rl,al,il,cl,ll,dl,ul],gl=()=>[rl,sl,il,al,cl,ll,dl,ul],pl=()=>[al,il,sl,rl,ll,cl],hl=()=>[il,al,rl,sl,ll,cl],fl=()=>[sl,rl,al,il,cl,ll],bl=()=>[rl,sl,il,al,cl,ll];var vl=Object.freeze({__proto__:null,events:e=>Hr([Ur(dr(),((t,o)=>{const n=e.channels,s=ae(n),r=o,a=((e,t)=>t.universal?e:U(e,(e=>R(t.channels,e))))(s,r);L(a,(e=>{const o=n[e],s=o.schema,a=Yn("channel["+e+"] data\nReceiver: "+na(t.element),s,r.data);o.onReceive(t,a)}))}))])}),yl=[ns("channels",$n(sn.value,Mn([Fi("onReceive"),ys("schema",Nn())])))];const xl=(e,t,o)=>Zr(((n,s)=>{o(n,e,t)})),wl=e=>({key:e,value:void 0}),Sl=(e,t,o,n,s,r,a)=>{const i=e=>ye(e,o)?e[o]():A.none(),l=ce(s,((e,t)=>((e,t,o)=>((e,t,o)=>{const n=o.toString(),s=n.indexOf(")")+1,r=n.indexOf("("),a=n.substring(r+1,s-1).split(/,\s*/);return e.toFunctionAnnotation=()=>({name:t,parameters:ya(a.slice(0,1).concat(a.slice(3)))}),e})(((n,...s)=>{const r=[n].concat(s);return n.config({name:x(e)}).fold((()=>{throw new Error("We could not find any behaviour configuration for: "+e+". Using API: "+o)}),(e=>{const o=Array.prototype.slice.call(r,1);return t.apply(void 0,[n,e.config,e.state].concat(o))}))}),o,t))(o,e,t))),c={...ce(r,((e,t)=>xa(e,t))),...l,revoke:k(wl,o),config:t=>{const n=Yn(o+"-config",e,t);return{key:o,value:{config:n,me:c,configAsRaw:Qt((()=>Yn(o+"-config",e,t))),initialConfig:t,state:a}}},schema:x(t),exhibit:(e,t)=>Se(i(e),be(n,"exhibit"),((e,o)=>o(t,e.config,e.state))).getOrThunk((()=>Ea({}))),name:x(o),handlers:e=>i(e).map((e=>be(n,"events").getOr((()=>({})))(e.config,e.state))).getOr({})};return c},kl=e=>Ds(e),Cl=Mn([os("fields"),os("name"),ys("active",{}),ys("apis",{}),ys("state",Oa),ys("extra",{})]),Ol=e=>{const t=Yn("Creating behaviour: "+e.name,Cl,e);return((e,t,o,n,s,r)=>{const a=Mn(e),i=vs(t,[("config",l=e,ms("config",Mn(l)))]);var l;return Sl(a,i,t,o,n,s,r)})(t.fields,t.name,t.active,t.apis,t.extra,t.state)},_l=Mn([os("branchKey"),os("branches"),os("name"),ys("active",{}),ys("apis",{}),ys("state",Oa),ys("extra",{})]),Tl=e=>{const t=Yn("Creating behaviour: "+e.name,_l,e);return((e,t,o,n,s,r)=>{const a=e,i=vs(t,[ms("config",e)]);return Sl(a,i,t,o,n,s,r)})(Jn(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)},El=x(void 0),Al=Ol({fields:yl,name:"receiving",active:vl});var Ml=Object.freeze({__proto__:null,exhibit:(e,t)=>Ea({classes:[],styles:t.useFixed()?{}:{position:"relative"}})});const Dl=e=>e.dom.focus(),Bl=e=>e.dom.blur(),Fl=e=>{const t=ht(e).dom;return e.dom===t.activeElement},Il=(e=$o())=>A.from(e.dom.activeElement).map(Ve),Rl=e=>Il(ht(e)).filter((t=>e.dom.contains(t.dom))),Nl=(e,t)=>{const o=ht(t),n=Il(o).bind((e=>{const o=t=>Ze(e,t);return o(t)?A.some(t):((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const s=Ve(e.childNodes[n]);if(t(s))return A.some(s);const r=o(e.childNodes[n]);if(r.isSome())return r}return A.none()};return o(e.dom)})(t,o)})),s=e(t);return n.each((e=>{Il(o).filter((t=>Ze(t,e))).fold((()=>{Dl(e)}),b)})),s},Vl=(e,t,o,n,s)=>{const r=e=>e+"px";return{position:e,left:t.map(r),top:o.map(r),right:n.map(r),bottom:s.map(r)}},zl=(e,t)=>{Ft(e,(e=>({...e,position:A.some(e.position)}))(t))},Hl=As([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),Ll=(e,t,o,n,s,r)=>{const a=t.rect,i=a.x-o,l=a.y-n,c=s-(i+a.width),d=r-(l+a.height),u=A.some(i),m=A.some(l),g=A.some(c),p=A.some(d),h=A.none();return t.direction.fold((()=>Vl(e,u,m,h,h)),(()=>Vl(e,h,m,g,h)),(()=>Vl(e,u,h,h,p)),(()=>Vl(e,h,h,g,p)),(()=>Vl(e,u,m,h,h)),(()=>Vl(e,u,h,h,p)),(()=>Vl(e,u,m,h,h)),(()=>Vl(e,h,m,g,h)))},Pl=(e,t)=>e.fold((()=>{const e=t.rect;return Vl("absolute",A.some(e.x),A.some(e.y),A.none(),A.none())}),((e,o,n,s)=>Ll("absolute",t,e,o,n,s)),((e,o,n,s)=>Ll("fixed",t,e,o,n,s))),Ul=(e,t)=>{const o=k(Yo,t),n=e.fold(o,o,(()=>{const e=Uo();return Yo(t).translate(-e.left,-e.top)})),s=Zt(t),r=jt(t);return Ko(n.left,n.top,s,r)},Wl=(e,t)=>t.fold((()=>e.fold(en,en,Ko)),(t=>e.fold(x(t),x(t),(()=>{const o=jl(e,t.x,t.y);return Ko(o.left,o.top,t.width,t.height)})))),jl=(e,t,o)=>{const n=$t(t,o);return e.fold(x(n),x(n),(()=>{const e=Uo();return n.translate(-e.left,-e.top)}))};Hl.none;const Gl=Hl.relative,$l=Hl.fixed,ql="data-alloy-placement",Xl=e=>_t(e,ql),Yl=As([{fit:["reposition"]},{nofit:["reposition","visibleW","visibleH","isVisible"]}]),Kl=(e,t,o,n)=>{const s=e.bubble,r=s.offset,a=((e,t,o)=>{const n=(n,s)=>t[n].map((t=>{const r="top"===n||"bottom"===n,a=r?o.top:o.left,i=("left"===n||"top"===n?Math.max:Math.min)(t,s)+a;return r?Yi(i,e.y,e.bottom):Yi(i,e.x,e.right)})).getOr(s),s=n("left",e.x),r=n("top",e.y),a=n("right",e.right),i=n("bottom",e.bottom);return Ko(s,r,a-s,i-r)})(n,e.restriction,r),i=e.x+r.left,l=e.y+r.top,c=Ko(i,l,t,o),{originInBounds:d,sizeInBounds:u,visibleW:m,visibleH:g}=((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,right:l,bottom:c,width:d,height:u}=e;return{originInBounds:a>=o&&a<=s&&i>=n&&i<=r,sizeInBounds:l<=s&&l>=o&&c<=r&&c>=n,visibleW:Math.min(d,a>=o?s-a:l-o),visibleH:Math.min(u,i>=n?r-i:c-n)}})(c,a),p=d&&u,h=p?c:((e,t)=>{const{x:o,y:n,right:s,bottom:r}=t,{x:a,y:i,width:l,height:c}=e,d=Math.max(o,s-l),u=Math.max(n,r-c),m=Yi(a,o,d),g=Yi(i,n,u),p=Math.min(m+l,s)-m,h=Math.min(g+c,r)-g;return Ko(m,g,p,h)})(c,a),f=h.width>0&&h.height>0,{maxWidth:b,maxHeight:v}=((e,t,o)=>{const n=x(t.bottom-o.y),s=x(o.bottom-t.y),r=((e,t,o,n)=>e.fold(t,t,n,n,t,n,o,o))(e,s,s,n),a=x(t.right-o.x),i=x(o.right-t.x),l=((e,t,o,n)=>e.fold(t,n,t,n,o,o,t,n))(e,i,i,a);return{maxWidth:l,maxHeight:r}})(e.direction,h,n),y={rect:h,maxHeight:v,maxWidth:b,direction:e.direction,placement:e.placement,classes:{on:s.classesOn,off:s.classesOff},layout:e.label,testY:l};return p||e.alwaysFit?Yl.fit(y):Yl.nofit(y,m,g,f)},Jl=e=>{const t=Es(A.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(A.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(A.some(e))}}},Zl=()=>Jl((e=>e.unbind())),Ql=()=>{const e=Jl(b);return{...e,on:t=>e.get().each(t)}},ec=E,tc=(e,t,o)=>((e,t,o,n)=>Fo(e,t,o,n,!1))(e,t,ec,o),oc=(e,t,o)=>((e,t,o,n)=>Fo(e,t,o,n,!0))(e,t,ec,o),nc=Bo,sc=["top","bottom","right","left"],rc="data-alloy-transition-timer",ac=(e,t,o,n,s,a)=>{const i=((e,t,o)=>o.exists((o=>{const n=e.mode;return"all"===n||o[n]!==t[n]})))(n,s,a);if(i||((e,t)=>Ga(e,t.classes))(e,n)){Dt(e,"position",o.position);const a=Ul(t,e),l=Pl(t,{...s,rect:a}),c=Z(sc,(e=>l[e]));((e,t)=>{const o=e=>parseFloat(e).toFixed(3);return he(t,((t,n)=>!((e,t,o=S)=>Se(e,t,o).getOr(e.isNone()&&t.isNone()))(e[n].map(o),t.map(o)))).isSome()})(o,c)&&(Ft(e,c),i&&((e,t)=>{Wa(e,t.classes),_t(e,rc).each((t=>{clearTimeout(parseInt(t,10)),Et(e,rc)})),((e,t)=>{const o=Zl(),n=Zl();let s;const a=t=>{var o;const n=null!==(o=t.raw.pseudoElement)&&void 0!==o?o:"";return Ze(t.target,e)&&!De(n)&&R(sc,t.raw.propertyName)},i=r=>{if(m(r)||a(r)){o.clear(),n.clear();const a=null==r?void 0:r.raw.type;(m(a)||a===or())&&(clearTimeout(s),Et(e,rc),ja(e,t.classes))}},l=tc(e,nr(),(t=>{a(t)&&(l.unbind(),o.set(tc(e,or(),i)),n.set(tc(e,tr(),i)))})),c=(e=>{const t=t=>{const o=It(e,t).split(/\s*,\s*/);return U(o,De)},o=e=>{if(r(e)&&/^[\d.]+/.test(e)){const t=parseFloat(e);return Ae(e,"ms")?t:1e3*t}return 0},n=t("transition-delay"),s=t("transition-duration");return j(s,((e,t,s)=>{const r=o(n[s])+o(t);return Math.max(e,r)}),0)})(e);requestAnimationFrame((()=>{s=setTimeout(i,c+17),kt(e,rc,s)}))})(e,t)})(e,n),Lt(e))}else ja(e,n.classes)},ic=(e,t)=>{((e,t)=>{const o=Ut.max(e,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);Dt(e,"max-height",o+"px")})(e,Math.floor(t))},lc=x(((e,t)=>{ic(e,t),Bt(e,{"overflow-x":"hidden","overflow-y":"auto"})})),cc=x(((e,t)=>{ic(e,t)})),dc=(e,t,o)=>void 0===e[t]?o:e[t],uc=(e,t,o,n)=>{const s=((e,t,o,n)=>{Ht(t,"max-height"),Ht(t,"max-width");const s={width:Zt(r=t),height:jt(r)};var r;return((e,t,o,n,s,r)=>{const a=n.width,i=n.height,l=(t,l,c,d,u)=>{const m=t(o,n,s,e,r),g=Kl(m,a,i,r);return g.fold(x(g),((e,t,o,n)=>(u===n?o>d||t>c:!u&&n)?g:Yl.nofit(l,c,d,u)))};return j(t,((e,t)=>{const o=k(l,t);return e.fold(x(e),o)}),Yl.nofit({rect:o,maxHeight:n.height,maxWidth:n.width,direction:Li(),placement:"southeast",classes:{on:[],off:[]},layout:"none",testY:o.y},-1,-1,!1)).fold(w,w)})(t,n.preference,e,s,o,n.bounds)})(e,t,o,n);return((e,t,o)=>{const n=Pl(o.origin,t);o.transition.each((s=>{ac(e,o.origin,n,s,t,o.lastPlacement)})),zl(e,n)})(t,s,n),((e,t)=>{((e,t)=>{kt(e,ql,t)})(e,t.placement)})(t,s),((e,t)=>{const o=t.classes;ja(e,o.off),Wa(e,o.on)})(t,s),((e,t,o)=>{(0,o.maxHeightFunction)(e,t.maxHeight)})(t,s,n),((e,t,o)=>{(0,o.maxWidthFunction)(e,t.maxWidth)})(t,s,n),{layout:s.layout,placement:s.placement}},mc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right","inset"],gc=(e,t,o,n=1)=>{const s=e*n,r=t*n,a=e=>be(o,e).getOr([]),i=(e,t,o)=>{const n=J(mc,o);return{offset:$t(e,t),classesOn:X(o,a),classesOff:X(n,a)}};return{southeast:()=>i(-e,t,["top","alignLeft"]),southwest:()=>i(e,t,["top","alignRight"]),south:()=>i(-e/2,t,["top","alignCentre"]),northeast:()=>i(-e,-t,["bottom","alignLeft"]),northwest:()=>i(e,-t,["bottom","alignRight"]),north:()=>i(-e/2,-t,["bottom","alignCentre"]),east:()=>i(e,-t/2,["valignCentre","left"]),west:()=>i(-e,-t/2,["valignCentre","right"]),insetNortheast:()=>i(s,r,["top","alignLeft","inset"]),insetNorthwest:()=>i(-s,r,["top","alignRight","inset"]),insetNorth:()=>i(-s/2,r,["top","alignCentre","inset"]),insetSoutheast:()=>i(s,-r,["bottom","alignLeft","inset"]),insetSouthwest:()=>i(-s,-r,["bottom","alignRight","inset"]),insetSouth:()=>i(-s/2,-r,["bottom","alignCentre","inset"]),insetEast:()=>i(-s,-r/2,["valignCentre","right","inset"]),insetWest:()=>i(s,-r/2,["valignCentre","left","inset"])}},pc=()=>gc(0,0,{}),hc=w,fc=(e,t)=>o=>"rtl"===bc(o)?t:e,bc=e=>"rtl"===It(e,"direction")?"rtl":"ltr";var vc;!function(e){e.TopToBottom="toptobottom",e.BottomToTop="bottomtotop"}(vc||(vc={}));const yc="data-alloy-vertical-dir",xc=e=>ui(e,(e=>Ge(e)&&Ot(e,"data-alloy-vertical-dir")===vc.BottomToTop)),wc=()=>vs("layouts",[os("onLtr"),os("onRtl"),us("onBottomLtr"),us("onBottomRtl")]),Sc=(e,t,o,n,s,r,a)=>{const i=a.map(xc).getOr(!1),l=t.layouts.map((t=>t.onLtr(e))),c=t.layouts.map((t=>t.onRtl(e))),d=i?t.layouts.bind((t=>t.onBottomLtr.map((t=>t(e))))).or(l).getOr(s):l.getOr(o),u=i?t.layouts.bind((t=>t.onBottomRtl.map((t=>t(e))))).or(c).getOr(r):c.getOr(n);return fc(d,u)(e)};var kc=[os("hotspot"),us("bubble"),ys("overrides",{}),wc(),Ri("placement",((e,t,o)=>{const n=t.hotspot,s=Ul(o,n.element),r=Sc(e.element,t,fl(),bl(),pl(),hl(),A.some(t.hotspot.element));return A.some(hc({anchorBox:s,bubble:t.bubble.getOr(pc()),overrides:t.overrides,layouts:r}))}))],Cc=[os("x"),os("y"),ys("height",0),ys("width",0),ys("bubble",pc()),ys("overrides",{}),wc(),Ri("placement",((e,t,o)=>{const n=jl(o,t.x,t.y),s=Ko(n.left,n.top,t.width,t.height),r=Sc(e.element,t,ml(),gl(),ml(),gl(),A.none());return A.some(hc({anchorBox:s,bubble:t.bubble,overrides:t.overrides,layouts:r}))}))];const Oc=As([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),_c=e=>e.fold(w,((e,t,o)=>e.translate(-t,-o))),Tc=e=>e.fold(w,w),Ec=e=>j(e,((e,t)=>e.translate(t.left,t.top)),$t(0,0)),Ac=e=>{const t=H(e,Tc);return Ec(t)},Mc=Oc.screen,Dc=Oc.absolute,Bc=(e,t,o)=>{const n=et(e.element),s=Uo(n),r=((e,t,o)=>{const n=nt(o.root).dom;return A.from(n.frameElement).map(Ve).filter((t=>{const o=et(t),n=et(e.element);return Ze(o,n)})).map(Xt)})(e,0,o).getOr(s);return Dc(r,s.left,s.top)},Fc=(e,t,o,n)=>{const s=Mc($t(e,t));return A.some(((e,t,o)=>({point:e,width:t,height:o}))(s,o,n))},Ic=(e,t,o,n,s)=>e.map((e=>{const r=[t,e.point],a=(i=()=>Ac(r),l=()=>Ac(r),c=()=>(e=>{const t=H(e,_c);return Ec(t)})(r),n.fold(i,l,c));var i,l,c;const d=(p=a.left,h=a.top,f=e.width,b=e.height,{x:p,y:h,width:f,height:b}),u=o.showAbove?pl():fl(),m=o.showAbove?hl():bl(),g=Sc(s,o,u,m,u,m,A.none());var p,h,f,b;return hc({anchorBox:d,bubble:o.bubble.getOr(pc()),overrides:o.overrides,layouts:g})}));var Rc=[os("node"),os("root"),us("bubble"),wc(),ys("overrides",{}),ys("showAbove",!1),Ri("placement",((e,t,o)=>{const n=Bc(e,0,t);return t.node.filter(yt).bind((s=>{const r=s.dom.getBoundingClientRect(),a=Fc(r.left,r.top,r.width,r.height),i=t.node.getOr(e.element);return Ic(a,n,t,o,i)}))}))];const Nc=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),Vc=As([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),zc=(Vc.before,Vc.on,Vc.after,e=>e.fold(w,w,w)),Hc=As([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Lc={domRange:Hc.domRange,relative:Hc.relative,exact:Hc.exact,exactFromRange:e=>Hc.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>Ve(e.startContainer),relative:(e,t)=>zc(e),exact:(e,t,o,n)=>e}))(e);return nt(t)},range:Nc},Pc=(e,t,o)=>{const n=e.document.createRange();var s;return s=n,t.fold((e=>{s.setStartBefore(e.dom)}),((e,t)=>{s.setStart(e.dom,t)}),(e=>{s.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},Uc=(e,t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},Wc=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),jc=As([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Gc=(e,t,o)=>t(Ve(o.startContainer),o.startOffset,Ve(o.endContainer),o.endOffset),$c=(e,t)=>((e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:x(e),rtl:A.none}),relative:(t,o)=>({ltr:Qt((()=>Pc(e,t,o))),rtl:Qt((()=>A.some(Pc(e,o,t))))}),exact:(t,o,n,s)=>({ltr:Qt((()=>Uc(e,t,o,n,s))),rtl:Qt((()=>A.some(Uc(e,n,s,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>jc.rtl(Ve(e.endContainer),e.endOffset,Ve(e.startContainer),e.startOffset))).getOrThunk((()=>Gc(0,jc.ltr,o))):Gc(0,jc.ltr,o)})(0,o)})(e,t).match({ltr:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(t.dom,o),r.setEnd(n.dom,s),r},rtl:(t,o,n,s)=>{const r=e.document.createRange();return r.setStart(n.dom,s),r.setEnd(t.dom,o),r}});jc.ltr,jc.rtl;const qc=(e,t,o)=>U(((e,t)=>{const o=p(t)?t:T;let n=e.dom;const s=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=Ve(e);if(s.push(t),!0===o(t))break;n=e}return s})(e,o),t),Xc=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Je(o)?[]:H(o.querySelectorAll(e),Ve)})(t,e),Yc=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return A.some(Nc(Ve(t.startContainer),t.startOffset,Ve(o.endContainer),o.endOffset))}return A.none()},Kc=e=>{if(null===e.anchorNode||null===e.focusNode)return Yc(e);{const t=Ve(e.anchorNode),o=Ve(e.focusNode);return((e,t,o,n)=>{const s=((e,t,o,n)=>{const s=et(e).dom.createRange();return s.setStart(e.dom,t),s.setEnd(o.dom,n),s})(e,t,o,n),r=Ze(e,o)&&t===n;return s.collapsed&&!r})(t,e.anchorOffset,o,e.focusOffset)?A.some(Nc(t,e.anchorOffset,o,e.focusOffset)):Yc(e)}},Jc=(e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?A.some(o).map(Wc):A.none()})($c(e,t)),Zc=((e,t)=>{const o=t=>e(t)?A.from(t.dom.nodeValue):A.none();return{get:t=>{if(!e(t))throw new Error("Can only get text value of a text node");return o(t).getOr("")},getOption:o,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})($e),Qc=(e,t)=>({element:e,offset:t}),ed=(e,t)=>$e(e)?Qc(e,t):((e,t)=>{const o=it(e);if(0===o.length)return Qc(e,t);if(t<o.length)return Qc(o[t],0);{const e=o[o.length-1],t=$e(e)?(e=>Zc.get(e))(e).length:it(e).length;return Qc(e,t)}})(e,t),td=e=>void 0!==e.foffset,od=(e,t)=>t.getSelection.getOrThunk((()=>()=>(e=>(e=>A.from(e.getSelection()))(e).filter((e=>e.rangeCount>0)).bind(Kc))(e)))().map((e=>{if(td(e)){const t=ed(e.start,e.soffset),o=ed(e.finish,e.foffset);return Lc.range(t.element,t.offset,o.element,o.offset)}return e}));var nd=[us("getSelection"),os("root"),us("bubble"),wc(),ys("overrides",{}),ys("showAbove",!1),Ri("placement",((e,t,o)=>{const n=nt(t.root).dom,s=Bc(e,0,t),r=od(n,t).bind((e=>{if(td(e)){const t=((e,t)=>(e=>{const t=e.getBoundingClientRect();return t.width>0||t.height>0?A.some(t).map(Wc):A.none()})($c(e,t)))(n,Lc.exactFromRange(e)).orThunk((()=>{const t=Ne("\ufeff");Ro(e.start,t);const o=Jc(n,Lc.exact(t,0,t,1));return Po(t),o}));return t.bind((e=>Fc(e.left,e.top,e.width,e.height)))}{const t=ce(e,(e=>e.dom.getBoundingClientRect())),o={left:Math.min(t.firstCell.left,t.lastCell.left),right:Math.max(t.firstCell.right,t.lastCell.right),top:Math.min(t.firstCell.top,t.lastCell.top),bottom:Math.max(t.firstCell.bottom,t.lastCell.bottom)};return Fc(o.left,o.top,o.right-o.left,o.bottom-o.top)}})),a=od(n,t).bind((e=>td(e)?Ge(e.start)?A.some(e.start):rt(e.start):A.some(e.firstCell))).getOr(e.element);return Ic(r,s,t,o,a)}))];const sd="link-layout",rd=e=>e.x+e.width,ad=(e,t)=>e.x-t.width,id=(e,t)=>e.y-t.height+e.height,ld=e=>e.y,cd=(e,t,o)=>zi(rd(e),ld(e),o.southeast(),Li(),"southeast",Ki(e,{left:0,top:2}),sd),dd=(e,t,o)=>zi(ad(e,t),ld(e),o.southwest(),Pi(),"southwest",Ki(e,{right:1,top:2}),sd),ud=(e,t,o)=>zi(rd(e),id(e,t),o.northeast(),Ui(),"northeast",Ki(e,{left:0,bottom:3}),sd),md=(e,t,o)=>zi(ad(e,t),id(e,t),o.northwest(),Wi(),"northwest",Ki(e,{right:1,bottom:3}),sd),gd=()=>[cd,dd,ud,md],pd=()=>[dd,cd,md,ud];var hd=[os("item"),wc(),ys("overrides",{}),Ri("placement",((e,t,o)=>{const n=Ul(o,t.item.element),s=Sc(e.element,t,gd(),pd(),gd(),pd(),A.none());return A.some(hc({anchorBox:n,bubble:pc(),overrides:t.overrides,layouts:s}))}))],fd=Jn("type",{selection:nd,node:Rc,hotspot:kc,submenu:hd,makeshift:Cc});const bd=[ds("classes",Hn),ks("mode","all",["all","layout","placement"])],vd=[ys("useFixed",T),us("getBounds")],yd=[ns("anchor",fd),vs("transition",bd)],xd=(e,t,o,n,s,r)=>{const a=Yn("placement.info",Dn(yd),s),i=a.anchor,l=n.element,c=o.get(n.uid);Nl((()=>{Dt(l,"position","fixed");const s=Nt(l,"visibility");Dt(l,"visibility","hidden");const d=t.useFixed()?(()=>{const e=document.documentElement;return $l(0,0,e.clientWidth,e.clientHeight)})():(e=>{const t=Xt(e.element),o=e.element.dom.getBoundingClientRect();return Gl(t.left,t.top,o.width,o.height)})(e);i.placement(e,i,d).each((e=>{const s=r.orThunk((()=>t.getBounds.map(_))),i=((e,t,o,n,s,r)=>((e,t,o,n,s,r,a,i)=>{const l=dc(a,"maxHeightFunction",lc()),c=dc(a,"maxWidthFunction",b),d=e.anchorBox,u=e.origin,m={bounds:Wl(u,r),origin:u,preference:n,maxHeightFunction:l,maxWidthFunction:c,lastPlacement:s,transition:i};return uc(d,t,o,m)})(((e,t)=>((e,t)=>({anchorBox:e,origin:t}))(e,t))(t.anchorBox,e),n.element,t.bubble,t.layouts,s,o,t.overrides,r))(d,e,s,n,c,a.transition);o.set(n.uid,i)})),s.fold((()=>{Ht(l,"visibility")}),(e=>{Dt(l,"visibility",e)})),Nt(l,"left").isNone()&&Nt(l,"top").isNone()&&Nt(l,"right").isNone()&&Nt(l,"bottom").isNone()&&xe(Nt(l,"position"),"fixed")&&Ht(l,"position")}),l)};var wd=Object.freeze({__proto__:null,position:(e,t,o,n,s)=>{const r=A.none();xd(e,t,o,n,s,r)},positionWithinBounds:xd,getMode:(e,t,o)=>t.useFixed()?"fixed":"absolute",reset:(e,t,o,n)=>{const s=n.element;L(["position","left","right","top","bottom"],(e=>Ht(s,e))),(e=>{Et(e,ql)})(s),o.clear(n.uid)}});const Sd=Ol({fields:vd,name:"positioning",active:Ml,apis:wd,state:Object.freeze({__proto__:null,init:()=>{let e={};return _a({readState:()=>e,clear:t=>{g(t)?delete e[t]:e={}},set:(t,o)=>{e[t]=o},get:t=>be(e,t)})}})}),kd=e=>e.getSystem().isConnected(),Cd=e=>{Fr(e,kr());const t=e.components();L(t,Cd)},Od=e=>{const t=e.components();L(t,Od),Fr(e,Sr())},_d=(e,t)=>{e.getSystem().addToWorld(t),yt(e.element)&&Od(t)},Td=e=>{Cd(e),e.getSystem().removeFromWorld(e)},Ed=(e,t)=>{zo(e.element,t.element)},Ad=(e,t)=>{Md(e,t,zo)},Md=(e,t,o)=>{e.getSystem().addToWorld(t),o(e.element,t.element),yt(e.element)&&Od(t),e.syncComponents()},Dd=e=>{Cd(e),Po(e.element),e.getSystem().removeFromWorld(e)},Bd=e=>{const t=st(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()));Dd(e),t.each((e=>{e.syncComponents()}))},Fd=e=>{const t=e.components();L(t,Dd),Lo(e.element),e.syncComponents()},Id=(e,t)=>{Nd(e,t,zo)},Rd=(e,t)=>{Nd(e,t,No)},Nd=(e,t,o)=>{o(e,t.element);const n=it(t.element);L(n,(e=>{t.getByDom(e).each(Od)}))},Vd=e=>{const t=it(e.element);L(t,(t=>{e.getByDom(t).each(Cd)})),Po(e.element)},zd=(e,t,o,n)=>{o.get().each((t=>{Fd(e)}));const s=t.getAttachPoint(e);Ad(s,e);const r=e.getSystem().build(n);return Ad(e,r),o.set(r),r},Hd=(e,t,o,n)=>{const s=zd(e,t,o,n);return t.onOpen(e,s),s},Ld=(e,t,o)=>{o.get().each((n=>{Fd(e),Bd(e),t.onClose(e,n),o.clear()}))},Pd=(e,t,o)=>o.isOpen(),Ud=(e,t,o)=>{const n=t.getAttachPoint(e);Dt(e.element,"position",Sd.getMode(n)),((e,t,o,n)=>{Nt(e.element,t).fold((()=>{Et(e.element,o)}),(t=>{kt(e.element,o,t)})),Dt(e.element,t,"hidden")})(e,"visibility",t.cloakVisibilityAttr)},Wd=(e,t,o)=>{(e=>N(["top","left","right","bottom"],(t=>Nt(e,t).isSome())))(e.element)||Ht(e.element,"position"),((e,t,o)=>{_t(e.element,o).fold((()=>Ht(e.element,t)),(o=>Dt(e.element,t,o)))})(e,"visibility",t.cloakVisibilityAttr)};var jd=Object.freeze({__proto__:null,cloak:Ud,decloak:Wd,open:Hd,openWhileCloaked:(e,t,o,n,s)=>{Ud(e,t),Hd(e,t,o,n),s(),Wd(e,t)},close:Ld,isOpen:Pd,isPartOf:(e,t,o,n)=>Pd(0,0,o)&&o.get().exists((o=>t.isPartOf(e,o,n))),getState:(e,t,o)=>o.get(),setContent:(e,t,o,n)=>o.get().map((()=>zd(e,t,o,n)))}),Gd=Object.freeze({__proto__:null,events:(e,t)=>Hr([Ur(hr(),((o,n)=>{Ld(o,e,t)}))])}),$d=[Di("onOpen"),Di("onClose"),os("isPartOf"),os("getAttachPoint"),ys("cloakVisibilityAttr","data-precloak-visibility")],qd=Object.freeze({__proto__:null,init:()=>{const e=Ql(),t=x("not-implemented");return _a({readState:t,isOpen:e.isSet,clear:e.clear,set:e.set,get:e.get})}});const Xd=Ol({fields:$d,name:"sandboxing",active:Gd,apis:jd,state:qd}),Yd=x("dismiss.popups"),Kd=x("reposition.popups"),Jd=x("mouse.released"),Zd=Mn([ys("isExtraPart",T),vs("fireEventInstead",[ys("event",Cr())])]),Qd=e=>{const t=Yn("Dismissal",Zd,e);return{[Yd()]:{schema:Mn([os("target")]),onReceive:(e,o)=>{Xd.isOpen(e)&&(Xd.isPartOf(e,o.target)||t.isExtraPart(e,o.target)||t.fireEventInstead.fold((()=>Xd.close(e)),(t=>Fr(e,t.event))))}}}},eu=Mn([vs("fireEventInstead",[ys("event",Or())]),is("doReposition")]),tu=e=>{const t=Yn("Reposition",eu,e);return{[Kd()]:{onReceive:e=>{Xd.isOpen(e)&&t.fireEventInstead.fold((()=>t.doReposition(e)),(t=>Fr(e,t.event)))}}}},ou=(e,t,o)=>{t.store.manager.onLoad(e,t,o)},nu=(e,t,o)=>{t.store.manager.onUnload(e,t,o)};var su=Object.freeze({__proto__:null,onLoad:ou,onUnload:nu,setValue:(e,t,o,n)=>{t.store.manager.setValue(e,t,o,n)},getValue:(e,t,o)=>t.store.manager.getValue(e,t,o),getState:(e,t,o)=>o}),ru=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.resetOnDom?[Kr(((o,n)=>{ou(o,e,t)})),Jr(((o,n)=>{nu(o,e,t)}))]:[xl(e,t,ou)];return Hr(o)}});const au=()=>{const e=Es(null);return _a({set:e.set,get:e.get,isNotSet:()=>null===e.get(),clear:()=>{e.set(null)},readState:()=>({mode:"memory",value:e.get()})})},iu=()=>{const e=Es({}),t=Es({});return _a({readState:()=>({mode:"dataset",dataByValue:e.get(),dataByText:t.get()}),lookup:o=>be(e.get(),o).orThunk((()=>be(t.get(),o))),update:o=>{const n=e.get(),s=t.get(),r={},a={};L(o,(e=>{r[e.value]=e,be(e,"meta").each((t=>{be(t,"text").each((t=>{a[t]=e}))}))})),e.set({...n,...r}),t.set({...s,...a})},clear:()=>{e.set({}),t.set({})}})};var lu=Object.freeze({__proto__:null,memory:au,dataset:iu,manual:()=>_a({readState:b}),init:e=>e.store.manager.state(e)});const cu=(e,t,o,n)=>{const s=t.store;o.update([n]),s.setValue(e,n),t.onSetValue(e,n)};var du=[us("initialValue"),os("getFallbackEntry"),os("getDataKey"),os("setValue"),Ri("manager",{setValue:cu,getValue:(e,t,o)=>{const n=t.store,s=n.getDataKey(e);return o.lookup(s).getOrThunk((()=>n.getFallbackEntry(s)))},onLoad:(e,t,o)=>{t.store.initialValue.each((n=>{cu(e,t,o,n)}))},onUnload:(e,t,o)=>{o.clear()},state:iu})],uu=[os("getValue"),ys("setValue",b),us("initialValue"),Ri("manager",{setValue:(e,t,o,n)=>{t.store.setValue(e,n),t.onSetValue(e,n)},getValue:(e,t,o)=>t.store.getValue(e),onLoad:(e,t,o)=>{t.store.initialValue.each((o=>{t.store.setValue(e,o)}))},onUnload:b,state:Oa.init})],mu=[us("initialValue"),Ri("manager",{setValue:(e,t,o,n)=>{o.set(n),t.onSetValue(e,n)},getValue:(e,t,o)=>o.get(),onLoad:(e,t,o)=>{t.store.initialValue.each((e=>{o.isNotSet()&&o.set(e)}))},onUnload:(e,t,o)=>{o.clear()},state:au})],gu=[xs("store",{mode:"memory"},Jn("mode",{memory:mu,manual:uu,dataset:du})),Di("onSetValue"),ys("resetOnDom",!1)];const pu=Ol({fields:gu,name:"representing",active:ru,apis:su,extra:{setValueFrom:(e,t)=>{const o=pu.getValue(t);pu.setValue(e,o)}},state:lu}),hu=(e,t)=>Ts(e,{},H(t,(t=>{return o=t.name(),n="Cannot configure "+t.name()+" for "+e,Qn(o,o,{tag:"option",process:{}},Cn((e=>un("The field: "+o+" is forbidden. "+n))));var o,n})).concat([es("dump",w)])),fu=e=>e.dump,bu=(e,t)=>({...kl(t),...e.dump}),vu=hu,yu=bu,xu="placeholder",wu=As([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Su=e=>ve(e,"uiType"),ku=(e,t,o,n)=>((e,t,o,n)=>Su(o)&&o.uiType===xu?((e,t,o,n)=>e.exists((e=>e!==o.owner))?wu.single(!0,x(o)):be(n,o.name).fold((()=>{throw new Error("Unknown placeholder component: "+o.name+"\nKnown: ["+ae(n)+"]\nNamespace: "+e.getOr("none")+"\nSpec: "+JSON.stringify(o,null,2))}),(e=>e.replace())))(e,0,o,n):wu.single(!1,x(o)))(e,0,o,n).fold(((s,r)=>{const a=Su(o)?r(t,o.config,o.validated):r(t),i=be(a,"components").getOr([]),l=X(i,(o=>ku(e,t,o,n)));return[{...a,components:l}]}),((e,n)=>{if(Su(o)){const e=n(t,o.config,o.validated);return o.validated.preprocess.getOr(w)(e)}return n(t)})),Cu=wu.single,Ou=wu.multiple,_u=x(xu),Tu=As([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),Eu=ys("factory",{sketch:w}),Au=ys("schema",[]),Mu=os("name"),Du=Qn("pname","pname",vn((e=>"<alloy."+la(e.name)+">")),Nn()),Bu=es("schema",(()=>[us("preprocess")])),Fu=ys("defaults",x({})),Iu=ys("overrides",x({})),Ru=Dn([Eu,Au,Mu,Du,Fu,Iu]),Nu=Dn([Eu,Au,Mu,Fu,Iu]),Vu=Dn([Eu,Au,Mu,Du,Fu,Iu]),zu=Dn([Eu,Bu,Mu,os("unit"),Du,Fu,Iu]),Hu=e=>e.fold(A.some,A.none,A.some,A.some),Lu=e=>{const t=e=>e.name;return e.fold(t,t,t,t)},Pu=(e,t)=>o=>{const n=Yn("Converting part type",t,o);return e(n)},Uu=Pu(Tu.required,Ru),Wu=Pu(Tu.external,Nu),ju=Pu(Tu.optional,Vu),Gu=Pu(Tu.group,zu),$u=x("entirety");var qu=Object.freeze({__proto__:null,required:Uu,external:Wu,optional:ju,group:Gu,asNamedPart:Hu,name:Lu,asCommon:e=>e.fold(w,w,w,w),original:$u});const Xu=(e,t,o,n)=>fn(t.defaults(e,o,n),o,{uid:e.partUids[t.name]},t.overrides(e,o,n)),Yu=(e,t)=>{const o={};return L(t,(t=>{Hu(t).each((t=>{const n=Ku(e,t.pname);o[t.name]=o=>{const s=Yn("Part: "+t.name+" in "+e,Dn(t.schema),o);return{...n,config:o,validated:s}}}))})),o},Ku=(e,t)=>({uiType:_u(),owner:e,name:t}),Ju=(e,t,o)=>({uiType:_u(),owner:e,name:t,config:o,validated:{}}),Zu=e=>X(e,(e=>e.fold(A.none,A.some,A.none,A.none).map((e=>ls(e.name,e.schema.concat([Ni($u())])))).toArray())),Qu=e=>H(e,Lu),em=(e,t,o)=>((e,t,o)=>{const n={},s={};return L(o,(e=>{e.fold((e=>{n[e.pname]=Cu(!0,((t,o,n)=>e.factory.sketch(Xu(t,e,o,n))))}),(e=>{const o=t.parts[e.name];s[e.name]=x(e.factory.sketch(Xu(t,e,o[$u()]),o))}),(e=>{n[e.pname]=Cu(!1,((t,o,n)=>e.factory.sketch(Xu(t,e,o,n))))}),(e=>{n[e.pname]=Ou(!0,((t,o,n)=>{const s=t[e.name];return H(s,(o=>e.factory.sketch(fn(e.defaults(t,o,n),o,e.overrides(t,o)))))}))}))})),{internals:x(n),externals:x(s)}})(0,t,o),tm=(e,t,o)=>((e,t,o,n)=>{const s=ce(n,((e,t)=>((e,t)=>{let o=!1;return{name:x(e),required:()=>t.fold(((e,t)=>e),((e,t)=>e)),used:()=>o,replace:()=>{if(o)throw new Error("Trying to use the same placeholder more than once: "+e);return o=!0,t}}})(t,e))),r=((e,t,o,n)=>X(o,(o=>ku(e,t,o,n))))(e,t,o,s);return le(s,(o=>{if(!1===o.used()&&o.required())throw new Error("Placeholder: "+o.name()+" was not found in components list\nNamespace: "+e.getOr("none")+"\nComponents: "+JSON.stringify(t.components,null,2))})),r})(A.some(e),t,t.components,o),om=(e,t,o)=>{const n=t.partUids[o];return e.getSystem().getByUid(n).toOptional()},nm=(e,t,o)=>om(e,t,o).getOrDie("Could not find part: "+o),sm=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return L(o,(e=>{n[e]=x(r.getByUid(s[e]))})),n},rm=(e,t)=>{const o=e.getSystem();return ce(t.partUids,((e,t)=>x(o.getByUid(e))))},am=e=>ae(e.partUids),im=(e,t,o)=>{const n={},s=t.partUids,r=e.getSystem();return L(o,(e=>{n[e]=x(r.getByUid(s[e]).getOrDie())})),n},lm=(e,t)=>{const o=Qu(t);return Ds(H(o,(t=>({key:t,value:e+"-"+t}))))},cm=e=>Qn("partUids","partUids",xn((t=>lm(t.uid,e))),Nn());var dm=Object.freeze({__proto__:null,generate:Yu,generateOne:Ju,schemas:Zu,names:Qu,substitutes:em,components:tm,defaultUids:lm,defaultUidsSchema:cm,getAllParts:rm,getAllPartNames:am,getPart:om,getPartOrDie:nm,getParts:sm,getPartsOrDie:im});const um=(e,t,o,n,s)=>{const r=((e,t)=>(e.length>0?[ls("parts",e)]:[]).concat([os("uid"),ys("dom",{}),ys("components",[]),Ni("originalSpec"),ys("debug.sketcher",{})]).concat(t))(n,s);return Yn(e+" [SpecSchema]",Mn(r.concat(t)),o)},mm=(e,t,o,n,s)=>{const r=gm(s),a=Zu(o),i=cm(o),l=um(e,t,r,a,[i]),c=em(0,l,o);return n(l,tm(e,l,c.internals()),r,c.externals())},gm=e=>(e=>ve(e,"uid"))(e)?e:{...e,uid:ha("uid")},pm=Mn([os("name"),os("factory"),os("configFields"),ys("apis",{}),ys("extraApis",{})]),hm=Mn([os("name"),os("factory"),os("configFields"),os("partFields"),ys("apis",{}),ys("extraApis",{})]),fm=e=>{const t=Yn("Sketcher for "+e.name,pm,e),o=ce(t.apis,Ca),n=ce(t.extraApis,((e,t)=>xa(e,t)));return{name:t.name,configFields:t.configFields,sketch:e=>((e,t,o,n)=>{const s=gm(n);return o(um(e,t,s,[],[]),s)})(t.name,t.configFields,t.factory,e),...o,...n}},bm=e=>{const t=Yn("Sketcher for "+e.name,hm,e),o=Yu(t.name,t.partFields),n=ce(t.apis,Ca),s=ce(t.extraApis,((e,t)=>xa(e,t)));return{name:t.name,partFields:t.partFields,configFields:t.configFields,sketch:e=>mm(t.name,t.configFields,t.partFields,t.factory,e),parts:o,...n,...s}},vm=e=>Ye("input")(e)&&"radio"!==Ot(e,"type")||Ye("textarea")(e);var ym=Object.freeze({__proto__:null,getCurrent:(e,t,o)=>t.find(e)});const xm=[os("find")],wm=Ol({fields:xm,name:"composing",apis:ym}),Sm=["input","button","textarea","select"],km=(e,t,o)=>{(t.disabled()?Am:Mm)(e,t)},Cm=(e,t)=>!0===t.useNative&&R(Sm,Ue(e.element)),Om=e=>{kt(e.element,"disabled","disabled")},_m=e=>{Et(e.element,"disabled")},Tm=e=>{kt(e.element,"aria-disabled","true")},Em=e=>{kt(e.element,"aria-disabled","false")},Am=(e,t,o)=>{t.disableClass.each((t=>{La(e.element,t)})),(Cm(e,t)?Om:Tm)(e),t.onDisabled(e)},Mm=(e,t,o)=>{t.disableClass.each((t=>{Pa(e.element,t)})),(Cm(e,t)?_m:Em)(e),t.onEnabled(e)},Dm=(e,t)=>Cm(e,t)?(e=>Tt(e.element,"disabled"))(e):(e=>"true"===Ot(e.element,"aria-disabled"))(e);var Bm=Object.freeze({__proto__:null,enable:Mm,disable:Am,isDisabled:Dm,onLoad:km,set:(e,t,o,n)=>{(n?Am:Mm)(e,t)}}),Fm=Object.freeze({__proto__:null,exhibit:(e,t)=>Ea({classes:t.disabled()?t.disableClass.toArray():[]}),events:(e,t)=>Hr([Lr(ur(),((t,o)=>Dm(t,e))),xl(e,t,km)])}),Im=[Os("disabled",T),ys("useNative",!0),us("disableClass"),Di("onDisabled"),Di("onEnabled")];const Rm=Ol({fields:Im,name:"disabling",active:Fm,apis:Bm}),Nm=(e,t,o,n)=>{const s=Xc(e.element,"."+t.highlightClass);L(s,(o=>{N(n,(e=>Ze(e.element,o)))||(Pa(o,t.highlightClass),e.getSystem().getByDom(o).each((o=>{t.onDehighlight(e,o),Fr(o,Br())})))}))},Vm=(e,t,o,n)=>{Nm(e,t,0,[n]),zm(e,t,o,n)||(La(n.element,t.highlightClass),t.onHighlight(e,n),Fr(n,Dr()))},zm=(e,t,o,n)=>Ua(n.element,t.highlightClass),Hm=(e,t,o)=>pi(e.element,"."+t.itemClass).bind((t=>e.getSystem().getByDom(t).toOptional())),Lm=(e,t,o)=>{const n=Xc(e.element,"."+t.itemClass);return(n.length>0?A.some(n[n.length-1]):A.none()).bind((t=>e.getSystem().getByDom(t).toOptional()))},Pm=(e,t,o,n)=>{const s=Xc(e.element,"."+t.itemClass);return $(s,(e=>Ua(e,t.highlightClass))).bind((t=>{const o=Xi(t,n,0,s.length-1);return e.getSystem().getByDom(s[o]).toOptional()}))},Um=(e,t,o)=>{const n=Xc(e.element,"."+t.itemClass);return we(H(n,(t=>e.getSystem().getByDom(t).toOptional())))};var Wm=Object.freeze({__proto__:null,dehighlightAll:(e,t,o)=>Nm(e,t,0,[]),dehighlight:(e,t,o,n)=>{zm(e,t,o,n)&&(Pa(n.element,t.highlightClass),t.onDehighlight(e,n),Fr(n,Br()))},highlight:Vm,highlightFirst:(e,t,o)=>{Hm(e,t).each((n=>{Vm(e,t,o,n)}))},highlightLast:(e,t,o)=>{Lm(e,t).each((n=>{Vm(e,t,o,n)}))},highlightAt:(e,t,o,n)=>{((e,t,o,n)=>{const s=Xc(e.element,"."+t.itemClass);return A.from(s[n]).fold((()=>sn.error(new Error("No element found with index "+n))),e.getSystem().getByDom)})(e,t,0,n).fold((e=>{throw e}),(n=>{Vm(e,t,o,n)}))},highlightBy:(e,t,o,n)=>{const s=Um(e,t);G(s,n).each((n=>{Vm(e,t,o,n)}))},isHighlighted:zm,getHighlighted:(e,t,o)=>pi(e.element,"."+t.highlightClass).bind((t=>e.getSystem().getByDom(t).toOptional())),getFirst:Hm,getLast:Lm,getPrevious:(e,t,o)=>Pm(e,t,0,-1),getNext:(e,t,o)=>Pm(e,t,0,1),getCandidates:Um}),jm=[os("highlightClass"),os("itemClass"),Di("onHighlight"),Di("onDehighlight")];const Gm=Ol({fields:jm,name:"highlighting",apis:Wm}),$m=[8],qm=[9],Xm=[13],Ym=[27],Km=[32],Jm=[37],Zm=[38],Qm=[39],eg=[40],tg=(e,t,o)=>{const n=K(e.slice(0,t)),s=K(e.slice(t+1));return G(n.concat(s),o)},og=(e,t,o)=>{const n=K(e.slice(0,t));return G(n,o)},ng=(e,t,o)=>{const n=e.slice(0,t),s=e.slice(t+1);return G(s.concat(n),o)},sg=(e,t,o)=>{const n=e.slice(t+1);return G(n,o)},rg=e=>t=>{const o=t.raw;return R(e,o.which)},ag=e=>t=>Y(e,(e=>e(t))),ig=e=>!0===e.raw.shiftKey,lg=e=>!0===e.raw.ctrlKey,cg=C(ig),dg=(e,t)=>({matches:e,classification:t}),ug=(e,t,o)=>{t.exists((e=>o.exists((t=>Ze(t,e)))))||Ir(e,_r(),{prevFocus:t,newFocus:o})},mg=()=>{const e=e=>Rl(e.element);return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().triggerFocus(o,t.element);const s=e(t);ug(t,n,s)}}},gg=()=>{const e=e=>Gm.getHighlighted(e).map((e=>e.element));return{get:e,set:(t,o)=>{const n=e(t);t.getSystem().getByDom(o).fold(b,(e=>{Gm.highlight(t,e)}));const s=e(t);ug(t,n,s)}}};var pg;!function(e){e.OnFocusMode="onFocus",e.OnEnterOrSpaceMode="onEnterOrSpace",e.OnApiMode="onApi"}(pg||(pg={}));const hg=(e,t,o,n,s)=>{const r=(e,t,o,n,s)=>{return(r=o(e,t,n,s),a=t.event,G(r,(e=>e.matches(a))).map((e=>e.classification))).bind((o=>o(e,t,n,s)));var r,a},a={schema:()=>e.concat([ys("focusManager",mg()),xs("focusInside","onFocus",Gn((e=>R(["onFocus","onEnterOrSpace","onApi"],e)?sn.value(e):sn.error("Invalid value for focusInside")))),Ri("handler",a),Ri("state",t),Ri("sendFocusIn",s)]),processKey:r,toEvents:(e,t)=>{const a=e.focusInside!==pg.OnFocusMode?A.none():s(e).map((o=>Ur(ir(),((n,s)=>{o(n,e,t),s.stop()})))),i=[Ur(Ks(),((n,a)=>{r(n,a,o,e,t).fold((()=>{((o,n)=>{const r=rg(Km.concat(Xm))(n.event);e.focusInside===pg.OnEnterOrSpaceMode&&r&&Rs(o,n)&&s(e).each((s=>{s(o,e,t),n.stop()}))})(n,a)}),(e=>{a.stop()}))})),Ur(Js(),((o,s)=>{r(o,s,n,e,t).each((e=>{s.stop()}))}))];return Hr(a.toArray().concat(i))}};return a},fg=e=>{const t=[us("onEscape"),us("onEnter"),ys("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),ys("firstTabstop",0),ys("useTabstopAt",E),us("visibilitySelector")].concat([e]),o=(e,t)=>{const o=e.visibilitySelector.bind((e=>hi(t,e))).getOr(t);return Wt(o)>0},n=(e,t)=>t.focusManager.get(e).bind((e=>hi(e,t.selector))),s=(e,t,n)=>{((e,t)=>{const n=Xc(e.element,t.selector),s=U(n,(e=>o(t,e)));return A.from(s[t.firstTabstop])})(e,t).each((o=>{t.focusManager.set(e,o)}))},r=(e,t,s,r)=>{const a=Xc(e.element,s.selector);return n(e,s).bind((t=>$(a,k(Ze,t)).bind((t=>((e,t,n,s,r)=>r(t,n,(e=>((e,t)=>o(e,t)&&e.useTabstopAt(t))(s,e))).fold((()=>s.cyclic?A.some(!0):A.none()),(t=>(s.focusManager.set(e,t),A.some(!0)))))(e,a,t,s,r)))))},a=(e,t,o)=>{const n=o.cyclic?tg:og;return r(e,0,o,n)},i=(e,t,o)=>{const n=o.cyclic?ng:sg;return r(e,0,o,n)},l=x([dg(ag([ig,rg(qm)]),a),dg(rg(qm),i),dg(ag([cg,rg(Xm)]),((e,t,o)=>o.onEnter.bind((o=>o(e,t)))))]),c=x([dg(rg(Ym),((e,t,o)=>o.onEscape.bind((o=>o(e,t))))),dg(rg(qm),((e,t,o)=>n(e,o).filter((e=>!o.useTabstopAt(e))).bind((n=>((e=>(e=>st(e))(e).bind(ct).exists((t=>Ze(t,e))))(n)?a:i)(e,t,o)))))]);return hg(t,Oa.init,l,c,(()=>A.some(s)))};var bg=fg(es("cyclic",T)),vg=fg(es("cyclic",E));const yg=(e,t,o)=>vm(o)&&rg(Km)(t.event)?A.none():((e,t,o)=>(Nr(e,o,ur()),A.some(!0)))(e,0,o),xg=(e,t)=>A.some(!0),wg=[ys("execute",yg),ys("useSpace",!1),ys("useEnter",!0),ys("useControlEnter",!1),ys("useDown",!1)],Sg=(e,t,o)=>o.execute(e,t,e.element);var kg=hg(wg,Oa.init,((e,t,o,n)=>{const s=o.useSpace&&!vm(e.element)?Km:[],r=o.useEnter?Xm:[],a=o.useDown?eg:[],i=s.concat(r).concat(a);return[dg(rg(i),Sg)].concat(o.useControlEnter?[dg(ag([lg,rg(Xm)]),Sg)]:[])}),((e,t,o,n)=>o.useSpace&&!vm(e.element)?[dg(rg(Km),xg)]:[]),(()=>A.none()));const Cg=()=>{const e=Ql();return _a({readState:()=>e.get().map((e=>({numRows:String(e.numRows),numColumns:String(e.numColumns)}))).getOr({numRows:"?",numColumns:"?"}),setGridSize:(t,o)=>{e.set({numRows:t,numColumns:o})},getNumRows:()=>e.get().map((e=>e.numRows)),getNumColumns:()=>e.get().map((e=>e.numColumns))})};var Og=Object.freeze({__proto__:null,flatgrid:Cg,init:e=>e.state(e)});const _g=e=>(t,o,n,s)=>{const r=e(t.element);return Mg(r,t,o,n,s)},Tg=(e,t)=>{const o=fc(e,t);return _g(o)},Eg=(e,t)=>{const o=fc(t,e);return _g(o)},Ag=e=>(t,o,n,s)=>Mg(e,t,o,n,s),Mg=(e,t,o,n,s)=>n.focusManager.get(t).bind((o=>e(t.element,o,n,s))).map((e=>(n.focusManager.set(t,e),!0))),Dg=Ag,Bg=Ag,Fg=Ag,Ig=e=>!(e=>e.offsetWidth<=0&&e.offsetHeight<=0)(e.dom),Rg=(e,t,o)=>{const n=Xc(e,o);return((e,o)=>$(e,(e=>Ze(e,t))).map((t=>({index:t,candidates:e}))))(U(n,Ig))},Ng=(e,t)=>$(e,(e=>Ze(t,e))),Vg=(e,t,o,n)=>n(Math.floor(t/o),t%o).bind((t=>{const n=t.row*o+t.column;return n>=0&&n<e.length?A.some(e[n]):A.none()})),zg=(e,t,o,n,s)=>Vg(e,t,n,((t,r)=>{const a=t===o-1?e.length-t*n:n,i=Xi(r,s,0,a-1);return A.some({row:t,column:i})})),Hg=(e,t,o,n,s)=>Vg(e,t,n,((t,r)=>{const a=Xi(t,s,0,o-1),i=a===o-1?e.length-a*n:n,l=Yi(r,0,i-1);return A.some({row:a,column:l})})),Lg=[os("selector"),ys("execute",yg),Bi("onEscape"),ys("captureTab",!1),Vi()],Pg=(e,t,o)=>{pi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},Ug=e=>(t,o,n,s)=>Rg(t,o,n.selector).bind((t=>e(t.candidates,t.index,s.getNumRows().getOr(n.initSize.numRows),s.getNumColumns().getOr(n.initSize.numColumns)))),Wg=(e,t,o)=>o.captureTab?A.some(!0):A.none(),jg=Ug(((e,t,o,n)=>zg(e,t,o,n,-1))),Gg=Ug(((e,t,o,n)=>zg(e,t,o,n,1))),$g=Ug(((e,t,o,n)=>Hg(e,t,o,n,-1))),qg=Ug(((e,t,o,n)=>Hg(e,t,o,n,1))),Xg=x([dg(rg(Jm),Tg(jg,Gg)),dg(rg(Qm),Eg(jg,Gg)),dg(rg(Zm),Dg($g)),dg(rg(eg),Bg(qg)),dg(ag([ig,rg(qm)]),Wg),dg(ag([cg,rg(qm)]),Wg),dg(rg(Km.concat(Xm)),((e,t,o,n)=>((e,t)=>t.focusManager.get(e).bind((e=>hi(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n)))))]),Yg=x([dg(rg(Ym),((e,t,o)=>o.onEscape(e,t))),dg(rg(Km),xg)]);var Kg=hg(Lg,Cg,Xg,Yg,(()=>A.some(Pg)));const Jg=(e,t,o,n,s)=>{const r=(e,t,o)=>s(e,t,n,0,o.length-1,o[t],(t=>{return n=o[t],"button"===Ue(n)&&"disabled"===Ot(n,"disabled")?r(e,t,o):A.from(o[t]);var n}));return Rg(e,o,t).bind((e=>{const t=e.index,o=e.candidates;return r(t,t,o)}))},Zg=(e,t,o,n)=>Jg(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Yi(t+o,n,s);return i===e?A.from(r):a(i)})),Qg=(e,t,o,n)=>Jg(e,t,o,n,((e,t,o,n,s,r,a)=>{const i=Xi(t,o,n,s);return i===e?A.none():a(i)})),ep=[os("selector"),ys("getInitial",A.none),ys("execute",yg),Bi("onEscape"),ys("executeOnMove",!1),ys("allowVertical",!0),ys("allowHorizontal",!0),ys("cycles",!0)],tp=(e,t,o)=>((e,t)=>t.focusManager.get(e).bind((e=>hi(e,t.selector))))(e,o).bind((n=>o.execute(e,t,n))),op=(e,t,o)=>{t.getInitial(e).orThunk((()=>pi(e.element,t.selector))).each((o=>{t.focusManager.set(e,o)}))},np=(e,t,o)=>(o.cycles?Qg:Zg)(e,o.selector,t,-1),sp=(e,t,o)=>(o.cycles?Qg:Zg)(e,o.selector,t,1),rp=e=>(t,o,n,s)=>e(t,o,n,s).bind((()=>n.executeOnMove?tp(t,o,n):A.some(!0))),ap=x([dg(rg(Km),xg),dg(rg(Ym),((e,t,o)=>o.onEscape(e,t)))]);var ip=hg(ep,Oa.init,((e,t,o,n)=>{const s=[...o.allowHorizontal?Jm:[]].concat(o.allowVertical?Zm:[]),r=[...o.allowHorizontal?Qm:[]].concat(o.allowVertical?eg:[]);return[dg(rg(s),rp(Tg(np,sp))),dg(rg(r),rp(Eg(np,sp))),dg(rg(Xm),tp),dg(rg(Km),tp)]}),ap,(()=>A.some(op)));const lp=(e,t,o)=>A.from(e[t]).bind((e=>A.from(e[o]).map((e=>({rowIndex:t,columnIndex:o,cell:e}))))),cp=(e,t,o,n)=>{const s=e[t].length,r=Xi(o,n,0,s-1);return lp(e,t,r)},dp=(e,t,o,n)=>{const s=Xi(o,n,0,e.length-1),r=e[s].length,a=Yi(t,0,r-1);return lp(e,s,a)},up=(e,t,o,n)=>{const s=e[t].length,r=Yi(o+n,0,s-1);return lp(e,t,r)},mp=(e,t,o,n)=>{const s=Yi(o+n,0,e.length-1),r=e[s].length,a=Yi(t,0,r-1);return lp(e,s,a)},gp=[ls("selectors",[os("row"),os("cell")]),ys("cycles",!0),ys("previousSelector",A.none),ys("execute",yg)],pp=(e,t,o)=>{t.previousSelector(e).orThunk((()=>{const o=t.selectors;return pi(e.element,o.cell)})).each((o=>{t.focusManager.set(e,o)}))},hp=(e,t)=>(o,n,s)=>{const r=s.cycles?e:t;return hi(n,s.selectors.row).bind((e=>{const t=Xc(e,s.selectors.cell);return Ng(t,n).bind((t=>{const n=Xc(o,s.selectors.row);return Ng(n,e).bind((e=>{const o=((e,t)=>H(e,(e=>Xc(e,t.selectors.cell))))(n,s);return r(o,e,t).map((e=>e.cell))}))}))}))},fp=hp(((e,t,o)=>cp(e,t,o,-1)),((e,t,o)=>up(e,t,o,-1))),bp=hp(((e,t,o)=>cp(e,t,o,1)),((e,t,o)=>up(e,t,o,1))),vp=hp(((e,t,o)=>dp(e,o,t,-1)),((e,t,o)=>mp(e,o,t,-1))),yp=hp(((e,t,o)=>dp(e,o,t,1)),((e,t,o)=>mp(e,o,t,1))),xp=x([dg(rg(Jm),Tg(fp,bp)),dg(rg(Qm),Eg(fp,bp)),dg(rg(Zm),Dg(vp)),dg(rg(eg),Bg(yp)),dg(rg(Km.concat(Xm)),((e,t,o)=>Rl(e.element).bind((n=>o.execute(e,t,n)))))]),wp=x([dg(rg(Km),xg)]);var Sp=hg(gp,Oa.init,xp,wp,(()=>A.some(pp)));const kp=[os("selector"),ys("execute",yg),ys("moveOnTab",!1)],Cp=(e,t,o)=>o.focusManager.get(e).bind((n=>o.execute(e,t,n))),Op=(e,t,o)=>{pi(e.element,t.selector).each((o=>{t.focusManager.set(e,o)}))},_p=(e,t,o)=>Qg(e,o.selector,t,-1),Tp=(e,t,o)=>Qg(e,o.selector,t,1),Ep=x([dg(rg(Zm),Fg(_p)),dg(rg(eg),Fg(Tp)),dg(ag([ig,rg(qm)]),((e,t,o,n)=>o.moveOnTab?Fg(_p)(e,t,o,n):A.none())),dg(ag([cg,rg(qm)]),((e,t,o,n)=>o.moveOnTab?Fg(Tp)(e,t,o,n):A.none())),dg(rg(Xm),Cp),dg(rg(Km),Cp)]),Ap=x([dg(rg(Km),xg)]);var Mp=hg(kp,Oa.init,Ep,Ap,(()=>A.some(Op)));const Dp=[Bi("onSpace"),Bi("onEnter"),Bi("onShiftEnter"),Bi("onLeft"),Bi("onRight"),Bi("onTab"),Bi("onShiftTab"),Bi("onUp"),Bi("onDown"),Bi("onEscape"),ys("stopSpaceKeyup",!1),us("focusIn")];var Bp=hg(Dp,Oa.init,((e,t,o)=>[dg(rg(Km),o.onSpace),dg(ag([cg,rg(Xm)]),o.onEnter),dg(ag([ig,rg(Xm)]),o.onShiftEnter),dg(ag([ig,rg(qm)]),o.onShiftTab),dg(ag([cg,rg(qm)]),o.onTab),dg(rg(Zm),o.onUp),dg(rg(eg),o.onDown),dg(rg(Jm),o.onLeft),dg(rg(Qm),o.onRight),dg(rg(Km),o.onSpace)]),((e,t,o)=>[...o.stopSpaceKeyup?[dg(rg(Km),xg)]:[],dg(rg(Ym),o.onEscape)]),(e=>e.focusIn));const Fp=bg.schema(),Ip=vg.schema(),Rp=ip.schema(),Np=Kg.schema(),Vp=Sp.schema(),zp=kg.schema(),Hp=Mp.schema(),Lp=Bp.schema(),Pp=Tl({branchKey:"mode",branches:Object.freeze({__proto__:null,acyclic:Fp,cyclic:Ip,flow:Rp,flatgrid:Np,matrix:Vp,execution:zp,menu:Hp,special:Lp}),name:"keying",active:{events:(e,t)=>e.handler.toEvents(e,t)},apis:{focusIn:(e,t,o)=>{t.sendFocusIn(t).fold((()=>{e.getSystem().triggerFocus(e.element,e.element)}),(n=>{n(e,t,o)}))},setGridSize:(e,t,o,n,s)=>{(e=>ye(e,"setGridSize"))(o)?o.setGridSize(n,s):console.error("Layout does not support setGridSize")}},state:Og}),Up=(e,t)=>{Nl((()=>{((e,t,o)=>{const n=e.components();(e=>{L(e.components(),(e=>Po(e.element))),Lo(e.element),e.syncComponents()})(e);const s=o(t),r=J(n,s);L(r,(t=>{Cd(t),e.getSystem().removeFromWorld(t)})),L(s,(t=>{kd(t)?Ed(e,t):(e.getSystem().addToWorld(t),Ed(e,t),yt(e.element)&&Od(t))})),e.syncComponents()})(e,t,(()=>H(t,e.getSystem().build)))}),e.element)},Wp=(e,t)=>{Nl((()=>{((o,n,s)=>{const r=o.components(),a=X(n,(e=>ka(e).toArray()));L(r,(e=>{R(a,e)||Td(e)}));const i=((e,t,o)=>Ya(e,t,((t,n)=>Ka(e,n,t,o))))(e.element,t,e.getSystem().buildOrPatch),l=J(r,i);L(l,(e=>{kd(e)&&Td(e)})),L(i,(e=>{kd(e)||_d(o,e)})),o.syncComponents()})(e,t)}),e.element)},jp=(e,t,o,n)=>{Td(t);const s=Ka(e.element,o,n,e.getSystem().buildOrPatch);_d(e,s),e.syncComponents()},Gp=(e,t,o)=>{const n=e.getSystem().build(o);Md(e,n,t)},$p=(e,t,o,n)=>{Bd(t),Gp(e,((e,t)=>((e,t,o)=>{lt(e,o).fold((()=>{zo(e,t)}),(e=>{Ro(e,t)}))})(e,t,o)),n)},qp=(e,t)=>e.components(),Xp=(e,t,o,n,s)=>{const r=qp(e);return A.from(r[n]).map((o=>(s.fold((()=>Bd(o)),(s=>{(t.reuseDom?jp:$p)(e,o,n,s)})),o)))};var Yp=Object.freeze({__proto__:null,append:(e,t,o,n)=>{Gp(e,zo,n)},prepend:(e,t,o,n)=>{Gp(e,Vo,n)},remove:(e,t,o,n)=>{const s=qp(e),r=G(s,(e=>Ze(n.element,e.element)));r.each(Bd)},replaceAt:Xp,replaceBy:(e,t,o,n,s)=>{const r=qp(e);return $(r,n).bind((o=>Xp(e,t,0,o,s)))},set:(e,t,o,n)=>(t.reuseDom?Wp:Up)(e,n),contents:qp});const Kp=Ol({fields:[Cs("reuseDom",!0)],name:"replacing",apis:Yp}),Jp=(e,t)=>{const o=((e,t)=>{const o=Hr(t);return Ol({fields:[os("enabled")],name:e,active:{events:x(o)}})})(e,t);return{key:e,value:{config:{},me:o,configAsRaw:x({}),initialConfig:{},state:Oa}}},Zp=(e,t)=>{t.ignore||(Dl(e.element),t.onFocus(e))};var Qp=Object.freeze({__proto__:null,focus:Zp,blur:(e,t)=>{t.ignore||Bl(e.element)},isFocused:e=>Fl(e.element)}),eh=Object.freeze({__proto__:null,exhibit:(e,t)=>{const o=t.ignore?{}:{attributes:{tabindex:"-1"}};return Ea(o)},events:e=>Hr([Ur(ir(),((t,o)=>{Zp(t,e),o.stop()}))].concat(e.stopMousedown?[Ur(Ws(),((e,t)=>{t.event.prevent()}))]:[]))}),th=[Di("onFocus"),ys("stopMousedown",!1),ys("ignore",!1)];const oh=Ol({fields:th,name:"focusing",active:eh,apis:Qp}),nh=(e,t,o,n)=>{const s=o.get();o.set(n),((e,t,o)=>{t.toggleClass.each((t=>{o.get()?La(e.element,t):Pa(e.element,t)}))})(e,t,o),((e,t,o)=>{const n=t.aria;n.update(e,n,o.get())})(e,t,o),s!==n&&t.onToggled(e,n)},sh=(e,t,o)=>{nh(e,t,o,!o.get())},rh=(e,t,o)=>{nh(e,t,o,t.selected)};var ah=Object.freeze({__proto__:null,onLoad:rh,toggle:sh,isOn:(e,t,o)=>o.get(),on:(e,t,o)=>{nh(e,t,o,!0)},off:(e,t,o)=>{nh(e,t,o,!1)},set:nh}),ih=Object.freeze({__proto__:null,exhibit:()=>Ea({}),events:(e,t)=>{const o=(n=e,s=t,r=sh,Qr((e=>{r(e,n,s)})));var n,s,r;const a=xl(e,t,rh);return Hr(q([e.toggleOnExecute?[o]:[],[a]]))}});const lh=(e,t,o)=>{kt(e.element,"aria-expanded",o)};var ch=[ys("selected",!1),us("toggleClass"),ys("toggleOnExecute",!0),Di("onToggled"),xs("aria",{mode:"none"},Jn("mode",{pressed:[ys("syncWithExpanded",!1),Ri("update",((e,t,o)=>{kt(e.element,"aria-pressed",o),t.syncWithExpanded&&lh(e,0,o)}))],checked:[Ri("update",((e,t,o)=>{kt(e.element,"aria-checked",o)}))],expanded:[Ri("update",lh)],selected:[Ri("update",((e,t,o)=>{kt(e.element,"aria-selected",o)}))],none:[Ri("update",b)]}))];const dh=Ol({fields:ch,name:"toggling",active:ih,apis:ah,state:(!1,{init:()=>{const e=Es(false);return{get:()=>e.get(),set:t=>e.set(t),clear:()=>e.set(false),readState:()=>e.get()}}})});const uh=()=>{const e=(e,t)=>{t.stop(),Rr(e)};return[Ur(er(),e),Ur(gr(),e),qr(Hs()),qr(Ws())]},mh=e=>Hr(q([e.map((e=>Qr(((t,o)=>{e(t),o.stop()})))).toArray(),uh()])),gh="alloy.item-hover",ph="alloy.item-focus",hh="alloy.item-toggled",fh=e=>{(Rl(e.element).isNone()||oh.isFocused(e))&&(oh.isFocused(e)||oh.focus(e),Ir(e,gh,{item:e}))},bh=e=>{Ir(e,ph,{item:e})},vh=x(gh),yh=x(ph),xh=x(hh),wh=e=>e.toggling.map((e=>e.exclusive?"menuitemradio":"menuitemcheckbox")).getOr("menuitem"),Sh=[os("data"),os("components"),os("dom"),ys("hasSubmenu",!1),us("toggling"),vu("itemBehaviours",[dh,oh,Pp,pu]),ys("ignoreFocus",!1),ys("domModification",{}),Ri("builder",(e=>({dom:e.dom,domModification:{...e.domModification,attributes:{role:wh(e),...e.domModification.attributes,"aria-haspopup":e.hasSubmenu,...e.hasSubmenu?{"aria-expanded":!1}:{}}},behaviours:yu(e.itemBehaviours,[e.toggling.fold(dh.revoke,(e=>dh.config((e=>({aria:{mode:"checked"},...ge(e,((e,t)=>"exclusive"!==t)),onToggled:(t,o)=>{p(e.onToggled)&&e.onToggled(t,o),((e,t)=>{Ir(e,hh,{item:e,state:t})})(t,o)}}))(e)))),oh.config({ignore:e.ignoreFocus,stopMousedown:e.ignoreFocus,onFocus:e=>{bh(e)}}),Pp.config({mode:"execution"}),pu.config({store:{mode:"memory",initialValue:e.data}}),Jp("item-type-events",[...uh(),Ur(qs(),fh),Ur(mr(),oh.focus)])]),components:e.components,eventOrder:e.eventOrder}))),ys("eventOrder",{})],kh=[os("dom"),os("components"),Ri("builder",(e=>({dom:e.dom,components:e.components,events:Hr([Xr(mr())])})))],Ch=x("item-widget"),Oh=x([Uu({name:"widget",overrides:e=>({behaviours:kl([pu.config({store:{mode:"manual",getValue:t=>e.data,setValue:b}})])})})]),_h=[os("uid"),os("data"),os("components"),os("dom"),ys("autofocus",!1),ys("ignoreFocus",!1),vu("widgetBehaviours",[pu,oh,Pp]),ys("domModification",{}),cm(Oh()),Ri("builder",(e=>{const t=em(Ch(),e,Oh()),o=tm(Ch(),e,t.internals()),n=t=>om(t,e,"widget").map((e=>(Pp.focusIn(e),e))),s=(t,o)=>vm(o.event.target)?A.none():e.autofocus?(o.setSource(t.element),A.none()):A.none();return{dom:e.dom,components:o,domModification:e.domModification,events:Hr([Qr(((e,t)=>{n(e).each((e=>{t.stop()}))})),Ur(qs(),fh),Ur(mr(),((t,o)=>{e.autofocus?n(t):oh.focus(t)}))]),behaviours:yu(e.widgetBehaviours,[pu.config({store:{mode:"memory",initialValue:e.data}}),oh.config({ignore:e.ignoreFocus,onFocus:e=>{bh(e)}}),Pp.config({mode:"special",focusIn:e.autofocus?e=>{n(e)}:El(),onLeft:s,onRight:s,onEscape:(t,o)=>oh.isFocused(t)||e.autofocus?e.autofocus?(o.setSource(t.element),A.none()):A.none():(oh.focus(t),A.some(!0))})])}}))],Th=Jn("type",{widget:_h,item:Sh,separator:kh}),Eh=x([Gu({factory:{sketch:e=>{const t=Yn("menu.spec item",Th,e);return t.builder(t)}},name:"items",unit:"item",defaults:(e,t)=>ve(t,"uid")?t:{...t,uid:ha("item")},overrides:(e,t)=>({type:t.type,ignoreFocus:e.fakeFocus,domModification:{classes:[e.markers.item]}})})]),Ah=x([os("value"),os("items"),os("dom"),os("components"),ys("eventOrder",{}),hu("menuBehaviours",[Gm,pu,wm,Pp]),xs("movement",{mode:"menu",moveOnTab:!0},Jn("mode",{grid:[Vi(),Ri("config",((e,t)=>({mode:"flatgrid",selector:"."+e.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:e.focusManager})))],matrix:[Ri("config",((e,t)=>({mode:"matrix",selectors:{row:t.rowSelector,cell:"."+e.markers.item},previousSelector:t.previousSelector,focusManager:e.focusManager}))),os("rowSelector"),ys("previousSelector",A.none)],menu:[ys("moveOnTab",!0),Ri("config",((e,t)=>({mode:"menu",selector:"."+e.markers.item,moveOnTab:t.moveOnTab,focusManager:e.focusManager})))]})),ns("markers",_i()),ys("fakeFocus",!1),ys("focusManager",mg()),Di("onHighlight"),Di("onDehighlight")]),Mh=x("alloy.menu-focus"),Dh=bm({name:"Menu",configFields:Ah(),partFields:Eh(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,markers:e.markers,behaviours:bu(e.menuBehaviours,[Gm.config({highlightClass:e.markers.selectedItem,itemClass:e.markers.item,onHighlight:e.onHighlight,onDehighlight:e.onDehighlight}),pu.config({store:{mode:"memory",initialValue:e.value}}),wm.config({find:A.some}),Pp.config(e.movement.config(e,e.movement))]),events:Hr([Ur(yh(),((e,t)=>{const o=t.event;e.getSystem().getByDom(o.target).each((o=>{Gm.highlight(e,o),t.stop(),Ir(e,Mh(),{menu:e,item:o})}))})),Ur(vh(),((e,t)=>{const o=t.event.item;Gm.highlight(e,o)})),Ur(xh(),((e,t)=>{const{item:o,state:n}=t.event;n&&"menuitemradio"===Ot(o.element,"role")&&((e,t)=>{const o=Xc(e.element,'[role="menuitemradio"][aria-checked="true"]');L(o,(o=>{Ze(o,t.element)||e.getSystem().getByDom(o).each((e=>{dh.off(e)}))}))})(e,o)}))]),components:t,eventOrder:e.eventOrder,domModification:{attributes:{role:"menu"}}})}),Bh=(e,t,o,n)=>be(o,n).bind((n=>be(e,n).bind((n=>{const s=Bh(e,t,o,n);return A.some([n].concat(s))})))).getOr([]),Fh=e=>"prepared"===e.type?A.some(e.menu):A.none(),Ih=()=>{const e=Es({}),t=Es({}),o=Es({}),n=Ql(),s=Es({}),r=e=>a(e).bind(Fh),a=e=>be(t.get(),e),i=t=>be(e.get(),t);return{setMenuBuilt:(e,o)=>{t.set({...t.get(),[e]:{type:"prepared",menu:o}})},setContents:(r,a,i,l)=>{n.set(r),e.set(i),t.set(a),s.set(l);const c=((e,t)=>{const o={};le(e,((e,t)=>{L(e,(e=>{o[e]=t}))}));const n=t,s=de(t,((e,t)=>({k:e,v:t}))),r=ce(s,((e,t)=>[t].concat(Bh(o,n,s,t))));return ce(o,(e=>be(r,e).getOr([e])))})(l,i);o.set(c)},expand:t=>be(e.get(),t).map((e=>{const n=be(o.get(),t).getOr([]);return[e].concat(n)})),refresh:e=>be(o.get(),e),collapse:e=>be(o.get(),e).bind((e=>e.length>1?A.some(e.slice(1)):A.none())),lookupMenu:a,lookupItem:i,otherMenus:e=>{const t=s.get();return J(ae(t),e)},getPrimary:()=>n.get().bind(r),getMenus:()=>t.get(),clear:()=>{e.set({}),t.set({}),o.set({}),n.clear()},isClear:()=>n.get().isNone(),getTriggeringPath:(t,s)=>{const a=U(i(t).toArray(),(e=>r(e).isSome()));return be(o.get(),t).bind((t=>{const o=K(a.concat(t));return(e=>{const t=[];for(let o=0;o<e.length;o++){const n=e[o];if(!n.isSome())return A.none();t.push(n.getOrDie())}return A.some(t)})(X(o,((t,a)=>((t,o,n)=>r(t).bind((s=>(t=>he(e.get(),((e,o)=>e===t)))(t).bind((e=>o(e).map((e=>({triggeredMenu:s,triggeringItem:e,triggeringPath:n}))))))))(t,s,o.slice(0,a+1)).fold((()=>xe(n.get(),t)?[]:[A.none()]),(e=>[A.some(e)])))))}))}}},Rh=Fh,Nh=la("tiered-menu-item-highlight"),Vh=la("tiered-menu-item-dehighlight");var zh;!function(e){e[e.HighlightMenuAndItem=0]="HighlightMenuAndItem",e[e.HighlightJustMenu=1]="HighlightJustMenu",e[e.HighlightNone=2]="HighlightNone"}(zh||(zh={}));const Hh=x("collapse-item"),Lh=fm({name:"TieredMenu",configFields:[Ii("onExecute"),Ii("onEscape"),Fi("onOpenMenu"),Fi("onOpenSubmenu"),Di("onRepositionMenu"),Di("onCollapseMenu"),ys("highlightOnOpen",zh.HighlightMenuAndItem),ls("data",[os("primary"),os("menus"),os("expansions")]),ys("fakeFocus",!1),Di("onHighlightItem"),Di("onDehighlightItem"),Di("onHover"),Ei(),os("dom"),ys("navigateOnHover",!0),ys("stayInDom",!1),hu("tmenuBehaviours",[Pp,Gm,wm,Kp]),ys("eventOrder",{})],apis:{collapseMenu:(e,t)=>{e.collapseMenu(t)},highlightPrimary:(e,t)=>{e.highlightPrimary(t)},repositionMenus:(e,t)=>{e.repositionMenus(t)}},factory:(e,t)=>{const o=Ql(),n=Ih(),s=e=>pu.getValue(e).value,r=t=>ce(e.data.menus,((e,t)=>X(e.items,(e=>"separator"===e.type?[]:[e.data.value])))),a=Gm.highlight,i=(t,o)=>{a(t,o),Gm.getHighlighted(o).orThunk((()=>Gm.getFirst(o))).each((n=>{e.fakeFocus?Gm.highlight(o,n):Nr(t,n.element,mr())}))},l=(e,t)=>we(H(t,(t=>e.lookupMenu(t).bind((e=>"prepared"===e.type?A.some(e.menu):A.none()))))),c=(t,o,n)=>{const s=l(o,o.otherMenus(n));L(s,(o=>{ja(o.element,[e.markers.backgroundMenu]),e.stayInDom||Kp.remove(t,o)}))},d=(t,n)=>{const r=(t=>o.get().getOrThunk((()=>{const n={},r=Xc(t.element,`.${e.markers.item}`),a=U(r,(e=>"true"===Ot(e,"aria-haspopup")));return L(a,(e=>{t.getSystem().getByDom(e).each((e=>{const t=s(e);n[t]=e}))})),o.set(n),n})))(t);le(r,((e,t)=>{const o=R(n,t);kt(e.element,"aria-expanded",o)}))},u=(t,o,n)=>A.from(n[0]).bind((s=>o.lookupMenu(s).bind((s=>{if("notbuilt"===s.type)return A.none();{const r=s.menu,a=l(o,n.slice(1));return L(a,(t=>{La(t.element,e.markers.backgroundMenu)})),yt(r.element)||Kp.append(t,ai(r)),ja(r.element,[e.markers.backgroundMenu]),i(t,r),c(t,o,n),A.some(r)}}))));let m;!function(e){e[e.HighlightSubmenu=0]="HighlightSubmenu",e[e.HighlightParent=1]="HighlightParent"}(m||(m={}));const g=(t,o,r=m.HighlightSubmenu)=>{if(o.hasConfigured(Rm)&&Rm.isDisabled(o))return A.some(o);{const a=s(o);return n.expand(a).bind((s=>(d(t,s),A.from(s[0]).bind((a=>n.lookupMenu(a).bind((i=>{const l=((e,t,o)=>{if("notbuilt"===o.type){const s=e.getSystem().build(o.nbMenu());return n.setMenuBuilt(t,s),s}return o.menu})(t,a,i);return yt(l.element)||Kp.append(t,ai(l)),e.onOpenSubmenu(t,o,l,K(s)),r===m.HighlightSubmenu?(Gm.highlightFirst(l),u(t,n,s)):(Gm.dehighlightAll(l),A.some(o))})))))))}},p=(t,o)=>{const r=s(o);return n.collapse(r).bind((s=>(d(t,s),u(t,n,s).map((n=>(e.onCollapseMenu(t,o,n),n))))))},h=t=>(o,n)=>hi(n.getSource(),`.${e.markers.item}`).bind((e=>o.getSystem().getByDom(e).toOptional().bind((e=>t(o,e).map(E))))),f=Hr([Ur(Mh(),((e,t)=>{const o=t.event.item;n.lookupItem(s(o)).each((()=>{const o=t.event.menu;Gm.highlight(e,o);const r=s(t.event.item);n.refresh(r).each((t=>c(e,n,t)))}))})),Qr(((t,o)=>{const n=o.event.target;t.getSystem().getByDom(n).each((o=>{0===s(o).indexOf("collapse-item")&&p(t,o),g(t,o,m.HighlightSubmenu).fold((()=>{e.onExecute(t,o)}),b)}))})),Kr(((t,o)=>{(t=>{const o=((t,o,n)=>ce(n,((n,s)=>{const r=()=>Dh.sketch({...n,value:s,markers:e.markers,fakeFocus:e.fakeFocus,onHighlight:(e,t)=>{Ir(e,Nh,{menuComp:e,itemComp:t})},onDehighlight:(e,t)=>{Ir(e,Vh,{menuComp:e,itemComp:t})},focusManager:e.fakeFocus?gg():mg()});return s===o?{type:"prepared",menu:t.getSystem().build(r())}:{type:"notbuilt",nbMenu:r}})))(t,e.data.primary,e.data.menus),s=r();return n.setContents(e.data.primary,o,e.data.expansions,s),n.getPrimary()})(t).each((o=>{Kp.append(t,ai(o)),e.onOpenMenu(t,o),e.highlightOnOpen===zh.HighlightMenuAndItem?i(t,o):e.highlightOnOpen===zh.HighlightJustMenu&&a(t,o)}))})),Ur(Nh,((t,o)=>{e.onHighlightItem(t,o.event.menuComp,o.event.itemComp)})),Ur(Vh,((t,o)=>{e.onDehighlightItem(t,o.event.menuComp,o.event.itemComp)})),...e.navigateOnHover?[Ur(vh(),((t,o)=>{const r=o.event.item;((e,t)=>{const o=s(t);n.refresh(o).bind((t=>(d(e,t),u(e,n,t))))})(t,r),g(t,r,m.HighlightParent),e.onHover(t,r)}))]:[]]),v=e=>Gm.getHighlighted(e).bind(Gm.getHighlighted),y={collapseMenu:e=>{v(e).each((t=>{p(e,t)}))},highlightPrimary:e=>{n.getPrimary().each((t=>{i(e,t)}))},repositionMenus:t=>{const o=n.getPrimary().bind((e=>v(t).bind((e=>{const t=s(e),o=fe(n.getMenus()),r=we(H(o,Rh));return n.getTriggeringPath(t,(e=>((e,t,o)=>re(t,(e=>{if(!e.getSystem().isConnected())return A.none();const t=Gm.getCandidates(e);return G(t,(e=>s(e)===o))})))(0,r,e)))})).map((t=>({primary:e,triggeringPath:t})))));o.fold((()=>{(e=>A.from(e.components()[0]).filter((e=>"menu"===Ot(e.element,"role"))))(t).each((o=>{e.onRepositionMenu(t,o,[])}))}),(({primary:o,triggeringPath:n})=>{e.onRepositionMenu(t,o,n)}))}};return{uid:e.uid,dom:e.dom,markers:e.markers,behaviours:bu(e.tmenuBehaviours,[Pp.config({mode:"special",onRight:h(((e,t)=>vm(t.element)?A.none():g(e,t,m.HighlightSubmenu))),onLeft:h(((e,t)=>vm(t.element)?A.none():p(e,t))),onEscape:h(((t,o)=>p(t,o).orThunk((()=>e.onEscape(t,o).map((()=>t)))))),focusIn:(e,t)=>{n.getPrimary().each((t=>{Nr(e,t.element,mr())}))}}),Gm.config({highlightClass:e.markers.selectedMenu,itemClass:e.markers.menu}),wm.config({find:e=>Gm.getHighlighted(e)}),Kp.config({})]),eventOrder:e.eventOrder,apis:y,events:f}},extraApis:{tieredData:(e,t,o)=>({primary:e,menus:t,expansions:o}),singleData:(e,t)=>({primary:e,menus:Ms(e,t),expansions:{}}),collapseItem:e=>({value:la(Hh()),meta:{text:e}})}}),Ph=fm({name:"InlineView",configFields:[os("lazySink"),Di("onShow"),Di("onHide"),fs("onEscape"),hu("inlineBehaviours",[Xd,pu,Al]),vs("fireDismissalEventInstead",[ys("event",Cr())]),vs("fireRepositionEventInstead",[ys("event",Or())]),ys("getRelated",A.none),ys("isExtraPart",T),ys("eventOrder",A.none)],factory:(e,t)=>{const o=(t,o,n,s)=>{const r=e.lazySink(t).getOrDie();Xd.openWhileCloaked(t,o,(()=>Sd.positionWithinBounds(r,t,n,s()))),pu.setValue(t,A.some({mode:"position",config:n,getBounds:s}))},n=(t,o,n,s)=>{const r=((e,t,o,n,s)=>{const r=()=>e.lazySink(t),a="horizontal"===n.type?{layouts:{onLtr:()=>fl(),onRtl:()=>bl()}}:{},i=e=>(e=>2===e.length)(e)?a:{};return Lh.sketch({dom:{tag:"div"},data:n.data,markers:n.menu.markers,highlightOnOpen:n.menu.highlightOnOpen,fakeFocus:n.menu.fakeFocus,onEscape:()=>(Xd.close(t),e.onEscape.map((e=>e(t))),A.some(!0)),onExecute:()=>A.some(!0),onOpenMenu:(e,t)=>{Sd.positionWithinBounds(r().getOrDie(),t,o,s())},onOpenSubmenu:(e,t,o,n)=>{const s=r().getOrDie();Sd.position(s,o,{anchor:{type:"submenu",item:t,...i(n)}})},onRepositionMenu:(e,t,n)=>{const a=r().getOrDie();Sd.positionWithinBounds(a,t,o,s()),L(n,(e=>{const t=i(e.triggeringPath);Sd.position(a,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem,...t}})}))}})})(e,t,o,n,s);Xd.open(t,r),pu.setValue(t,A.some({mode:"menu",menu:r}))},s=t=>{Xd.isOpen(t)&&pu.getValue(t).each((o=>{switch(o.mode){case"menu":Xd.getState(t).each(Lh.repositionMenus);break;case"position":const n=e.lazySink(t).getOrDie();Sd.positionWithinBounds(n,t,o.config,o.getBounds())}}))},r={setContent:(e,t)=>{Xd.setContent(e,t)},showAt:(e,t,n)=>{const s=A.none;o(e,t,n,s)},showWithinBounds:o,showMenuAt:(e,t,o)=>{n(e,t,o,A.none)},showMenuWithinBounds:n,hide:e=>{Xd.isOpen(e)&&(pu.setValue(e,A.none()),Xd.close(e))},getContent:e=>Xd.getState(e),reposition:s,isOpen:Xd.isOpen};return{uid:e.uid,dom:e.dom,behaviours:bu(e.inlineBehaviours,[Xd.config({isPartOf:(t,o,n)=>vi(o,n)||((t,o)=>e.getRelated(t).exists((e=>vi(e,o))))(t,n),getAttachPoint:t=>e.lazySink(t).getOrDie(),onOpen:t=>{e.onShow(t)},onClose:t=>{e.onHide(t)}}),pu.config({store:{mode:"memory",initialValue:A.none()}}),Al.config({channels:{...Qd({isExtraPart:t.isExtraPart,...e.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...tu({...e.fireRepositionEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({}),doReposition:s})}})]),eventOrder:e.eventOrder,apis:r}},apis:{showAt:(e,t,o,n)=>{e.showAt(t,o,n)},showWithinBounds:(e,t,o,n,s)=>{e.showWithinBounds(t,o,n,s)},showMenuAt:(e,t,o,n)=>{e.showMenuAt(t,o,n)},showMenuWithinBounds:(e,t,o,n,s)=>{e.showMenuWithinBounds(t,o,n,s)},hide:(e,t)=>{e.hide(t)},isOpen:(e,t)=>e.isOpen(t),getContent:(e,t)=>e.getContent(t),setContent:(e,t,o)=>{e.setContent(t,o)},reposition:(e,t)=>{e.reposition(t)}}});var Uh=tinymce.util.Tools.resolve("tinymce.util.Delay");const Wh=fm({name:"Button",factory:e=>{const t=mh(e.action),o=e.dom.tag,n=t=>be(e.dom,"attributes").bind((e=>be(e,t)));return{uid:e.uid,dom:e.dom,components:e.components,events:t,behaviours:yu(e.buttonBehaviours,[oh.config({}),Pp.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:"button"===o?{type:n("type").getOr("button"),...n("role").map((e=>({role:e}))).getOr({})}:{role:e.role.getOr(n("role").getOr("button"))}},eventOrder:e.eventOrder}},configFields:[ys("uid",void 0),os("dom"),ys("components",[]),vu("buttonBehaviours",[oh,Pp]),us("action"),us("role"),ys("eventOrder",{})]}),jh=e=>{const t=(e=>void 0!==e.uid)(e)&&ye(e,"uid")?e.uid:ha("memento");return{get:e=>e.getSystem().getByUid(t).getOrDie(),getOpt:e=>e.getSystem().getByUid(t).toOptional(),asSpec:()=>({...e,uid:t})}};var Gh=tinymce.util.Tools.resolve("tinymce.util.I18n");const $h={indent:!0,outdent:!0,"table-insert-column-after":!0,"table-insert-column-before":!0,"paste-column-after":!0,"paste-column-before":!0,"unordered-list":!0,"list-bull-circle":!0,"list-bull-default":!0,"list-bull-square":!0},qh="temporary-placeholder",Xh=e=>()=>be(e,qh).getOr("!not found!"),Yh=(e,t)=>{const o=e.toLowerCase();if(Gh.isRtl()){const e=((e,t)=>Ae(e,t)?e:((e,t)=>e+t)(e,t))(o,"-rtl");return ve(t,e)?e:o}return o},Kh=(e,t)=>be(t,Yh(e,t)),Jh=(e,t)=>{const o=t();return Kh(e,o).getOrThunk(Xh(o))},Zh=()=>Jp("add-focusable",[Kr((e=>{gi(e.element,"svg").each((e=>kt(e,"focusable","false")))}))]),Qh=(e,t,o,n)=>{var s,r;const a=(e=>!!Gh.isRtl()&&ve($h,e))(t)?["tox-icon--flip"]:[],i=be(o,Yh(t,o)).or(n).getOrThunk(Xh(o));return{dom:{tag:e.tag,attributes:null!==(s=e.attributes)&&void 0!==s?s:{},classes:e.classes.concat(a),innerHtml:i},behaviours:kl([...null!==(r=e.behaviours)&&void 0!==r?r:[],Zh()])}},ef=(e,t,o,n=A.none())=>Qh(t,e,o(),n),tf={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},of=fm({name:"Notification",factory:e=>{const t=jh({dom:{tag:"p",innerHtml:e.translationProvider(e.text)},behaviours:kl([Kp.config({})])}),o=e=>({dom:{tag:"div",classes:["tox-bar"],styles:{width:`${e}%`}}}),n=e=>({dom:{tag:"div",classes:["tox-text"],innerHtml:`${e}%`}}),s=jh({dom:{tag:"div",classes:e.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(0)]},n(0)],behaviours:kl([Kp.config({})])}),r={updateProgress:(e,t)=>{e.getSystem().isConnected()&&s.getOpt(e).each((e=>{Kp.set(e,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[o(t)]},n(t)])}))},updateText:(e,o)=>{if(e.getSystem().isConnected()){const n=t.get(e);Kp.set(n,[ti(o)])}}},a=q([e.icon.toArray(),e.level.toArray(),e.level.bind((e=>A.from(tf[e]))).toArray()]),i=jh(Wh.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[ef("close",{tag:"span",classes:["tox-icon"],attributes:{"aria-label":e.translationProvider("Close")}},e.iconProvider)],action:t=>{e.onAction(t)}})),l=((e,t,o)=>{const n=o(),s=G(e,(e=>ve(n,Yh(e,n))));return Qh({tag:"div",classes:["tox-notification__icon"]},s.getOr(qh),n,A.none())})(a,0,e.iconProvider),c=[l,{dom:{tag:"div",classes:["tox-notification__body"]},components:[t.asSpec()],behaviours:kl([Kp.config({})])}];return{uid:e.uid,dom:{tag:"div",attributes:{role:"alert"},classes:e.level.map((e=>["tox-notification","tox-notification--in",`tox-notification--${e}`])).getOr(["tox-notification","tox-notification--in"])},behaviours:kl([oh.config({}),Jp("notification-events",[Ur(Xs(),(e=>{i.getOpt(e).each(oh.focus)}))])]),components:c.concat(e.progress?[s.asSpec()]:[]).concat(e.closeButton?[i.asSpec()]:[]),apis:r}},configFields:[us("level"),os("progress"),us("icon"),os("onAction"),os("text"),os("iconProvider"),os("translationProvider"),Cs("closeButton",!0)],apis:{updateProgress:(e,t,o)=>{e.updateProgress(t,o)},updateText:(e,t,o)=>{e.updateText(t,o)}}});var nf,sf,rf=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),af=tinymce.util.Tools.resolve("tinymce.EditorManager"),lf=tinymce.util.Tools.resolve("tinymce.Env");!function(e){e.default="wrap",e.floating="floating",e.sliding="sliding",e.scrolling="scrolling"}(nf||(nf={})),function(e){e.auto="auto",e.top="top",e.bottom="bottom"}(sf||(sf={}));const cf=e=>t=>t.options.get(e),df=e=>t=>A.from(e(t)),uf=e=>{const t=lf.deviceType.isPhone(),o=lf.deviceType.isTablet()||t,n=e.options.register,s=e=>r(e)||!1===e,a=e=>r(e)||h(e);n("skin",{processor:e=>r(e)||!1===e,default:"oxide"}),n("skin_url",{processor:"string"}),n("height",{processor:a,default:Math.max(e.getElement().offsetHeight,400)}),n("width",{processor:a,default:rf.DOM.getStyle(e.getElement(),"width")}),n("min_height",{processor:"number",default:100}),n("min_width",{processor:"number"}),n("max_height",{processor:"number"}),n("max_width",{processor:"number"}),n("style_formats",{processor:"object[]"}),n("style_formats_merge",{processor:"boolean",default:!1}),n("style_formats_autohide",{processor:"boolean",default:!1}),n("line_height_formats",{processor:"string",default:"1 1.1 1.2 1.3 1.4 1.5 2"}),n("font_family_formats",{processor:"string",default:"Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats"}),n("font_size_formats",{processor:"string",default:"8pt 10pt 12pt 14pt 18pt 24pt 36pt"}),n("font_size_input_default_unit",{processor:"string",default:"pt"}),n("block_formats",{processor:"string",default:"Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre"}),n("content_langs",{processor:"object[]"}),n("removed_menuitems",{processor:"string",default:""}),n("menubar",{processor:e=>r(e)||d(e),default:!t}),n("menu",{processor:"object",default:{}}),n("toolbar",{processor:e=>d(e)||r(e)||l(e)?{value:e,valid:!0}:{valid:!1,message:"Must be a boolean, string or array."},default:!0}),V(9,(e=>{n("toolbar"+(e+1),{processor:"string"})})),n("toolbar_mode",{processor:"string",default:o?"scrolling":"floating"}),n("toolbar_groups",{processor:"object",default:{}}),n("toolbar_location",{processor:"string",default:sf.auto}),n("toolbar_persist",{processor:"boolean",default:!1}),n("toolbar_sticky",{processor:"boolean",default:e.inline}),n("toolbar_sticky_offset",{processor:"number",default:0}),n("fixed_toolbar_container",{processor:"string",default:""}),n("fixed_toolbar_container_target",{processor:"object"}),n("ui_mode",{processor:"string",default:"combined"}),n("file_picker_callback",{processor:"function"}),n("file_picker_validator_handler",{processor:"function"}),n("file_picker_types",{processor:"string"}),n("typeahead_urls",{processor:"boolean",default:!0}),n("anchor_top",{processor:s,default:"#top"}),n("anchor_bottom",{processor:s,default:"#bottom"}),n("draggable_modal",{processor:"boolean",default:!1}),n("statusbar",{processor:"boolean",default:!0}),n("elementpath",{processor:"boolean",default:!0}),n("branding",{processor:"boolean",default:!0}),n("promotion",{processor:"boolean",default:!0}),n("resize",{processor:e=>"both"===e||d(e),default:!lf.deviceType.isTouch()}),n("sidebar_show",{processor:"string"}),n("help_accessibility",{processor:"boolean",default:e.hasPlugin("help")})},mf=cf("readonly"),gf=cf("height"),pf=cf("width"),hf=df(cf("min_width")),ff=df(cf("min_height")),bf=df(cf("max_width")),vf=df(cf("max_height")),yf=df(cf("style_formats")),xf=cf("style_formats_merge"),wf=cf("style_formats_autohide"),Sf=cf("content_langs"),kf=cf("removed_menuitems"),Cf=cf("toolbar_mode"),Of=cf("toolbar_groups"),_f=cf("toolbar_location"),Tf=cf("fixed_toolbar_container"),Ef=cf("fixed_toolbar_container_target"),Af=cf("toolbar_persist"),Mf=cf("toolbar_sticky_offset"),Df=cf("menubar"),Bf=cf("toolbar"),Ff=cf("file_picker_callback"),If=cf("file_picker_validator_handler"),Rf=cf("font_size_input_default_unit"),Nf=cf("file_picker_types"),Vf=cf("typeahead_urls"),zf=cf("anchor_top"),Hf=cf("anchor_bottom"),Lf=cf("draggable_modal"),Pf=cf("statusbar"),Uf=cf("elementpath"),Wf=cf("branding"),jf=cf("resize"),Gf=cf("paste_as_text"),$f=cf("sidebar_show"),qf=cf("promotion"),Xf=cf("help_accessibility"),Yf=e=>!1===e.options.get("skin"),Kf=e=>!1!==e.options.get("menubar"),Jf=e=>{const t=e.options.get("skin_url");if(Yf(e))return t;if(t)return e.documentBaseURI.toAbsolute(t);{const t=e.options.get("skin");return af.baseURL+"/skins/ui/"+t}},Zf=e=>e.options.get("line_height_formats").split(" "),Qf=e=>{const t=Bf(e),o=r(t),n=l(t)&&t.length>0;return!tb(e)&&(n||o||!0===t)},eb=e=>{const t=V(9,(t=>e.options.get("toolbar"+(t+1)))),o=U(t,r);return Ce(o.length>0,o)},tb=e=>eb(e).fold((()=>{const t=Bf(e);return f(t,r)&&t.length>0}),E),ob=e=>_f(e)===sf.bottom,nb=e=>{var t;if(!e.inline)return A.none();const o=null!==(t=Tf(e))&&void 0!==t?t:"";if(o.length>0)return pi(xt(),o);const n=Ef(e);return g(n)?A.some(Ve(n)):A.none()},sb=e=>e.inline&&nb(e).isSome(),rb=e=>nb(e).getOrThunk((()=>ft(ht(Ve(e.getElement()))))),ab=e=>e.inline&&!Kf(e)&&!Qf(e)&&!tb(e),ib=e=>(e.options.get("toolbar_sticky")||e.inline)&&!sb(e)&&!ab(e),lb=e=>!sb(e)&&"split"===e.options.get("ui_mode"),cb=e=>{const t=e.options.get("menu");return ce(t,(e=>({...e,items:e.items})))};var db=Object.freeze({__proto__:null,get ToolbarMode(){return nf},get ToolbarLocation(){return sf},register:uf,getSkinUrl:Jf,isReadOnly:mf,isSkinDisabled:Yf,getHeightOption:gf,getWidthOption:pf,getMinWidthOption:hf,getMinHeightOption:ff,getMaxWidthOption:bf,getMaxHeightOption:vf,getUserStyleFormats:yf,shouldMergeStyleFormats:xf,shouldAutoHideStyleFormats:wf,getLineHeightFormats:Zf,getContentLanguages:Sf,getRemovedMenuItems:kf,isMenubarEnabled:Kf,isMultipleToolbars:tb,isToolbarEnabled:Qf,isToolbarPersist:Af,getMultipleToolbarsOption:eb,getUiContainer:rb,useFixedContainer:sb,isSplitUiMode:lb,getToolbarMode:Cf,isDraggableModal:Lf,isDistractionFree:ab,isStickyToolbar:ib,getStickyToolbarOffset:Mf,getToolbarLocation:_f,isToolbarLocationBottom:ob,getToolbarGroups:Of,getMenus:cb,getMenubar:Df,getToolbar:Bf,getFilePickerCallback:Ff,getFilePickerTypes:Nf,useTypeaheadUrls:Vf,getAnchorTop:zf,getAnchorBottom:Hf,getFilePickerValidatorHandler:If,getFontSizeInputDefaultUnit:Rf,useStatusBar:Pf,useElementPath:Uf,promotionEnabled:qf,useBranding:Wf,getResize:jf,getPasteAsText:Gf,getSidebarShow:$f,useHelpAccessibility:Xf});const ub="[data-mce-autocompleter]",mb=e=>hi(e,ub);var gb;!function(e){e[e.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",e[e.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX"}(gb||(gb={}));var pb=gb;const hb="tox-menu-nav__js",fb="tox-collection__item",bb="tox-swatch",vb={normal:hb,color:bb},yb="tox-collection__item--enabled",xb="tox-collection__item-icon",wb="tox-collection__item-label",Sb="tox-collection__item-caret",kb="tox-collection__item--active",Cb="tox-collection__item-container",Ob="tox-collection__item-container--row",_b=e=>be(vb,e).getOr(hb),Tb=e=>"color"===e?"tox-swatches":"tox-menu",Eb=e=>({backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:Tb(e),tieredMenu:"tox-tiered-menu"}),Ab=e=>{const t=Eb(e);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:_b(e)}},Mb=(e,t,o)=>{const n=Eb(o);return{tag:"div",classes:q([[n.menu,`tox-menu-${t}-column`],e?[n.hasIcons]:[]])}},Db=[Dh.parts.items({})],Bb=(e,t,o)=>{const n=Eb(o);return{dom:{tag:"div",classes:q([[n.tieredMenu]])},markers:Ab(o)}},Fb=x([us("data"),ys("inputAttributes",{}),ys("inputStyles",{}),ys("tag","input"),ys("inputClasses",[]),Di("onSetValue"),ys("styles",{}),ys("eventOrder",{}),hu("inputBehaviours",[pu,oh]),ys("selectOnFocus",!0)]),Ib=e=>kl([oh.config({onFocus:e.selectOnFocus?e=>{const t=e.element,o=$a(t);t.dom.setSelectionRange(0,o.length)}:b})]),Rb=e=>({...Ib(e),...bu(e.inputBehaviours,[pu.config({store:{mode:"manual",...e.data.map((e=>({initialValue:e}))).getOr({}),getValue:e=>$a(e.element),setValue:(e,t)=>{$a(e.element)!==t&&qa(e.element,t)}},onSetValue:e.onSetValue})])}),Nb=e=>({tag:e.tag,attributes:{type:"text",...e.inputAttributes},styles:e.inputStyles,classes:e.inputClasses}),Vb=fm({name:"Input",configFields:Fb(),factory:(e,t)=>({uid:e.uid,dom:Nb(e),components:[],behaviours:Rb(e),eventOrder:e.eventOrder})}),zb=la("refetch-trigger-event"),Hb=la("redirect-menu-item-interaction"),Lb="tox-menu__searcher",Pb=e=>pi(e.element,`.${Lb}`).bind((t=>e.getSystem().getByDom(t).toOptional())),Ub=Pb,Wb=e=>({fetchPattern:pu.getValue(e),selectionStart:e.element.dom.selectionStart,selectionEnd:e.element.dom.selectionEnd}),jb=e=>{const t=(e,t)=>(t.cut(),A.none()),o=(e,t)=>{const o={interactionEvent:t.event,eventType:t.event.raw.type};return Ir(e,Hb,o),A.some(!0)},n="searcher-events";return{dom:{tag:"div",classes:[fb]},components:[Vb.sketch({inputClasses:[Lb,"tox-textfield"],inputAttributes:{...e.placeholder.map((t=>({placeholder:e.i18n(t)}))).getOr({}),type:"search","aria-autocomplete":"list"},inputBehaviours:kl([Jp(n,[Ur(Zs(),(e=>{Fr(e,zb)})),Ur(Ks(),((e,t)=>{"Escape"===t.event.raw.key&&t.stop()}))]),Pp.config({mode:"special",onLeft:t,onRight:t,onSpace:t,onEnter:o,onEscape:o,onUp:o,onDown:o})]),eventOrder:{keydown:[n,Pp.name()]}})]}},Gb="tox-collection--results__js",$b=e=>{var t;return e.dom?{...e,dom:{...e.dom,attributes:{...null!==(t=e.dom.attributes)&&void 0!==t?t:{},id:la("aria-item-search-result-id"),"aria-selected":"false"}}}:e},qb=(e,t)=>o=>{const n=z(o,t);return H(n,(t=>({dom:e,components:t})))},Xb=(e,t)=>{const o=[];let n=[];return L(e,((e,s)=>{t(e,s)?(n.length>0&&o.push(n),n=[],(ve(e.dom,"innerHtml")||e.components&&e.components.length>0)&&n.push(e)):n.push(e)})),n.length>0&&o.push(n),H(o,(e=>({dom:{tag:"div",classes:["tox-collection__group"]},components:e})))},Yb=(e,t,o)=>Dh.parts.items({preprocess:n=>{const s=H(n,o);return"auto"!==e&&e>1?qb({tag:"div",classes:["tox-collection__group"]},e)(s):Xb(s,((e,o)=>"separator"===t[o].type))}}),Kb=(e,t,o=!0)=>({dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[Yb(e,t,w)]}),Jb=e=>N(e,(e=>"icon"in e&&void 0!==e.icon)),Zb=e=>(console.error(Kn(e)),console.log(e),A.none()),Qb=(e,t,o,n,s)=>{const r=(a=o,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Dh.parts.items({preprocess:e=>Xb(e,((e,t)=>"separator"===a[t].type))})]});var a;return{value:e,dom:r.dom,components:r.components,items:o}},ev=(e,t,o,n,s)=>{if("color"===s.menuType){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Dh.parts.items({preprocess:"auto"!==e?qb({tag:"div",classes:["tox-swatches__row"]},e):w})]}]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType&&"auto"===n){const t=Kb(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("normal"===s.menuType||"searchable"===s.menuType){const t="searchable"!==s.menuType?Kb(n,o):"search-with-field"===s.searchMode.searchMode?((e,t,o)=>{const n=la("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===e?["tox-collection--list"]:["tox-collection--grid"])},components:[jb({i18n:Gh.translate,placeholder:o.placeholder}),{dom:{tag:"div",classes:[...1===e?["tox-collection--list"]:["tox-collection--grid"],Gb],attributes:{id:n}},components:[Yb(e,t,$b)]}]}})(n,o,s.searchMode):((e,t,o=!0)=>{const n=la("aria-controls-search-results");return{dom:{tag:"div",classes:["tox-menu","tox-collection",Gb].concat(1===e?["tox-collection--list"]:["tox-collection--grid"]),attributes:{id:n}},components:[Yb(e,t,$b)]}})(n,o);return{value:e,dom:t.dom,components:t.components,items:o}}if("listpreview"===s.menuType&&"auto"!==n){const t=(e=>({dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Dh.parts.items({preprocess:qb({tag:"div",classes:["tox-collection__group"]},e)})]}))(n);return{value:e,dom:t.dom,components:t.components,items:o}}return{value:e,dom:Mb(t,n,s.menuType),components:Db,items:o}},tv=rs("type"),ov=rs("name"),nv=rs("label"),sv=rs("text"),rv=rs("title"),av=rs("icon"),iv=rs("value"),lv=is("fetch"),cv=is("getSubmenuItems"),dv=is("onAction"),uv=is("onItemAction"),mv=Os("onSetup",(()=>b)),gv=ps("name"),pv=ps("text"),hv=ps("icon"),fv=ps("tooltip"),bv=ps("label"),vv=ps("shortcut"),yv=fs("select"),xv=Cs("active",!1),wv=Cs("borderless",!1),Sv=Cs("enabled",!0),kv=Cs("primary",!1),Cv=e=>ys("columns",e),Ov=ys("meta",{}),_v=Os("onAction",b),Tv=e=>Ss("type",e),Ev=e=>Qn("name","name",vn((()=>la(`${e}-name`))),Hn),Av=Dn([tv,pv]),Mv=Dn([Tv("autocompleteitem"),xv,Sv,Ov,iv,pv,hv]),Dv=[Sv,fv,hv,pv,mv],Bv=Dn([tv,dv].concat(Dv)),Fv=e=>qn("toolbarbutton",Bv,e),Iv=[xv].concat(Dv),Rv=Dn(Iv.concat([tv,dv])),Nv=e=>qn("ToggleButton",Rv,e),Vv=[Os("predicate",T),ks("scope","node",["node","editor"]),ks("position","selection",["node","selection","line"])],zv=Dv.concat([Tv("contextformbutton"),kv,dv,es("original",w)]),Hv=Iv.concat([Tv("contextformbutton"),kv,dv,es("original",w)]),Lv=Dv.concat([Tv("contextformbutton")]),Pv=Iv.concat([Tv("contextformtogglebutton")]),Uv=Jn("type",{contextformbutton:zv,contextformtogglebutton:Hv}),Wv=Dn([Tv("contextform"),Os("initValue",x("")),bv,ds("commands",Uv),ms("launch",Jn("type",{contextformbutton:Lv,contextformtogglebutton:Pv}))].concat(Vv)),jv=Dn([Tv("contexttoolbar"),rs("items")].concat(Vv)),Gv=[tv,rs("src"),ps("alt"),_s("classes",[],Hn)],$v=Dn(Gv),qv=[tv,sv,gv,_s("classes",["tox-collection__item-label"],Hn)],Xv=Dn(qv),Yv=En((()=>jn("type",{cardimage:$v,cardtext:Xv,cardcontainer:Kv}))),Kv=Dn([tv,Ss("direction","horizontal"),Ss("align","left"),Ss("valign","middle"),ds("items",Yv)]),Jv=[Sv,pv,vv,("menuitem",Qn("value","value",vn((()=>la("menuitem-value"))),Nn())),Ov];const Zv=Dn([tv,bv,ds("items",Yv),mv,_v].concat(Jv)),Qv=Dn([tv,xv,hv].concat(Jv)),ey=[tv,rs("fancytype"),_v],ty=[ys("initData",{})].concat(ey),oy=[fs("select"),Ts("initData",{},[Cs("allowCustomColors",!0),Ss("storageKey","default"),bs("colors",Nn())])].concat(ey),ny=Jn("fancytype",{inserttable:ty,colorswatch:oy}),sy=Dn([tv,mv,_v,hv].concat(Jv)),ry=Dn([tv,cv,mv,hv].concat(Jv)),ay=Dn([tv,hv,xv,mv,dv].concat(Jv)),iy=(e,t,o)=>{const n=Xc(e.element,"."+o);if(n.length>0){const e=$(n,(e=>{const o=e.dom.getBoundingClientRect().top,s=n[0].dom.getBoundingClientRect().top;return Math.abs(o-s)>t})).getOr(n.length);return A.some({numColumns:e,numRows:Math.ceil(n.length/e)})}return A.none()},ly=e=>((e,t)=>kl([Jp(e,t)]))(la("unnamed-events"),e),cy=la("tooltip.exclusive"),dy=la("tooltip.show"),uy=la("tooltip.hide"),my=(e,t,o)=>{e.getSystem().broadcastOn([cy],{})};var gy=Object.freeze({__proto__:null,hideAllExclusive:my,setComponents:(e,t,o,n)=>{o.getTooltip().each((e=>{e.getSystem().isConnected()&&Kp.set(e,n)}))}}),py=Object.freeze({__proto__:null,events:(e,t)=>{const o=o=>{t.getTooltip().each((n=>{Bd(n),e.onHide(o,n),t.clearTooltip()})),t.clearTimer()};return Hr(q([[Ur(dy,(o=>{t.resetTimer((()=>{(o=>{if(!t.isShowing()){my(o);const n=e.lazySink(o).getOrDie(),s=o.getSystem().build({dom:e.tooltipDom,components:e.tooltipComponents,events:Hr("normal"===e.mode?[Ur(qs(),(e=>{Fr(o,dy)})),Ur(Gs(),(e=>{Fr(o,uy)}))]:[]),behaviours:kl([Kp.config({})])});t.setTooltip(s),Ad(n,s),e.onShow(o,s),Sd.position(n,s,{anchor:e.anchor(o)})}})(o)}),e.delay)})),Ur(uy,(n=>{t.resetTimer((()=>{o(n)}),e.delay)})),Ur(dr(),((e,t)=>{const n=t;n.universal||R(n.channels,cy)&&o(e)})),Jr((e=>{o(e)}))],"normal"===e.mode?[Ur(Xs(),(e=>{Fr(e,dy)})),Ur(lr(),(e=>{Fr(e,uy)})),Ur(qs(),(e=>{Fr(e,dy)})),Ur(Gs(),(e=>{Fr(e,uy)}))]:[Ur(Dr(),((e,t)=>{Fr(e,dy)})),Ur(Br(),(e=>{Fr(e,uy)}))]]))}}),hy=[os("lazySink"),os("tooltipDom"),ys("exclusive",!0),ys("tooltipComponents",[]),ys("delay",300),ks("mode","normal",["normal","follow-highlight"]),ys("anchor",(e=>({type:"hotspot",hotspot:e,layouts:{onLtr:x([cl,ll,sl,al,rl,il]),onRtl:x([cl,ll,sl,al,rl,il])}}))),Di("onHide"),Di("onShow")],fy=Object.freeze({__proto__:null,init:()=>{const e=Ql(),t=Ql(),o=()=>{e.on(clearTimeout)},n=x("not-implemented");return _a({getTooltip:t.get,isShowing:t.isSet,setTooltip:t.set,clearTooltip:t.clear,clearTimer:o,resetTimer:(t,n)=>{o(),e.set(setTimeout(t,n))},readState:n})}});const by=Ol({fields:hy,name:"tooltipping",active:py,state:fy,apis:gy}),vy="silver.readonly",yy=Dn([("readonly",ns("readonly",Ln))]);const xy=(e,t)=>{const o=e.mainUi.outerContainer.element,n=[e.mainUi.mothership,...e.uiMotherships];t&&L(n,(e=>{e.broadcastOn([Yd()],{target:o})})),L(n,(e=>{e.broadcastOn([vy],{readonly:t})}))},wy=(e,t)=>{e.on("init",(()=>{e.mode.isReadOnly()&&xy(t,!0)})),e.on("SwitchMode",(()=>xy(t,e.mode.isReadOnly()))),mf(e)&&e.mode.set("readonly")},Sy=()=>Al.config({channels:{[vy]:{schema:yy,onReceive:(e,t)=>{Rm.set(e,t.readonly)}}}}),ky=e=>Rm.config({disabled:e}),Cy=e=>Rm.config({disabled:e,disableClass:"tox-tbtn--disabled"}),Oy=e=>Rm.config({disabled:e,disableClass:"tox-tbtn--disabled",useNative:!1}),_y=(e,t)=>{const o=e.getApi(t);return e=>{e(o)}},Ty=(e,t)=>Kr((o=>{_y(e,o)((o=>{const n=e.onSetup(o);p(n)&&t.set(n)}))})),Ey=(e,t)=>Jr((o=>_y(e,o)(t.get()))),Ay=(e,t)=>Qr(((o,n)=>{_y(e,o)(e.onAction),e.triggersSubmenu||t!==pb.CLOSE_ON_EXECUTE||(o.getSystem().isConnected()&&Fr(o,hr()),n.stop())})),My={[ur()]:["disabling","alloy.base.behaviour","toggling","item-events"]},Dy=we,By=(e,t,o,n)=>{const s=Es(b);return{type:"item",dom:t.dom,components:Dy(t.optComponents),data:e.data,eventOrder:My,hasSubmenu:e.triggersSubmenu,itemBehaviours:kl([Jp("item-events",[Ay(e,o),Ty(e,s),Ey(e,s)]),(r=()=>!e.enabled||n.isDisabled(),Rm.config({disabled:r,disableClass:"tox-collection__item--state-disabled"})),Sy(),Kp.config({})].concat(e.itemBehaviours))};var r},Fy=e=>({value:e.value,meta:{text:e.text.getOr(""),...e.meta}}),Iy=e=>{const t=lf.os.isMacOS()||lf.os.isiOS(),o=t?{alt:"\u2325",ctrl:"\u2303",shift:"\u21e7",meta:"\u2318",access:"\u2303\u2325"}:{meta:"Ctrl",access:"Shift+Alt"},n=e.split("+"),s=H(n,(e=>{const t=e.toLowerCase().trim();return ve(o,t)?o[t]:e}));return t?s.join(""):s.join("+")},Ry=(e,t,o=[xb])=>ef(e,{tag:"div",classes:o},t),Ny=e=>({dom:{tag:"div",classes:[wb]},components:[ti(Gh.translate(e))]}),Vy=(e,t)=>({dom:{tag:"div",classes:t,innerHtml:e}}),zy=(e,t)=>({dom:{tag:"div",classes:[wb]},components:[{dom:{tag:e.tag,styles:e.styles},components:[ti(Gh.translate(t))]}]}),Hy=e=>({dom:{tag:"div",classes:["tox-collection__item-accessory"]},components:[ti(Iy(e))]}),Ly=e=>Ry("checkmark",e,["tox-collection__item-checkmark"]),Py=e=>{const t=e.map((e=>({attributes:{title:Gh.translate(e),id:la("menu-item")}}))).getOr({});return{tag:"div",classes:[hb,fb],...t}},Uy=(e,t,o,n=A.none())=>"color"===e.presets?((e,t,o)=>{const n=e.ariaLabel,s=e.value,r=e.iconContent.map((e=>((e,t,o)=>{const n=t();return Kh(e,n).or(o).getOrThunk(Xh(n))})(e,t.icons,o)));return{dom:(()=>{const e=bb,o=r.getOr(""),a=n.map((e=>({title:t.translate(e)}))).getOr({}),i={tag:"div",attributes:a,classes:[e]};return"custom"===s?{...i,tag:"button",classes:[...i.classes,"tox-swatches__picker-btn"],innerHtml:o}:"remove"===s?{...i,classes:[...i.classes,"tox-swatch--remove"],innerHtml:o}:g(s)?{...i,attributes:{...i.attributes,"data-mce-color":s},styles:{"background-color":s},innerHtml:o}:i})(),optComponents:[]}})(e,t,n):((e,t,o,n)=>{const s={tag:"div",classes:[xb]},r=o?e.iconContent.map((e=>ef(e,s,t.icons,n))).orThunk((()=>A.some({dom:s}))):A.none(),a=e.checkMark,i=A.from(e.meta).fold((()=>Ny),(e=>ve(e,"style")?k(zy,e.style):Ny)),l=e.htmlContent.fold((()=>e.textContent.map(i)),(e=>A.some(Vy(e,[wb]))));return{dom:Py(e.ariaLabel),optComponents:[r,l,e.shortcutContent.map(Hy),a,e.caret]}})(e,t,o,n),Wy=(e,t)=>be(e,"tooltipWorker").map((e=>[by.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:e=>({type:"submenu",item:e,overrides:{maxHeightFunction:cc}}),mode:"follow-highlight",onShow:(t,o)=>{e((e=>{by.setComponents(t,[oi({element:Ve(e)})])}))}})])).getOr([]),jy=(e,t)=>{const o=(e=>rf.DOM.encode(e))(Gh.translate(e));if(t.length>0){const e=new RegExp((e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"))(t),"gi");return o.replace(e,(e=>`<span class="tox-autocompleter-highlight">${e}</span>`))}return o},Gy=(e,t)=>H(e,(e=>{switch(e.type){case"cardcontainer":return((e,t)=>{const o="vertical"===e.direction?"tox-collection__item-container--column":Ob,n="left"===e.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right";return{dom:{tag:"div",classes:[Cb,o,n,(()=>{switch(e.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}})()]},components:t}})(e,Gy(e.items,t));case"cardimage":return((e,t,o)=>({dom:{tag:"img",classes:t,attributes:{src:e,alt:o.getOr("")}}}))(e.src,e.classes,e.alt);case"cardtext":const o=e.name.exists((e=>R(t.cardText.highlightOn,e))),n=o?A.from(t.cardText.matchText).getOr(""):"";return Vy(jy(e.text,n),e.classes)}})),$y=Yu(Ch(),Oh()),qy=e=>({value:Jy(e)}),Xy=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Yy=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Ky=e=>Xy.test(e)||Yy.test(e),Jy=e=>_e(e,"#").toUpperCase(),Zy=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},Qy=e=>{const t=Zy(e.red)+Zy(e.green)+Zy(e.blue);return qy(t)},ex=Math.min,tx=Math.max,ox=Math.round,nx=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,sx=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,rx=(e,t,o,n)=>({red:e,green:t,blue:o,alpha:n}),ax=e=>{const t=parseInt(e,10);return t.toString()===e&&t>=0&&t<=255},ix=e=>{let t,o,n;const s=(e.hue||0)%360;let r=e.saturation/100,a=e.value/100;if(r=tx(0,ex(r,1)),a=tx(0,ex(a,1)),0===r)return t=o=n=ox(255*a),rx(t,o,n,1);const i=s/60,l=a*r,c=l*(1-Math.abs(i%2-1)),d=a-l;switch(Math.floor(i)){case 0:t=l,o=c,n=0;break;case 1:t=c,o=l,n=0;break;case 2:t=0,o=l,n=c;break;case 3:t=0,o=c,n=l;break;case 4:t=c,o=0,n=l;break;case 5:t=l,o=0,n=c;break;default:t=o=n=0}return t=ox(255*(t+d)),o=ox(255*(o+d)),n=ox(255*(n+d)),rx(t,o,n,1)},lx=e=>{const t=(e=>{const t=(e=>{const t=e.value.replace(Xy,((e,t,o,n)=>t+t+o+o+n+n));return{value:t}})(e),o=Yy.exec(t.value);return null===o?["FFFFFF","FF","FF","FF"]:o})(e),o=parseInt(t[1],16),n=parseInt(t[2],16),s=parseInt(t[3],16);return rx(o,n,s,1)},cx=(e,t,o,n)=>{const s=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),i=parseFloat(n);return rx(s,r,a,i)},dx=e=>{if("transparent"===e)return A.some(rx(0,0,0,0));const t=nx.exec(e);if(null!==t)return A.some(cx(t[1],t[2],t[3],"1"));const o=sx.exec(e);return null!==o?A.some(cx(o[1],o[2],o[3],o[4])):A.none()},ux=e=>`rgba(${e.red},${e.green},${e.blue},${e.alpha})`,mx=rx(255,0,0,1),gx=(e,t)=>{e.dispatch("ResizeContent",t)},px=(e,t)=>{e.dispatch("TextColorChange",t)},hx=(e,t)=>e.dispatch("ResolveName",{name:t.nodeName.toLowerCase(),target:t}),fx=(e,t)=>()=>{e(),t()},bx=e=>yx(e,"NodeChange",(t=>{t.setEnabled(e.selection.isEditable())})),vx=(e,t)=>o=>{const n=bx(e)(o),s=((e,t)=>o=>{const n=Zl(),s=()=>{o.setActive(e.formatter.match(t));const s=e.formatter.formatChanged(t,o.setActive);n.set(s)};return e.initialized?s():e.once("init",s),()=>{e.off("init",s),n.clear()}})(e,t)(o);return()=>{n(),s()}},yx=(e,t,o)=>n=>{const s=()=>o(n),r=()=>{o(n),e.on(t,s)};return e.initialized?r():e.once("init",r),()=>{e.off("init",r),e.off(t,s)}},xx=e=>t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("mceToggleFormat",!1,t.format)}))},wx=(e,t)=>()=>e.execCommand(t);var Sx=tinymce.util.Tools.resolve("tinymce.util.LocalStorage");const kx={},Cx=e=>be(kx,e).getOrThunk((()=>{const t=`tinymce-custom-colors-${e}`,o=Sx.getItem(t);if(m(o)){const e=Sx.getItem("tinymce-custom-colors");Sx.setItem(t,g(e)?e:"[]")}const n=((e,t=10)=>{const o=Sx.getItem(e),n=r(o)?JSON.parse(o):[],s=t-(a=n).length<0?a.slice(0,t):a;var a;const i=e=>{s.splice(e,1)};return{add:o=>{I(s,o).each(i),s.unshift(o),s.length>t&&s.pop(),Sx.setItem(e,JSON.stringify(s))},state:()=>s.slice(0)}})(t,10);return kx[e]=n,n})),Ox=(e,t)=>{Cx(e).add(t)},_x=(e,t,o)=>({hue:e,saturation:t,value:o}),Tx=e=>{let t=0,o=0,n=0;const s=e.red/255,r=e.green/255,a=e.blue/255,i=Math.min(s,Math.min(r,a)),l=Math.max(s,Math.max(r,a));return i===l?(n=i,_x(0,0,100*n)):(t=s===i?3:a===i?1:5,t=60*(t-(s===i?r-a:a===i?s-r:a-s)/(l-i)),o=(l-i)/l,n=l,_x(Math.round(t),Math.round(100*o),Math.round(100*n)))},Ex=e=>Qy(ix(e)),Ax=e=>{return(t=e,Ky(t)?A.some({value:Jy(t)}):A.none()).orThunk((()=>dx(e).map(Qy))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const o=t.getContext("2d");o.clearRect(0,0,t.width,t.height),o.fillStyle="#FFFFFF",o.fillStyle=e,o.fillRect(0,0,1,1);const n=o.getImageData(0,0,1,1).data,s=n[0],r=n[1],a=n[2],i=n[3];return Qy(rx(s,r,a,i))}));var t},Mx="forecolor",Dx="hilitecolor",Bx=e=>{const t=[];for(let o=0;o<e.length;o+=2)t.push({text:e[o+1],value:"#"+Ax(e[o]).value,icon:"checkmark",type:"choiceitem"});return t},Fx=e=>t=>t.options.get(e),Ix="#000000",Rx=(e,t)=>t===Mx&&e.options.isSet("color_map_foreground")?Fx("color_map_foreground")(e):t===Dx&&e.options.isSet("color_map_background")?Fx("color_map_background")(e):Fx("color_map")(e),Nx=(e,t="default")=>Math.max(5,Math.ceil(Math.sqrt(Rx(e,t).length))),Vx=(e,t)=>{const o=Fx("color_cols")(e),n=Nx(e,t);return o===Nx(e)?n:o},zx=(e,t="default")=>Math.round(t===Mx?Fx("color_cols_foreground")(e):t===Dx?Fx("color_cols_background")(e):Fx("color_cols")(e)),Hx=Fx("custom_colors"),Lx=Fx("color_default_foreground"),Px=Fx("color_default_background"),Ux=(e,t)=>{const o=Ve(e.selection.getStart()),n="hilitecolor"===t?Is(o,(e=>{if(Ge(e)){const t=It(e,"background-color");return Ce(dx(t).exists((e=>0!==e.alpha)),t)}return A.none()})).getOr("rgba(0, 0, 0, 0)"):It(o,"color");return dx(n).map((e=>"#"+Qy(e).value))},Wx=e=>{const t="choiceitem",o={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return e?[o,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[o]},jx=(e,t,o,n)=>{"custom"===o?Jx(e)((o=>{o.each((o=>{Ox(t,o),e.execCommand("mceApplyTextcolor",t,o),n(o)}))}),Ux(e,t).getOr(Ix)):"remove"===o?(n(""),e.execCommand("mceRemoveTextcolor",t)):(n(o),e.execCommand("mceApplyTextcolor",t,o))},Gx=(e,t,o)=>e.concat((e=>H(Cx(e).state(),(e=>({type:"choiceitem",text:e,icon:"checkmark",value:e}))))(t).concat(Wx(o))),$x=(e,t,o)=>n=>{n(Gx(e,t,o))},qx=(e,t,o)=>{const n="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";e.setIconFill(n,o)},Xx=(e,t)=>o=>{const n=Ux(e,t);return xe(n,o.toUpperCase())},Yx=(e,t,o,n,s)=>{e.ui.registry.addSplitButton(t,{tooltip:n,presets:"color",icon:"forecolor"===t?"text-color":"highlight-bg-color",select:Xx(e,o),columns:zx(e,o),fetch:$x(Rx(e,o),o,Hx(e)),onAction:t=>{jx(e,o,s.get(),b)},onItemAction:(n,r)=>{jx(e,o,r,(o=>{s.set(o),px(e,{name:t,color:o})}))},onSetup:o=>{qx(o,t,s.get());const n=e=>{e.name===t&&qx(o,e.name,e.color)};return e.on("TextColorChange",n),fx(bx(e)(o),(()=>{e.off("TextColorChange",n)}))}})},Kx=(e,t,o,n,s)=>{e.ui.registry.addNestedMenuItem(t,{text:n,icon:"forecolor"===t?"text-color":"highlight-bg-color",onSetup:o=>(qx(o,t,s.get()),bx(e)(o)),getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"colorswatch",select:Xx(e,o),initData:{storageKey:o},onAction:n=>{jx(e,o,n.value,(o=>{s.set(o),px(e,{name:t,color:o})}))}}]})},Jx=e=>(t,o)=>{let n=!1;const s={colorpicker:o};e.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s,onAction:(e,t)=>{"hex-valid"===t.name&&(n=t.value)},onSubmit:o=>{const s=o.getData().colorpicker;n?(t(A.from(s)),o.close()):e.windowManager.alert(e.translate(["Invalid hex color code: {0}",s]))},onClose:b,onCancel:()=>{t(A.none())}})},Zx=(e,t,o,n,s,r,a,i)=>{const l=Jb(t),c=Qx(t,o,n,"color"!==s?"normal":"color",r,a,i);return ev(e,l,c,n,{menuType:s})},Qx=(e,t,o,n,s,r,a)=>we(H(e,(i=>{return"choiceitem"===i.type?(l=i,qn("choicemenuitem",Qv,l)).fold(Zb,(i=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Uy({presets:o,textContent:t?e.text:A.none(),htmlContent:A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:t?e.shortcut:A.none(),checkMark:t?A.some(Ly(a.icons)):A.none(),caret:A.none(),value:e.value},a,i);return fn(By({data:Fy(e),enabled:e.enabled,getApi:e=>({setActive:t=>{dh.set(e,t)},isActive:()=>dh.isOn(e),isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t)}),onAction:t=>n(e.value),onSetup:e=>(e.setActive(s),b),triggersSubmenu:!1,itemBehaviours:[]},l,r,a),{toggling:{toggleClass:yb,toggleOnExecute:!1,selected:e.active,exclusive:!0}})})(i,1===o,n,t,r(i.value),s,a,Jb(e))))):A.none();var l}))),ew=(e,t)=>{const o=Ab(t);return 1===e?{mode:"menu",moveOnTab:!0}:"auto"===e?{mode:"grid",selector:"."+o.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group"),previousSelector:e=>"color"===t?pi(e.element,"[aria-checked=true]"):A.none()}},tw=la("cell-over"),ow=la("cell-execute"),nw=(e,t,o)=>{const n=o=>Ir(o,ow,{row:e,col:t}),s=(e,t)=>{t.stop(),n(e)};return ri({dom:{tag:"div",attributes:{role:"button","aria-label":o}},behaviours:kl([Jp("insert-table-picker-cell",[Ur(qs(),oh.focus),Ur(ur(),n),Ur(er(),s),Ur(gr(),s)]),dh.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),oh.config({onFocus:o=>Ir(o,tw,{row:e,col:t})})])})},sw=e=>X(e,(e=>H(e,ai))),rw=(e,t)=>ti(`${t}x${e}`),aw={inserttable:(e,t)=>{const o=(e=>(t,o)=>e.shared.providers.translate(`${o} columns, ${t} rows`))(t),n=((e,t,o)=>{const n=[];for(let t=0;t<10;t++){const o=[];for(let n=0;n<10;n++){const s=e(t+1,n+1);o.push(nw(t,n,s))}n.push(o)}return n})(o),s=rw(0,0),r=jh({dom:{tag:"span",classes:["tox-insert-table-picker__label"]},components:[s],behaviours:kl([Kp.config({})])});return{type:"widget",data:{value:la("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[$y.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:sw(n).concat(r.asSpec()),behaviours:kl([Jp("insert-table-picker",[Kr((e=>{Kp.set(r.get(e),[s])})),$r(tw,((e,t,o)=>{const{row:s,col:a}=o.event;((e,t,o,n,s)=>{for(let n=0;n<10;n++)for(let s=0;s<10;s++)dh.set(e[n][s],n<=t&&s<=o)})(n,s,a),Kp.set(r.get(e),[rw(s+1,a+1)])})),$r(ow,((t,o,n)=>{const{row:s,col:r}=n.event;e.onAction({numRows:s+1,numColumns:r+1}),Fr(t,hr())}))]),Pp.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:(e,t)=>{const o=((e,t)=>{const o=e.initData.allowCustomColors&&t.colorinput.hasCustomColors();return e.initData.colors.fold((()=>Gx(t.colorinput.getColors(e.initData.storageKey),e.initData.storageKey,o)),(e=>e.concat(Wx(o))))})(e,t),n=t.colorinput.getColorCols(e.initData.storageKey),s="color",r={...Zx(la("menu-value"),o,(t=>{e.onAction({value:t})}),n,s,pb.CLOSE_ON_EXECUTE,e.select.getOr(T),t.shared.providers),markers:Ab(s),movement:ew(n,s)};return{type:"widget",data:{value:la("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[$y.widget(Dh.sketch(r))]}}},iw=e=>({type:"separator",dom:{tag:"div",classes:[fb,"tox-collection__group-heading"]},components:e.text.map(ti).toArray()});var lw=Object.freeze({__proto__:null,getCoupled:(e,t,o,n)=>o.getOrCreate(e,t,n),getExistingCoupled:(e,t,o,n)=>o.getExisting(e,t,n)}),cw=[ns("others",$n(sn.value,Nn()))],dw=Object.freeze({__proto__:null,init:()=>{const e={},t=(t,o)=>{if(0===ae(t.others).length)throw new Error("Cannot find any known coupled components");return be(e,o)},o=x({});return _a({readState:o,getExisting:(e,o,n)=>t(o,n).orThunk((()=>(be(o.others,n).getOrDie("No information found for coupled component: "+n),A.none()))),getOrCreate:(o,n,s)=>t(n,s).getOrThunk((()=>{const t=be(n.others,s).getOrDie("No information found for coupled component: "+s)(o),r=o.getSystem().build(t);return e[s]=r,r}))})}});const uw=Ol({fields:cw,name:"coupling",apis:lw,state:dw}),mw=e=>{let t=A.none(),o=[];const n=e=>{s()?r(e):o.push(e)},s=()=>t.isSome(),r=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{s()||(t=A.some(e),L(o,r),o=[])})),{get:n,map:e=>mw((t=>{n((o=>{t(e(o))}))})),isReady:s}},gw={nu:mw,pure:e=>mw((t=>{t(e)}))},pw=e=>{setTimeout((()=>{throw e}),0)},hw=e=>{const t=t=>{e().then(t,pw)};return{map:t=>hw((()=>e().then(t))),bind:t=>hw((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>hw((()=>e().then((()=>t.toPromise())))),toLazy:()=>gw.nu(t),toCached:()=>{let t=null;return hw((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},fw=e=>hw((()=>new Promise(e))),bw=e=>hw((()=>Promise.resolve(e))),vw=x("sink"),yw=x(ju({name:vw(),overrides:x({dom:{tag:"div"},behaviours:kl([Sd.config({useFixed:E})]),events:Hr([qr(Ks()),qr(Ws()),qr(er())])})})),xw=(e,t)=>{const o=e.getHotspot(t).getOr(t),n="hotspot",s=e.getAnchorOverrides();return e.layouts.fold((()=>({type:n,hotspot:o,overrides:s})),(e=>({type:n,hotspot:o,overrides:s,layouts:e})))},ww=(e,t,o,n,s,r,a)=>{const i=((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>(0,e.fetch)(o).map(t))(e,t,n),l=Cw(n,e);return i.map((e=>e.bind((e=>A.from(Lh.sketch({...r.menu(),uid:ha(""),data:e,highlightOnOpen:a,onOpenMenu:(e,t)=>{const n=l().getOrDie();Sd.position(n,t,{anchor:o}),Xd.decloak(s)},onOpenSubmenu:(e,t,o)=>{const n=l().getOrDie();Sd.position(n,o,{anchor:{type:"submenu",item:t}}),Xd.decloak(s)},onRepositionMenu:(e,t,n)=>{const s=l().getOrDie();Sd.position(s,t,{anchor:o}),L(n,(e=>{Sd.position(s,e.triggeredMenu,{anchor:{type:"submenu",item:e.triggeringItem}})}))},onEscape:()=>(oh.focus(n),Xd.close(s),A.some(!0))}))))))})(e,t,xw(e,o),o,n,s,a);return i.map((e=>(e.fold((()=>{Xd.isOpen(n)&&Xd.close(n)}),(e=>{Xd.cloak(n),Xd.open(n,e),r(n)})),n)))},Sw=(e,t,o,n,s,r,a)=>(Xd.close(n),bw(n)),kw=(e,t,o,n,s,r)=>{const a=uw.getCoupled(o,"sandbox");return(Xd.isOpen(a)?Sw:ww)(e,t,o,a,n,s,r)},Cw=(e,t)=>e.getSystem().getByUid(t.uid+"-"+vw()).map((e=>()=>sn.value(e))).getOrThunk((()=>t.lazySink.fold((()=>()=>sn.error(new Error("No internal sink is specified, nor could an external sink be found"))),(t=>()=>t(e))))),Ow=e=>{Xd.getState(e).each((e=>{Lh.repositionMenus(e)}))},_w=(e,t,o)=>{const n=bi(),s=Cw(t,e);return{dom:{tag:"div",classes:e.sandboxClasses,attributes:{id:n.id,role:"listbox"}},behaviours:yu(e.sandboxBehaviours,[pu.config({store:{mode:"memory",initialValue:t}}),Xd.config({onOpen:(s,r)=>{const a=xw(e,t);n.link(t.element),e.matchWidth&&((e,t,o)=>{const n=wm.getCurrent(t).getOr(t),s=Jt(e.element);o?Dt(n.element,"min-width",s+"px"):((e,t)=>{Kt.set(e,t)})(n.element,s)})(a.hotspot,r,e.useMinWidth),e.onOpen(a,s,r),void 0!==o&&void 0!==o.onOpen&&o.onOpen(s,r)},onClose:(e,s)=>{n.unlink(t.element),void 0!==o&&void 0!==o.onClose&&o.onClose(e,s)},isPartOf:(e,o,n)=>vi(o,n)||vi(t,n),getAttachPoint:()=>s().getOrDie()}),wm.config({find:e=>Xd.getState(e).bind((e=>wm.getCurrent(e)))}),Al.config({channels:{...Qd({isExtraPart:T}),...tu({doReposition:Ow})}})])}},Tw=e=>{const t=uw.getCoupled(e,"sandbox");Ow(t)},Ew=()=>[ys("sandboxClasses",[]),vu("sandboxBehaviours",[wm,Al,Xd,pu])],Aw=x([os("dom"),os("fetch"),Di("onOpen"),Bi("onExecute"),ys("getHotspot",A.some),ys("getAnchorOverrides",x({})),wc(),hu("dropdownBehaviours",[dh,uw,Pp,oh]),os("toggleClass"),ys("eventOrder",{}),us("lazySink"),ys("matchWidth",!1),ys("useMinWidth",!1),us("role")].concat(Ew())),Mw=x([Wu({schema:[Ei(),ys("fakeFocus",!1)],name:"menu",defaults:e=>({onExecute:e.onExecute})}),yw()]),Dw=bm({name:"Dropdown",configFields:Aw(),partFields:Mw(),factory:(e,t,o,n)=>{const s=e=>{Xd.getState(e).each((e=>{Lh.highlightPrimary(e)}))},r=(t,o,s)=>kw(e,w,t,n,o,s),a={expand:e=>{dh.isOn(e)||r(e,b,zh.HighlightNone).get(b)},open:e=>{dh.isOn(e)||r(e,b,zh.HighlightMenuAndItem).get(b)},refetch:t=>uw.getExistingCoupled(t,"sandbox").fold((()=>r(t,b,zh.HighlightMenuAndItem).map(b)),(o=>ww(e,w,t,o,n,b,zh.HighlightMenuAndItem).map(b))),isOpen:dh.isOn,close:e=>{dh.isOn(e)&&r(e,b,zh.HighlightMenuAndItem).get(b)},repositionMenus:e=>{dh.isOn(e)&&Tw(e)}},i=(e,t)=>(Rr(e),A.some(!0));return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.dropdownBehaviours,[dh.config({toggleClass:e.toggleClass,aria:{mode:"expanded"}}),uw.config({others:{sandbox:t=>_w(e,t,{onOpen:()=>dh.on(t),onClose:()=>dh.off(t)})}}),Pp.config({mode:"special",onSpace:i,onEnter:i,onDown:(e,t)=>{if(Dw.isOpen(e)){const t=uw.getCoupled(e,"sandbox");s(t)}else Dw.open(e);return A.some(!0)},onEscape:(e,t)=>Dw.isOpen(e)?(Dw.close(e),A.some(!0)):A.none()}),oh.config({})]),events:mh(A.some((e=>{r(e,s,zh.HighlightMenuAndItem).get(b)}))),eventOrder:{...e.eventOrder,[ur()]:["disabling","toggling","alloy.base.behaviour"]},apis:a,domModification:{attributes:{"aria-haspopup":"true",...e.role.fold((()=>({})),(e=>({role:e}))),..."button"===e.dom.tag?{type:("type",be(e.dom,"attributes").bind((e=>be(e,"type")))).getOr("button")}:{}}}}},apis:{open:(e,t)=>e.open(t),refetch:(e,t)=>e.refetch(t),expand:(e,t)=>e.expand(t),close:(e,t)=>e.close(t),isOpen:(e,t)=>e.isOpen(t),repositionMenus:(e,t)=>e.repositionMenus(t)}}),Bw=(e,t,o)=>{Ub(e).each((e=>{var n;((e,t)=>{_t(t.element,"id").each((t=>kt(e.element,"aria-activedescendant",t)))})(e,o),(Ua((n=t).element,Gb)?A.some(n.element):pi(n.element,"."+Gb)).each((t=>{_t(t,"id").each((t=>kt(e.element,"aria-controls",t)))}))})),kt(o.element,"aria-selected","true")},Fw=(e,t,o)=>{kt(o.element,"aria-selected","false")},Iw=e=>uw.getExistingCoupled(e,"sandbox").bind(Pb).map(Wb).map((e=>e.fetchPattern)).getOr("");var Rw;!function(e){e[e.ContentFocus=0]="ContentFocus",e[e.UiFocus=1]="UiFocus"}(Rw||(Rw={}));const Nw=(e,t,o,n,s)=>{const r=o.shared.providers,a=e=>s?{...e,shortcut:A.none(),icon:e.text.isSome()?A.none():e.icon}:e;switch(e.type){case"menuitem":return(i=e,qn("menuitem",sy,i)).fold(Zb,(e=>A.some(((e,t,o,n=!0)=>{const s=Uy({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.none(),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return By({data:Fy(e),getApi:e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t)}),enabled:e.enabled,onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o)})(a(e),t,r,n))));case"nestedmenuitem":return(e=>qn("nestedmenuitem",ry,e))(e).fold(Zb,(e=>A.some(((e,t,o,n=!0,s=!1)=>{const r=s?(a=o.icons,Ry("chevron-down",a,[Sb])):(e=>Ry("chevron-right",e,[Sb]))(o.icons);var a;const i=Uy({presets:"normal",iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,caret:A.some(r),checkMark:A.none(),shortcutContent:e.shortcut},o,n);return By({data:Fy(e),getApi:e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setIconFill:(t,o)=>{pi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{kt(e,"fill",o)}))}}),enabled:e.enabled,onAction:b,onSetup:e.onSetup,triggersSubmenu:!0,itemBehaviours:[]},i,t,o)})(a(e),t,r,n,s))));case"togglemenuitem":return(e=>qn("togglemenuitem",ay,e))(e).fold(Zb,(e=>A.some(((e,t,o,n=!0)=>{const s=Uy({iconContent:e.icon,textContent:e.text,htmlContent:A.none(),ariaLabel:e.text,checkMark:A.some(Ly(o.icons)),caret:A.none(),shortcutContent:e.shortcut,presets:"normal",meta:e.meta},o,n);return fn(By({data:Fy(e),enabled:e.enabled,getApi:e=>({setActive:t=>{dh.set(e,t)},isActive:()=>dh.isOn(e),isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t)}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:[]},s,t,o),{toggling:{toggleClass:yb,toggleOnExecute:!1,selected:e.active}})})(a(e),t,r,n))));case"separator":return(e=>qn("separatormenuitem",Av,e))(e).fold(Zb,(e=>A.some(iw(e))));case"fancymenuitem":return(e=>qn("fancymenuitem",ny,e))(e).fold(Zb,(e=>((e,t)=>be(aw,e.fancytype).map((o=>o(e,t))))(e,o)));default:return console.error("Unknown item in general menu",e),A.none()}var i},Vw=(e,t,o,n,s,r,a)=>{const i=1===n,l=!i||Jb(e);return we(H(e,(e=>{switch(e.type){case"separator":return(n=e,qn("Autocompleter.Separator",Av,n)).fold(Zb,(e=>A.some(iw(e))));case"cardmenuitem":return(e=>qn("cardmenuitem",Zv,e))(e).fold(Zb,(e=>A.some(((e,t,o,n)=>{const s={dom:Py(e.label),optComponents:[A.some({dom:{tag:"div",classes:[Cb,Ob]},components:Gy(e.items,n)})]};return By({data:Fy({text:A.none(),...e}),enabled:e.enabled,getApi:e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>{Rm.set(e,!t),L(Xc(e.element,"*"),(o=>{e.getSystem().getByDom(o).each((e=>{e.hasConfigured(Rm)&&Rm.set(e,!t)}))}))}}),onAction:e.onAction,onSetup:e.onSetup,triggersSubmenu:!1,itemBehaviours:A.from(n.itemBehaviours).getOr([])},s,t,o.providers)})({...e,onAction:t=>{e.onAction(t),o(e.value,e.meta)}},s,r,{itemBehaviours:Wy(e.meta,r),cardText:{matchText:t,highlightOn:a}}))));default:return(e=>qn("Autocompleter.Item",Mv,e))(e).fold(Zb,(e=>A.some(((e,t,o,n,s,r,a,i=!0)=>{const l=Uy({presets:n,textContent:A.none(),htmlContent:o?e.text.map((e=>jy(e,t))):A.none(),ariaLabel:e.text,iconContent:e.icon,shortcutContent:A.none(),checkMark:A.none(),caret:A.none(),value:e.value},a.providers,i,e.icon);return By({data:Fy(e),enabled:e.enabled,getApi:x({}),onAction:t=>s(e.value,e.meta),onSetup:x(b),triggersSubmenu:!1,itemBehaviours:Wy(e.meta,a)},l,r,a.providers)})(e,t,i,"normal",o,s,r,l))))}var n})))},zw=(e,t,o,n,s,r)=>{const a=Jb(t),i=we(H(t,(e=>{const t=e=>Nw(e,o,n,(e=>s?!ve(e,"text"):a)(e),s);return"nestedmenuitem"===e.type&&e.getSubmenuItems().length<=0?t({...e,enabled:!1}):t(e)}))),l=(e=>"no-search"===e.searchMode?{menuType:"normal"}:{menuType:"searchable",searchMode:e})(r);return(s?Qb:ev)(e,a,i,1,l)},Hw=e=>Lh.singleData(e.value,e),Lw=(e,t)=>{const o=Es(!1),n=Es(!1),s=ri(Ph.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:kl([Jp("dismissAutocompleter",[Ur(Cr(),(()=>c()))])]),lazySink:t.getSink})),r=()=>Ph.isOpen(s),a=n.get,i=()=>{r()&&Ph.hide(s)},l=()=>Ph.getContent(s).bind((e=>te(e.components(),0))),c=()=>e.execCommand("mceAutocompleterClose"),d=n=>{const r=(n=>{const s=re(n,(e=>A.from(e.columns))).getOr(1);return X(n,(n=>{const r=n.items;return Vw(r,n.matchText,((t,s)=>{const r=e.selection.getRng();((e,t)=>mb(Ve(t.startContainer)).map((t=>{const o=e.createRng();return o.selectNode(t.dom),o})))(e.dom,r).each((r=>{const a={hide:()=>c(),reload:t=>{i(),e.execCommand("mceAutocompleterReload",!1,{fetchOptions:t})}};o.set(!0),n.onAction(a,r,t,s),o.set(!1)}))}),s,pb.BUBBLE_TO_SANDBOX,t,n.highlightOn)}))})(n);r.length>0?((t,o)=>{var n;(n=Ve(e.getBody()),pi(n,ub)).each((n=>{const r=re(t,(e=>A.from(e.columns))).getOr(1);Ph.showMenuAt(s,{anchor:{type:"node",root:Ve(e.getBody()),node:A.from(n)}},((e,t,o,n)=>{const s=ew(t,n),r=Ab(n);return{data:Hw({...e,movement:s,menuBehaviours:ly("auto"!==t?[]:[Kr(((e,t)=>{iy(e,4,r.item).each((({numColumns:t,numRows:o})=>{Pp.setGridSize(e,o,t)}))}))])}),menu:{markers:Ab(n),fakeFocus:o===Rw.ContentFocus}}})(ev("autocompleter-value",!0,o,r,{menuType:"normal"}),r,Rw.ContentFocus,"normal"))})),l().each(Gm.highlightFirst)})(n,r):i()};e.on("AutocompleterStart",(({lookupData:e})=>{n.set(!0),o.set(!1),d(e)})),e.on("AutocompleterUpdate",(({lookupData:e})=>d(e))),e.on("AutocompleterEnd",(()=>{i(),n.set(!1),o.set(!1)}));((e,t)=>{const o=(e,t)=>{Ir(e,Ks(),{raw:t})},n=()=>e.getMenu().bind(Gm.getHighlighted);t.on("keydown",(t=>{const s=t.which;e.isActive()&&(e.isMenuOpen()?13===s?(n().each(Rr),t.preventDefault()):40===s?(n().fold((()=>{e.getMenu().each(Gm.highlightFirst)}),(e=>{o(e,t)})),t.preventDefault(),t.stopImmediatePropagation()):37!==s&&38!==s&&39!==s||n().each((e=>{o(e,t),t.preventDefault(),t.stopImmediatePropagation()})):13!==s&&38!==s&&40!==s||e.cancelIfNecessary())})),t.on("NodeChange",(t=>{e.isActive()&&!e.isProcessingAction()&&mb(Ve(t.element)).isNone()&&e.cancelIfNecessary()}))})({cancelIfNecessary:c,isMenuOpen:r,isActive:a,isProcessingAction:o.get,getMenu:l},e)},Pw=["visible","hidden","clip"],Uw=e=>Me(e).length>0&&!R(Pw,e),Ww=e=>{if(je(e)){const t=It(e,"overflow-x"),o=It(e,"overflow-y");return Uw(t)||Uw(o)}return!1},jw=(e,t)=>lb(e)?(e=>{const t=qc(e,Ww),o=0===t.length?bt(e).map(vt).map((e=>qc(e,Ww))).getOr([]):t;return oe(o).map((e=>({element:e,others:o.slice(1)})))})(t):A.none(),Gw=e=>{const t=[...H(e.others,Jo),en()];return((e,t)=>j(t,((e,t)=>Qo(e,t)),e))(Jo(e.element),t)},$w=(e,t,o)=>hi(e,t,o).isSome(),qw=(e,t)=>{let o=null;return{cancel:()=>{null!==o&&(clearTimeout(o),o=null)},schedule:(...n)=>{o=setTimeout((()=>{e.apply(null,n),o=null}),t)}}},Xw=e=>{const t=e.raw;return void 0===t.touches||1!==t.touches.length?A.none():A.some(t.touches[0])},Yw=(e,t)=>{const o={stopBackspace:!0,...t},n=(e=>{const t=Ql(),o=Es(!1),n=qw((t=>{e.triggerEvent(pr(),t),o.set(!0)}),400),s=Ds([{key:Hs(),value:e=>(Xw(e).each((s=>{n.cancel();const r={x:s.clientX,y:s.clientY,target:e.target};n.schedule(e),o.set(!1),t.set(r)})),A.none())},{key:Ls(),value:e=>(n.cancel(),Xw(e).each((e=>{t.on((o=>{((e,t)=>{const o=Math.abs(e.clientX-t.x),n=Math.abs(e.clientY-t.y);return o>5||n>5})(e,o)&&t.clear()}))})),A.none())},{key:Ps(),value:s=>(n.cancel(),t.get().filter((e=>Ze(e.target,s.target))).map((t=>o.get()?(s.prevent(),!1):e.triggerEvent(gr(),s))))}]);return{fireIfReady:(e,t)=>be(s,t).bind((t=>t(e)))}})(o),s=H(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","transitioncancel","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),(t=>tc(e,t,(e=>{n.fireIfReady(e,t).each((t=>{t&&e.kill()})),o.triggerEvent(t,e)&&e.kill()})))),r=Ql(),a=tc(e,"paste",(e=>{n.fireIfReady(e,"paste").each((t=>{t&&e.kill()})),o.triggerEvent("paste",e)&&e.kill(),r.set(setTimeout((()=>{o.triggerEvent(cr(),e)}),0))})),i=tc(e,"keydown",(e=>{o.triggerEvent("keydown",e)?e.kill():o.stopBackspace&&(e=>e.raw.which===$m[0]&&!R(["input","textarea"],Ue(e.target))&&!$w(e.target,'[contenteditable="true"]'))(e)&&e.prevent()})),l=tc(e,"focusin",(e=>{o.triggerEvent("focusin",e)&&e.kill()})),c=Ql(),d=tc(e,"focusout",(e=>{o.triggerEvent("focusout",e)&&e.kill(),c.set(setTimeout((()=>{o.triggerEvent(lr(),e)}),0))}));return{unbind:()=>{L(s,(e=>{e.unbind()})),i.unbind(),l.unbind(),d.unbind(),a.unbind(),r.on(clearTimeout),c.on(clearTimeout)}}},Kw=(e,t)=>{const o=be(e,"target").getOr(t);return Es(o)},Jw=As([{stopped:[]},{resume:["element"]},{complete:[]}]),Zw=(e,t,o,n,s,r)=>{const a=e(t,n),i=((e,t)=>{const o=Es(!1),n=Es(!1);return{stop:()=>{o.set(!0)},cut:()=>{n.set(!0)},isStopped:o.get,isCut:n.get,event:e,setSource:t.set,getSource:t.get}})(o,s);return a.fold((()=>(r.logEventNoHandlers(t,n),Jw.complete())),(e=>{const o=e.descHandler;return Aa(o)(i),i.isStopped()?(r.logEventStopped(t,e.element,o.purpose),Jw.stopped()):i.isCut()?(r.logEventCut(t,e.element,o.purpose),Jw.complete()):st(e.element).fold((()=>(r.logNoParent(t,e.element,o.purpose),Jw.complete())),(n=>(r.logEventResponse(t,e.element,o.purpose),Jw.resume(n))))}))},Qw=(e,t,o,n,s,r)=>Zw(e,t,o,n,s,r).fold(E,(n=>Qw(e,t,o,n,s,r)),T),eS=(e,t,o,n,s)=>{const r=Kw(o,n);return Qw(e,t,o,n,r,s)},tS=()=>{const e=(()=>{const e={};return{registerId:(t,o,n)=>{le(n,((n,s)=>{const r=void 0!==e[s]?e[s]:{};r[o]=((e,t)=>({cHandler:k.apply(void 0,[e.handler].concat(t)),purpose:e.purpose}))(n,t),e[s]=r}))},unregisterId:t=>{le(e,((e,o)=>{ve(e,t)&&delete e[t]}))},filterByType:t=>be(e,t).map((e=>pe(e,((e,t)=>((e,t)=>({id:e,descHandler:t}))(t,e))))).getOr([]),find:(t,o,n)=>be(e,o).bind((e=>Is(n,(t=>((e,t)=>pa(t).bind((t=>be(e,t))).map((e=>((e,t)=>({element:e,descHandler:t}))(t,e))))(e,t)),t)))}})(),t={},o=o=>{pa(o.element).each((o=>{delete t[o],e.unregisterId(o)}))};return{find:(t,o,n)=>e.find(t,o,n),filter:t=>e.filterByType(t),register:n=>{const s=(e=>{const t=e.element;return pa(t).getOrThunk((()=>((e,t)=>{const o=la(ua+"uid-");return ga(t,o),o})(0,e.element)))})(n);ye(t,s)&&((e,n)=>{const s=t[n];if(s!==e)throw new Error('The tagId "'+n+'" is already used by: '+na(s.element)+"\nCannot use it for: "+na(e.element)+"\nThe conflicting element is"+(yt(s.element)?" ":" not ")+"already in the DOM");o(e)})(n,s);const r=[n];e.registerId(r,s,n.events),t[s]=n},unregister:o,getById:e=>be(t,e)}},oS=fm({name:"Container",factory:e=>{const{attributes:t,...o}=e.dom;return{uid:e.uid,dom:{tag:"div",attributes:{role:"presentation",...t},...o},components:e.components,behaviours:fu(e.containerBehaviours),events:e.events,domModification:e.domModification,eventOrder:e.eventOrder}},configFields:[ys("components",[]),hu("containerBehaviours",[]),ys("events",{}),ys("domModification",{}),ys("eventOrder",{})]}),nS=e=>{const t=t=>st(e.element).fold(E,(e=>Ze(t,e))),o=tS(),n=(e,n)=>o.find(t,e,n),s=Yw(e.element,{triggerEvent:(e,t)=>Si(e,t.target,(o=>((e,t,o,n)=>eS(e,t,o,o.target,n))(n,e,t,o)))}),r={debugInfo:x("real"),triggerEvent:(e,t,o)=>{Si(e,t,(s=>eS(n,e,o,t,s)))},triggerFocus:(e,t)=>{pa(e).fold((()=>{Dl(e)}),(o=>{Si(ir(),e,(o=>(((e,t,o,n,s)=>{const r=Kw(o,n);Zw(e,t,o,n,r,s)})(n,ir(),{originator:t,kill:b,prevent:b,target:e},e,o),!1)))}))},triggerEscape:(e,t)=>{r.triggerEvent("keydown",e.element,t.event)},getByUid:e=>p(e),getByDom:e=>h(e),build:ri,buildOrPatch:si,addToGui:e=>{l(e)},removeFromGui:e=>{c(e)},addToWorld:e=>{a(e)},removeFromWorld:e=>{i(e)},broadcast:e=>{u(e)},broadcastOn:(e,t)=>{m(e,t)},broadcastEvent:(e,t)=>{g(e,t)},isConnected:E},a=e=>{e.connect(r),$e(e.element)||(o.register(e),L(e.components(),a),r.triggerEvent(br(),e.element,{target:e.element}))},i=e=>{$e(e.element)||(L(e.components(),i),o.unregister(e)),e.disconnect()},l=t=>{Ad(e,t)},c=e=>{Bd(e)},d=e=>{const t=o.filter(dr());L(t,(t=>{const o=t.descHandler;Aa(o)(e)}))},u=e=>{d({universal:!0,data:e})},m=(e,t)=>{d({universal:!1,channels:e,data:t})},g=(e,t)=>((e,t,o)=>{const n=(e=>{const t=Es(!1);return{stop:()=>{t.set(!0)},cut:b,isStopped:t.get,isCut:T,event:e,setSource:O("Cannot set source of a broadcasted event"),getSource:O("Cannot get source of a broadcasted event")}})(t);return L(e,(e=>{const t=e.descHandler;Aa(t)(n)})),n.isStopped()})(o.filter(e),t),p=e=>o.getById(e).fold((()=>sn.error(new Error('Could not find component with uid: "'+e+'" in system.'))),sn.value),h=e=>{const t=pa(e).getOr("not found");return p(t)};return a(e),{root:e,element:e.element,destroy:()=>{s.unbind(),Po(e.element)},add:l,remove:c,getByUid:p,getByDom:h,addToWorld:a,removeFromWorld:i,broadcast:u,broadcastOn:m,broadcastEvent:g}},sS=x([ys("prefix","form-field"),hu("fieldBehaviours",[wm,pu])]),rS=x([ju({schema:[os("dom")],name:"label"}),ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[os("text")],name:"aria-descriptor"}),Uu({factory:{sketch:e=>{const t=((e,t)=>{const o={};return le(e,((e,n)=>{R(t,n)||(o[n]=e)})),o})(e,["factory"]);return e.factory.sketch(t)}},schema:[os("factory")],name:"field"})]),aS=bm({name:"FormField",configFields:sS(),partFields:rS(),factory:(e,t,o,n)=>{const s=bu(e.fieldBehaviours,[wm.config({find:t=>om(t,e,"field")}),pu.config({store:{mode:"manual",getValue:e=>wm.getCurrent(e).bind(pu.getValue),setValue:(e,t)=>{wm.getCurrent(e).each((e=>{pu.setValue(e,t)}))}}})]),r=Hr([Kr(((t,o)=>{const n=sm(t,e,["label","field","aria-descriptor"]);n.field().each((t=>{const o=la(e.prefix);n.label().each((e=>{kt(e.element,"for",o),kt(t.element,"id",o)})),n["aria-descriptor"]().each((o=>{const n=la(e.prefix);kt(o.element,"id",n),kt(t.element,"aria-describedby",n)}))}))}))]),a={getField:t=>om(t,e,"field"),getLabel:t=>om(t,e,"label")};return{uid:e.uid,dom:e.dom,components:t,behaviours:s,events:r,apis:a}},apis:{getField:(e,t)=>e.getField(t),getLabel:(e,t)=>e.getLabel(t)}});var iS=Object.freeze({__proto__:null,exhibit:(e,t)=>Ea({attributes:Ds([{key:t.tabAttr,value:"true"}])})}),lS=[ys("tabAttr","data-alloy-tabstop")];const cS=Ol({fields:lS,name:"tabstopping",active:iS});var dS=tinymce.util.Tools.resolve("tinymce.html.Entities");const uS=(e,t,o,n)=>{const s=mS(e,t,o,n);return aS.sketch(s)},mS=(e,t,o,n)=>({dom:gS(o),components:e.toArray().concat([t]),fieldBehaviours:kl(n)}),gS=e=>({tag:"div",classes:["tox-form__group"].concat(e)}),pS=(e,t)=>aS.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ti(t.translate(e))]}),hS=la("form-component-change"),fS=la("form-close"),bS=la("form-cancel"),vS=la("form-action"),yS=la("form-submit"),xS=la("form-block"),wS=la("form-unblock"),SS=la("form-tabchange"),kS=la("form-resize"),CS=["input","textarea"],OS=e=>{const t=Ue(e);return R(CS,t)},_S=(e,t)=>{const o=t.getRoot(e).getOr(e.element);Pa(o,t.invalidClass),t.notify.each((t=>{OS(e.element)&&kt(e.element,"aria-invalid",!1),t.getContainer(e).each((e=>{ta(e,t.validHtml)})),t.onValid(e)}))},TS=(e,t,o,n)=>{const s=t.getRoot(e).getOr(e.element);La(s,t.invalidClass),t.notify.each((t=>{OS(e.element)&&kt(e.element,"aria-invalid",!0),t.getContainer(e).each((e=>{ta(e,n)})),t.onInvalid(e,n)}))},ES=(e,t,o)=>t.validator.fold((()=>bw(sn.value(!0))),(t=>t.validate(e))),AS=(e,t,o)=>(t.notify.each((t=>{t.onValidate(e)})),ES(e,t).map((o=>e.getSystem().isConnected()?o.fold((o=>(TS(e,t,0,o),sn.error(o))),(o=>(_S(e,t),sn.value(o)))):sn.error("No longer in system"))));var MS=Object.freeze({__proto__:null,markValid:_S,markInvalid:TS,query:ES,run:AS,isInvalid:(e,t)=>{const o=t.getRoot(e).getOr(e.element);return Ua(o,t.invalidClass)}}),DS=Object.freeze({__proto__:null,events:(e,t)=>e.validator.map((t=>Hr([Ur(t.onEvent,(t=>{AS(t,e).get(w)}))].concat(t.validateOnLoad?[Kr((t=>{AS(t,e).get(b)}))]:[])))).getOr({})}),BS=[os("invalidClass"),ys("getRoot",A.none),vs("notify",[ys("aria","alert"),ys("getContainer",A.none),ys("validHtml",""),Di("onValid"),Di("onInvalid"),Di("onValidate")]),vs("validator",[os("validate"),ys("onEvent","input"),ys("validateOnLoad",!0)])];const FS=Ol({fields:BS,name:"invalidating",active:DS,apis:MS,extra:{validation:e=>t=>{const o=pu.getValue(t);return bw(e(o))}}}),IS=Ol({fields:[],name:"unselecting",active:Object.freeze({__proto__:null,events:()=>Hr([Lr(sr(),E)]),exhibit:()=>Ea({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})})}),RS=la("color-input-change"),NS=la("color-swatch-change"),VS=la("color-picker-cancel"),zS=ju({schema:[os("dom")],name:"label"}),HS=e=>ju({name:e+"-edge",overrides:t=>t.model.manager.edgeActions[e].fold((()=>({})),(e=>({events:Hr([Wr(Hs(),((t,o,n)=>e(t,n)),[t]),Wr(Ws(),((t,o,n)=>e(t,n)),[t]),Wr(js(),((t,o,n)=>{n.mouseIsDown.get()&&e(t,n)}),[t])])})))}),LS=HS("top-left"),PS=HS("top"),US=HS("top-right"),WS=HS("right"),jS=HS("bottom-right"),GS=HS("bottom"),$S=HS("bottom-left");var qS=[zS,HS("left"),WS,PS,GS,LS,US,$S,jS,Uu({name:"thumb",defaults:x({dom:{styles:{position:"absolute"}}}),overrides:e=>({events:Hr([Gr(Hs(),e,"spectrum"),Gr(Ls(),e,"spectrum"),Gr(Ps(),e,"spectrum"),Gr(Ws(),e,"spectrum"),Gr(js(),e,"spectrum"),Gr($s(),e,"spectrum")])})}),Uu({schema:[es("mouseIsDown",(()=>Es(!1)))],name:"spectrum",overrides:e=>{const t=e.model.manager,o=(o,n)=>t.getValueFromEvent(n).map((n=>t.setValueFrom(o,e,n)));return{behaviours:kl([Pp.config({mode:"special",onLeft:o=>t.onLeft(o,e),onRight:o=>t.onRight(o,e),onUp:o=>t.onUp(o,e),onDown:o=>t.onDown(o,e)}),oh.config({})]),events:Hr([Ur(Hs(),o),Ur(Ls(),o),Ur(Ws(),o),Ur(js(),((t,n)=>{e.mouseIsDown.get()&&o(t,n)}))])}}})];const XS=x("slider.change.value"),YS=e=>{const t=e.event.raw;if((e=>-1!==e.type.indexOf("touch"))(t)){const e=t;return void 0!==e.touches&&1===e.touches.length?A.some(e.touches[0]).map((e=>$t(e.clientX,e.clientY))):A.none()}{const e=t;return void 0!==e.clientX?A.some(e).map((e=>$t(e.clientX,e.clientY))):A.none()}},KS=e=>e.model.minX,JS=e=>e.model.minY,ZS=e=>e.model.minX-1,QS=e=>e.model.minY-1,ek=e=>e.model.maxX,tk=e=>e.model.maxY,ok=e=>e.model.maxX+1,nk=e=>e.model.maxY+1,sk=(e,t,o)=>t(e)-o(e),rk=e=>sk(e,ek,KS),ak=e=>sk(e,tk,JS),ik=e=>rk(e)/2,lk=e=>ak(e)/2,ck=e=>e.stepSize,dk=e=>e.snapToGrid,uk=e=>e.snapStart,mk=e=>e.rounded,gk=(e,t)=>void 0!==e[t+"-edge"],pk=e=>gk(e,"left"),hk=e=>gk(e,"right"),fk=e=>gk(e,"top"),bk=e=>gk(e,"bottom"),vk=e=>e.model.value.get(),yk=(e,t)=>({x:e,y:t}),xk=(e,t)=>{Ir(e,XS(),{value:t})},wk=(e,t,o,n)=>e<t?e:e>o?o:e===t?t-1:Math.max(t,e-n),Sk=(e,t,o,n)=>e>o?e:e<t?t:e===o?o+1:Math.min(o,e+n),kk=(e,t,o)=>Math.max(t,Math.min(o,e)),Ck=e=>{const{min:t,max:o,range:n,value:s,step:r,snap:a,snapStart:i,rounded:l,hasMinEdge:c,hasMaxEdge:d,minBound:u,maxBound:m,screenRange:g}=e,p=c?t-1:t,h=d?o+1:o;if(s<u)return p;if(s>m)return h;{const e=((e,t,o)=>Math.min(o,Math.max(e,t))-t)(s,u,m),c=kk(e/g*n+t,p,h);return a&&c>=t&&c<=o?((e,t,o,n,s)=>s.fold((()=>{const s=e-t,r=Math.round(s/n)*n;return kk(t+r,t-1,o+1)}),(t=>{const s=(e-t)%n,r=Math.round(s/n),a=Math.floor((e-t)/n),i=Math.floor((o-t)/n),l=t+Math.min(i,a+r)*n;return Math.max(t,l)})))(c,t,o,r,i):l?Math.round(c):c}},Ok=e=>{const{min:t,max:o,range:n,value:s,hasMinEdge:r,hasMaxEdge:a,maxBound:i,maxOffset:l,centerMinEdge:c,centerMaxEdge:d}=e;return s<t?r?0:c:s>o?a?i:d:(s-t)/n*l},_k="top",Tk="right",Ek="bottom",Ak="left",Mk=e=>e.element.dom.getBoundingClientRect(),Dk=(e,t)=>e[t],Bk=e=>{const t=Mk(e);return Dk(t,Ak)},Fk=e=>{const t=Mk(e);return Dk(t,Tk)},Ik=e=>{const t=Mk(e);return Dk(t,_k)},Rk=e=>{const t=Mk(e);return Dk(t,Ek)},Nk=e=>{const t=Mk(e);return Dk(t,"width")},Vk=e=>{const t=Mk(e);return Dk(t,"height")},zk=(e,t,o)=>(e+t)/2-o,Hk=(e,t)=>{const o=Mk(e),n=Mk(t),s=Dk(o,Ak),r=Dk(o,Tk),a=Dk(n,Ak);return zk(s,r,a)},Lk=(e,t)=>{const o=Mk(e),n=Mk(t),s=Dk(o,_k),r=Dk(o,Ek),a=Dk(n,_k);return zk(s,r,a)},Pk=(e,t)=>{Ir(e,XS(),{value:t})},Uk=(e,t,o)=>{const n={min:KS(t),max:ek(t),range:rk(t),value:o,step:ck(t),snap:dk(t),snapStart:uk(t),rounded:mk(t),hasMinEdge:pk(t),hasMaxEdge:hk(t),minBound:Bk(e),maxBound:Fk(e),screenRange:Nk(e)};return Ck(n)},Wk=e=>(t,o)=>((e,t,o)=>{const n=(e>0?Sk:wk)(vk(o),KS(o),ek(o),ck(o));return Pk(t,n),A.some(n)})(e,t,o).map(E),jk=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=Nk(e),a=n.bind((t=>A.some(Hk(t,e)))).getOr(0),i=s.bind((t=>A.some(Hk(t,e)))).getOr(r),l={min:KS(t),max:ek(t),range:rk(t),value:o,hasMinEdge:pk(t),hasMaxEdge:hk(t),minBound:Bk(e),minOffset:0,maxBound:Fk(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return Ok(l)})(t,r,o,n,s);return Bk(t)-Bk(e)+a},Gk=Wk(-1),$k=Wk(1),qk=A.none,Xk=A.none,Yk={"top-left":A.none(),top:A.none(),"top-right":A.none(),right:A.some(((e,t)=>{xk(e,ok(t))})),"bottom-right":A.none(),bottom:A.none(),"bottom-left":A.none(),left:A.some(((e,t)=>{xk(e,ZS(t))}))};var Kk=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=Uk(e,t,o);return Pk(e,n),n},setToMin:(e,t)=>{const o=KS(t);Pk(e,o)},setToMax:(e,t)=>{const o=ek(t);Pk(e,o)},findValueOfOffset:Uk,getValueFromEvent:e=>YS(e).map((e=>e.left)),findPositionOfValue:jk,setPositionFromValue:(e,t,o,n)=>{const s=vk(o),r=jk(e,n.getSpectrum(e),s,n.getLeftEdge(e),n.getRightEdge(e),o),a=Jt(t.element)/2;Dt(t.element,"left",r-a+"px")},onLeft:Gk,onRight:$k,onUp:qk,onDown:Xk,edgeActions:Yk});const Jk=(e,t)=>{Ir(e,XS(),{value:t})},Zk=(e,t,o)=>{const n={min:JS(t),max:tk(t),range:ak(t),value:o,step:ck(t),snap:dk(t),snapStart:uk(t),rounded:mk(t),hasMinEdge:fk(t),hasMaxEdge:bk(t),minBound:Ik(e),maxBound:Rk(e),screenRange:Vk(e)};return Ck(n)},Qk=e=>(t,o)=>((e,t,o)=>{const n=(e>0?Sk:wk)(vk(o),JS(o),tk(o),ck(o));return Jk(t,n),A.some(n)})(e,t,o).map(E),eC=(e,t,o,n,s,r)=>{const a=((e,t,o,n,s)=>{const r=Vk(e),a=n.bind((t=>A.some(Lk(t,e)))).getOr(0),i=s.bind((t=>A.some(Lk(t,e)))).getOr(r),l={min:JS(t),max:tk(t),range:ak(t),value:o,hasMinEdge:fk(t),hasMaxEdge:bk(t),minBound:Ik(e),minOffset:0,maxBound:Rk(e),maxOffset:r,centerMinEdge:a,centerMaxEdge:i};return Ok(l)})(t,r,o,n,s);return Ik(t)-Ik(e)+a},tC=A.none,oC=A.none,nC=Qk(-1),sC=Qk(1),rC={"top-left":A.none(),top:A.some(((e,t)=>{xk(e,QS(t))})),"top-right":A.none(),right:A.none(),"bottom-right":A.none(),bottom:A.some(((e,t)=>{xk(e,nk(t))})),"bottom-left":A.none(),left:A.none()};var aC=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=Zk(e,t,o);return Jk(e,n),n},setToMin:(e,t)=>{const o=JS(t);Jk(e,o)},setToMax:(e,t)=>{const o=tk(t);Jk(e,o)},findValueOfOffset:Zk,getValueFromEvent:e=>YS(e).map((e=>e.top)),findPositionOfValue:eC,setPositionFromValue:(e,t,o,n)=>{const s=vk(o),r=eC(e,n.getSpectrum(e),s,n.getTopEdge(e),n.getBottomEdge(e),o),a=Wt(t.element)/2;Dt(t.element,"top",r-a+"px")},onLeft:tC,onRight:oC,onUp:nC,onDown:sC,edgeActions:rC});const iC=(e,t)=>{Ir(e,XS(),{value:t})},lC=(e,t)=>({x:e,y:t}),cC=(e,t)=>(o,n)=>((e,t,o,n)=>{const s=e>0?Sk:wk,r=t?vk(n).x:s(vk(n).x,KS(n),ek(n),ck(n)),a=t?s(vk(n).y,JS(n),tk(n),ck(n)):vk(n).y;return iC(o,lC(r,a)),A.some(r)})(e,t,o,n).map(E),dC=cC(-1,!1),uC=cC(1,!1),mC=cC(-1,!0),gC=cC(1,!0),pC={"top-left":A.some(((e,t)=>{xk(e,yk(ZS(t),QS(t)))})),top:A.some(((e,t)=>{xk(e,yk(ik(t),QS(t)))})),"top-right":A.some(((e,t)=>{xk(e,yk(ok(t),QS(t)))})),right:A.some(((e,t)=>{xk(e,yk(ok(t),lk(t)))})),"bottom-right":A.some(((e,t)=>{xk(e,yk(ok(t),nk(t)))})),bottom:A.some(((e,t)=>{xk(e,yk(ik(t),nk(t)))})),"bottom-left":A.some(((e,t)=>{xk(e,yk(ZS(t),nk(t)))})),left:A.some(((e,t)=>{xk(e,yk(ZS(t),lk(t)))}))};var hC=Object.freeze({__proto__:null,setValueFrom:(e,t,o)=>{const n=Uk(e,t,o.left),s=Zk(e,t,o.top),r=lC(n,s);return iC(e,r),r},setToMin:(e,t)=>{const o=KS(t),n=JS(t);iC(e,lC(o,n))},setToMax:(e,t)=>{const o=ek(t),n=tk(t);iC(e,lC(o,n))},getValueFromEvent:e=>YS(e),setPositionFromValue:(e,t,o,n)=>{const s=vk(o),r=jk(e,n.getSpectrum(e),s.x,n.getLeftEdge(e),n.getRightEdge(e),o),a=eC(e,n.getSpectrum(e),s.y,n.getTopEdge(e),n.getBottomEdge(e),o),i=Jt(t.element)/2,l=Wt(t.element)/2;Dt(t.element,"left",r-i+"px"),Dt(t.element,"top",a-l+"px")},onLeft:dC,onRight:uC,onUp:mC,onDown:gC,edgeActions:pC});const fC=bm({name:"Slider",configFields:[ys("stepSize",1),ys("onChange",b),ys("onChoose",b),ys("onInit",b),ys("onDragStart",b),ys("onDragEnd",b),ys("snapToGrid",!1),ys("rounded",!0),us("snapStart"),ns("model",Jn("mode",{x:[ys("minX",0),ys("maxX",100),es("value",(e=>Es(e.mode.minX))),os("getInitialValue"),Ri("manager",Kk)],y:[ys("minY",0),ys("maxY",100),es("value",(e=>Es(e.mode.minY))),os("getInitialValue"),Ri("manager",aC)],xy:[ys("minX",0),ys("maxX",100),ys("minY",0),ys("maxY",100),es("value",(e=>Es({x:e.mode.minX,y:e.mode.minY}))),os("getInitialValue"),Ri("manager",hC)]})),hu("sliderBehaviours",[Pp,pu]),es("mouseIsDown",(()=>Es(!1)))],partFields:qS,factory:(e,t,o,n)=>{const s=t=>nm(t,e,"thumb"),r=t=>nm(t,e,"spectrum"),a=t=>om(t,e,"left-edge"),i=t=>om(t,e,"right-edge"),l=t=>om(t,e,"top-edge"),c=t=>om(t,e,"bottom-edge"),d=e.model,u=d.manager,m=(t,o)=>{u.setPositionFromValue(t,o,e,{getLeftEdge:a,getRightEdge:i,getTopEdge:l,getBottomEdge:c,getSpectrum:r})},g=(e,t)=>{d.value.set(t);const o=s(e);m(e,o)},p=t=>{const o=e.mouseIsDown.get();e.mouseIsDown.set(!1),o&&om(t,e,"thumb").each((o=>{const n=d.value.get();e.onChoose(t,o,n)}))},h=(t,o)=>{o.stop(),e.mouseIsDown.set(!0),e.onDragStart(t,s(t))},f=(t,o)=>{o.stop(),e.onDragEnd(t,s(t)),p(t)};return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.sliderBehaviours,[Pp.config({mode:"special",focusIn:t=>om(t,e,"spectrum").map(Pp.focusIn).map(E)}),pu.config({store:{mode:"manual",getValue:e=>d.value.get(),setValue:g}}),Al.config({channels:{[Jd()]:{onReceive:p}}})]),events:Hr([Ur(XS(),((t,o)=>{((t,o)=>{g(t,o);const n=s(t);e.onChange(t,n,o),A.some(!0)})(t,o.event.value)})),Kr(((t,o)=>{const n=d.getInitialValue();d.value.set(n);const a=s(t);m(t,a);const i=r(t);e.onInit(t,a,i,d.value.get())})),Ur(Hs(),h),Ur(Ps(),f),Ur(Ws(),h),Ur($s(),f)]),apis:{resetToMin:t=>{u.setToMin(t,e)},resetToMax:t=>{u.setToMax(t,e)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:(e,t,o)=>{e.setValue(t,o)},resetToMin:(e,t)=>{e.resetToMin(t)},resetToMax:(e,t)=>{e.resetToMax(t)},refresh:(e,t)=>{e.refresh(t)}}}),bC=la("rgb-hex-update"),vC=la("slider-update"),yC=la("palette-update"),xC="form",wC=[hu("formBehaviours",[pu])],SC=e=>"<alloy.field."+e+">",kC=(e,t)=>({uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.formBehaviours,[pu.config({store:{mode:"manual",getValue:t=>{const o=rm(t,e);return ce(o,((e,t)=>e().bind((e=>{return o=wm.getCurrent(e),n=new Error(`Cannot find a current component to extract the value from for form part '${t}': `+na(e.element)),o.fold((()=>sn.error(n)),sn.value);var o,n})).map(pu.getValue)))},setValue:(t,o)=>{le(o,((o,n)=>{om(t,e,n).each((e=>{wm.getCurrent(e).each((e=>{pu.setValue(e,o)}))}))}))}}})]),apis:{getField:(t,o)=>om(t,e,o).bind(wm.getCurrent)}}),CC={getField:Ca(((e,t,o)=>e.getField(t,o))),sketch:e=>{const t=(()=>{const e=[];return{field:(t,o)=>(e.push(t),Ju(xC,SC(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=H(n,(e=>Uu({name:e,pname:SC(e)})));return mm(xC,wC,s,kC,o)}},OC=la("valid-input"),_C=la("invalid-input"),TC=la("validating-input"),EC="colorcustom.rgb.",AC=(e,t,o,n)=>{const s=(o,n)=>FS.config({invalidClass:t("invalid"),notify:{onValidate:e=>{Ir(e,TC,{type:o})},onValid:e=>{Ir(e,OC,{type:o,value:pu.getValue(e)})},onInvalid:e=>{Ir(e,_C,{type:o,value:pu.getValue(e)})}},validator:{validate:t=>{const o=pu.getValue(t),s=n(o)?sn.value(!0):sn.error(e("aria.input.invalid"));return bw(s)},validateOnLoad:!1}}),r=(o,n,r,a,i)=>{const l=e(EC+"range"),c=aS.parts.label({dom:{tag:"label",attributes:{"aria-label":a}},components:[ti(r)]}),d=aS.parts.field({data:i,factory:Vb,inputAttributes:{type:"text",..."hex"===n?{"aria-live":"polite"}:{}},inputClasses:[t("textfield")],inputBehaviours:kl([s(n,o),cS.config({})]),onSetValue:e=>{FS.isInvalid(e)&&FS.run(e).get(b)}}),u=[c,d],m="hex"!==n?[aS.parts["aria-descriptor"]({text:l})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(m)}},a=(e,t)=>{const o=t.red,n=t.green,s=t.blue;pu.setValue(e,{red:o,green:n,blue:s})},i=jh({dom:{tag:"div",classes:[t("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),l=(e,t)=>{i.getOpt(e).each((e=>{Dt(e.element,"background-color","#"+t.value)}))},c=fm({factory:()=>{const s={red:Es(A.some(255)),green:Es(A.some(255)),blue:Es(A.some(255)),hex:Es(A.some("ffffff"))},c=e=>s[e].get(),d=(e,t)=>{s[e].set(t)},u=e=>{const t=e.red,o=e.green,n=e.blue;d("red",A.some(t)),d("green",A.some(o)),d("blue",A.some(n))},m=(e,t)=>{const o=t.event;"hex"!==o.type?d(o.type,A.none()):n(e)},g=(e,t)=>{const n=t.event;(e=>"hex"===e.type)(n)?((e,t)=>{o(e);const n=qy(t);d("hex",A.some(n.value));const s=lx(n);a(e,s),u(s),Ir(e,bC,{hex:n}),l(e,n)})(e,n.value):((e,t,o)=>{const n=parseInt(o,10);d(t,A.some(n)),c("red").bind((e=>c("green").bind((t=>c("blue").map((o=>rx(e,t,o,1))))))).each((t=>{const o=((e,t)=>{const o=Qy(t);return CC.getField(e,"hex").each((t=>{oh.isFocused(t)||pu.setValue(e,{hex:o.value})})),o})(e,t);Ir(e,bC,{hex:o}),l(e,o)}))})(e,n.type,n.value)},p=t=>({label:e(EC+t+".label"),description:e(EC+t+".description")}),h=p("red"),f=p("green"),b=p("blue"),v=p("hex");return fn(CC.sketch((o=>({dom:{tag:"form",classes:[t("rgb-form")],attributes:{"aria-label":e("aria.color.picker")}},components:[o.field("red",aS.sketch(r(ax,"red",h.label,h.description,255))),o.field("green",aS.sketch(r(ax,"green",f.label,f.description,255))),o.field("blue",aS.sketch(r(ax,"blue",b.label,b.description,255))),o.field("hex",aS.sketch(r(Ky,"hex",v.label,v.description,"ffffff"))),i.asSpec()],formBehaviours:kl([FS.config({invalidClass:t("form-invalid")}),Jp("rgb-form-events",[Ur(OC,g),Ur(_C,m),Ur(TC,m)])])}))),{apis:{updateHex:(e,t)=>{pu.setValue(e,{hex:t.value}),((e,t)=>{const o=lx(t);a(e,o),u(o)})(e,t),l(e,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:(e,t,o)=>{e.updateHex(t,o)}},extraApis:{}});return c},MC=(e,t)=>{const o=fm({name:"ColourPicker",configFields:[os("dom"),ys("onValidHex",b),ys("onInvalidHex",b)],factory:o=>{const n=AC(e,t,o.onValidHex,o.onInvalidHex),s=((e,t)=>{const o=fC.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[t("sv-palette-spectrum")]}}),n=fC.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette-thumb")],innerHtml:`<div class=${t("sv-palette-inner-thumb")} role="presentation"></div>`}}),s=(e,t)=>{const{width:o,height:n}=e,s=e.getContext("2d");if(null===s)return;s.fillStyle=t,s.fillRect(0,0,o,n);const r=s.createLinearGradient(0,0,o,0);r.addColorStop(0,"rgba(255,255,255,1)"),r.addColorStop(1,"rgba(255,255,255,0)"),s.fillStyle=r,s.fillRect(0,0,o,n);const a=s.createLinearGradient(0,0,0,n);a.addColorStop(0,"rgba(0,0,0,0)"),a.addColorStop(1,"rgba(0,0,0,1)"),s.fillStyle=a,s.fillRect(0,0,o,n)};return fm({factory:e=>{const r=x({x:0,y:0}),a=kl([wm.config({find:A.some}),oh.config({})]);return fC.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[t("sv-palette")]},model:{mode:"xy",getInitialValue:r},rounded:!1,components:[o,n],onChange:(e,t,o)=>{Ir(e,yC,{value:o})},onInit:(e,t,o,n)=>{s(o.element.dom,ux(mx))},sliderBehaviours:a})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:(e,t,o)=>{((e,t)=>{const o=e.components()[0].element.dom,n=_x(t,100,100),r=ix(n);s(o,ux(r))})(t,o)},setThumb:(e,t,o)=>{((e,t)=>{const o=Tx(lx(t));fC.setValue(e,{x:o.saturation,y:100-o.value})})(t,o)}},extraApis:{}})})(0,t),r={paletteRgba:Es(mx),paletteHue:Es(0)},a=jh(((e,t)=>{const o=fC.parts.spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),n=fC.parts.thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return fC.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:x(0)},components:[o,n],sliderBehaviours:kl([oh.config({})]),onChange:(e,t,o)=>{Ir(e,vC,{value:o})}})})(0,t)),i=jh(s.sketch({})),l=jh(n.sketch({})),c=(e,t,o)=>{i.getOpt(e).each((e=>{s.setHue(e,o)}))},d=(e,t)=>{l.getOpt(e).each((e=>{n.updateHex(e,t)}))},u=(e,t,o)=>{a.getOpt(e).each((e=>{fC.setValue(e,(e=>100-e/360*100)(o))}))},m=(e,t)=>{i.getOpt(e).each((e=>{s.setThumb(e,t)}))},g=(e,t,o,n)=>{((e,t)=>{const o=lx(e);r.paletteRgba.set(o),r.paletteHue.set(t)})(t,o),L(n,(n=>{n(e,t,o)}))};return{uid:o.uid,dom:o.dom,components:[i.asSpec(),a.asSpec(),l.asSpec()],behaviours:kl([Jp("colour-picker-events",[Ur(bC,(()=>{const e=[c,u,m];return(t,o)=>{const n=o.event.hex,s=(e=>Tx(lx(e)))(n);g(t,n,s.hue,e)}})()),Ur(yC,(()=>{const e=[d];return(t,o)=>{const n=o.event.value,s=r.paletteHue.get(),a=_x(s,n.x,100-n.y),i=Ex(a);g(t,i,s,e)}})()),Ur(vC,(()=>{const e=[c,d];return(t,o)=>{const n=(e=>(100-e)/100*360)(o.event.value),s=r.paletteRgba.get(),a=Tx(s),i=_x(n,a.saturation,a.value),l=Ex(i);g(t,l,n,e)}})())]),wm.config({find:e=>l.getOpt(e)}),Pp.config({mode:"acyclic"})])}}});return o},DC=()=>wm.config({find:A.some}),BC=e=>wm.config({find:t=>lt(t.element,e).bind((e=>t.getSystem().getByDom(e).toOptional()))}),FC=Dn([ys("preprocess",w),ys("postprocess",w)]),IC=(e,t)=>{const o=Yn("RepresentingConfigs.memento processors",FC,t);return pu.config({store:{mode:"manual",getValue:t=>{const n=e.get(t),s=pu.getValue(n);return o.postprocess(s)},setValue:(t,n)=>{const s=o.preprocess(n),r=e.get(t);pu.setValue(r,s)}}})},RC=(e,t,o)=>pu.config({store:{mode:"manual",...e.map((e=>({initialValue:e}))).getOr({}),getValue:t,setValue:o}}),NC=(e,t,o)=>RC(e,(e=>t(e.element)),((e,t)=>o(e.element,t))),VC=e=>pu.config({store:{mode:"memory",initialValue:e}}),zC={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"};var HC=tinymce.util.Tools.resolve("tinymce.Resource"),LC=tinymce.util.Tools.resolve("tinymce.util.Tools");const PC=(e,t)=>{let o=null;const n=()=>{c(o)||(clearTimeout(o),o=null)};return{cancel:n,throttle:(...s)=>{n(),o=setTimeout((()=>{o=null,e.apply(null,s)}),t)}}},UC=la("alloy-fake-before-tabstop"),WC=la("alloy-fake-after-tabstop"),jC=e=>({dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:e},behaviours:kl([oh.config({ignore:!0}),cS.config({})])}),GC=(e,t)=>({dom:{tag:"div",classes:["tox-navobj",...e.getOr([])]},components:[jC([UC]),t,jC([WC])],behaviours:kl([BC(1)])}),$C=(e,t)=>{Ir(e,Ks(),{raw:{which:9,shiftKey:t}})},qC=(e,t)=>{const o=t.element;Ua(o,UC)?$C(e,!0):Ua(o,WC)&&$C(e,!1)},XC=e=>$w(e,["."+UC,"."+WC].join(","),T),YC=la("update-dialog"),KC=la("update-title"),JC=la("update-body"),ZC=la("update-footer"),QC=la("body-send-message"),eO=la("dialog-focus-shifted"),tO=Do().browser,oO=tO.isSafari(),nO=tO.isFirefox(),sO=oO||nO,rO=tO.isChromium(),aO=({scrollTop:e,scrollHeight:t,clientHeight:o})=>Math.ceil(e)+o>=t,iO=(e,t)=>e.scrollTo(0,"bottom"===t?99999999:t),lO=(e,t,o)=>{const n=e.dom;A.from(n.contentDocument).fold(o,(e=>{let o=0;const s=((e,t)=>{const o=e.body;return A.from(!/^<!DOCTYPE (html|HTML)/.test(t)&&(!rO&&!oO||g(o)&&(0!==o.scrollTop||Math.abs(o.scrollHeight-o.clientHeight)>1))?o:e.documentElement)})(e,t).map((e=>(o=e.scrollTop,e))).forall(aO),r=()=>{const e=n.contentWindow;g(e)&&(s?iO(e,"bottom"):!s&&sO&&0!==o&&iO(e,o))};oO&&n.addEventListener("load",r,{once:!0}),e.open(),e.write(t),e.close(),oO||r()}))},cO=Ce(sO,oO?500:200).map((e=>((e,t)=>{let o=null,n=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null,n=null)},throttle:(...s)=>{n=s,c(o)&&(o=setTimeout((()=>{const t=n;o=null,n=null,e.apply(null,t)}),t))}}})(lO,e))),dO=la("toolbar.button.execute"),uO=la("common-button-display-events"),mO={[ur()]:["disabling","alloy.base.behaviour","toggling","toolbar-button-events"],[Sr()]:["toolbar-button-events",uO],[Ws()]:["focusing","alloy.base.behaviour",uO]},gO=e=>Dt(e.element,"width",It(e.element,"width")),pO=(e,t,o)=>ef(e,{tag:"span",classes:["tox-icon","tox-tbtn__icon-wrap"],behaviours:o},t),hO=(e,t)=>pO(e,t,[]),fO=(e,t)=>pO(e,t,[Kp.config({})]),bO=(e,t,o)=>({dom:{tag:"span",classes:[`${t}__select-label`]},components:[ti(o.translate(e))],behaviours:kl([Kp.config({})])}),vO=la("update-menu-text"),yO=la("update-menu-icon"),xO=(e,t,o)=>{const n=Es(b),s=e.text.map((e=>jh(bO(e,t,o.providers)))),r=e.icon.map((e=>jh(fO(e,o.providers.icons)))),a=(e,t)=>{const o=pu.getValue(e);return oh.focus(o),Ir(o,"keydown",{raw:t.event.raw}),Dw.close(o),A.some(!0)},i=e.role.fold((()=>({})),(e=>({role:e}))),l=e.tooltip.fold((()=>({})),(e=>{const t=o.providers.translate(e);return{title:t,"aria-label":t}})),c=ef("chevron-down",{tag:"div",classes:[`${t}__select-chevron`]},o.providers.icons),d=la("common-button-display-events"),u=jh(Dw.sketch({...e.uid?{uid:e.uid}:{},...i,dom:{tag:"button",classes:[t,`${t}--select`].concat(H(e.classes,(e=>`${t}--${e}`))),attributes:{...l}},components:Dy([r.map((e=>e.asSpec())),s.map((e=>e.asSpec())),A.some(c)]),matchWidth:!0,useMinWidth:!0,onOpen:(t,o,n)=>{e.searchable&&(e=>{Ub(e).each((e=>oh.focus(e)))})(n)},dropdownBehaviours:kl([...e.dropdownBehaviours,ky((()=>e.disabled||o.providers.isDisabled())),Sy(),IS.config({}),Kp.config({}),Jp("dropdown-events",[Ty(e,n),Ey(e,n)]),Jp(d,[Kr(((e,t)=>gO(e)))]),Jp("menubutton-update-display-text",[Ur(vO,((e,t)=>{s.bind((t=>t.getOpt(e))).each((e=>{Kp.set(e,[ti(o.providers.translate(t.event.text))])}))})),Ur(yO,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Kp.set(e,[fO(t.event.icon,o.providers.icons)])}))}))])]),eventOrder:fn(mO,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"],[Sr()]:["toolbar-button-events","dropdown-events",d]}),sandboxBehaviours:kl([Pp.config({mode:"special",onLeft:a,onRight:a}),Jp("dropdown-sandbox-events",[Ur(zb,((e,t)=>{(e=>{const t=pu.getValue(e),o=Pb(e).map(Wb);Dw.refetch(t).get((()=>{const e=uw.getCoupled(t,"sandbox");o.each((t=>Pb(e).each((e=>((e,t)=>{pu.setValue(e,t.fetchPattern),e.element.dom.selectionStart=t.selectionStart,e.element.dom.selectionEnd=t.selectionEnd})(e,t)))))}))})(e),t.stop()})),Ur(Hb,((e,t)=>{((e,t)=>{(e=>Xd.getState(e).bind(Gm.getHighlighted).bind(Gm.getHighlighted))(e).each((o=>{((e,t,o,n)=>{const s={...n,target:t};e.getSystem().triggerEvent(o,t,s)})(e,o.element,t.event.eventType,t.event.interactionEvent)}))})(e,t),t.stop()}))])]),lazySink:o.getSink,toggleClass:`${t}--active`,parts:{menu:{...Bb(0,e.columns,e.presets),fakeFocus:e.searchable,onHighlightItem:Bw,onCollapseMenu:(e,t,o)=>{Gm.getHighlighted(o).each((t=>{Bw(e,o,t)}))},onDehighlightItem:Fw}},getAnchorOverrides:()=>({maxHeightFunction:(e,t)=>{lc()(e,t-10)}}),fetch:t=>fw(k(e.fetch,t))}));return u.asSpec()},wO=e=>"separator"===e.type,SO={type:"separator"},kO=(e,t)=>{const o=((e,t)=>{const o=j(e,((e,o)=>(e=>r(e))(o)?""===o?e:"|"===o?e.length>0&&!wO(e[e.length-1])?e.concat([SO]):e:ve(t,o.toLowerCase())?e.concat([t[o.toLowerCase()]]):e:e.concat([o])),[]);return o.length>0&&wO(o[o.length-1])&&o.pop(),o})(r(e)?e.split(" "):e,t);return W(o,((e,o)=>{if((e=>ve(e,"getSubmenuItems"))(o)){const n=(e=>{const t=be(e,"value").getOrThunk((()=>la("generated-menu-item")));return fn({value:t},e)})(o),s=((e,t)=>{const o=e.getSubmenuItems(),n=kO(o,t);return{item:e,menus:fn(n.menus,{[e.value]:n.items}),expansions:fn(n.expansions,{[e.value]:e.value})}})(n,t);return{menus:fn(e.menus,s.menus),items:[s.item,...e.items],expansions:fn(e.expansions,s.expansions)}}return{...e,items:[o,...e.items]}}),{menus:{},expansions:{},items:[]})},CO=(e,t,o,n)=>{const s=la("primary-menu"),r=kO(e,o.shared.providers.menuItems());if(0===r.items.length)return A.none();const a=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-field",placeholder:e.placeholder}))))(n),i=zw(s,r.items,t,o,n.isHorizontalMenu,a),l=(e=>e.search.fold((()=>({searchMode:"no-search"})),(e=>({searchMode:"search-with-results"}))))(n),c=ce(r.menus,((e,n)=>zw(n,e,t,o,!1,l))),d=fn(c,Ms(s,i));return A.from(Lh.tieredData(s,d,r.expansions))},OO=e=>!ve(e,"items"),_O="data-value",TO=(e,t,o,n)=>H(o,(o=>OO(o)?{type:"togglemenuitem",text:o.text,value:o.value,active:o.value===n,onAction:()=>{pu.setValue(e,o.value),Ir(e,hS,{name:t}),oh.focus(e)}}:{type:"nestedmenuitem",text:o.text,getSubmenuItems:()=>TO(e,t,o.items,n)})),EO=(e,t)=>re(e,(e=>OO(e)?Ce(e.value===t,e):EO(e.items,t))),AO=fm({name:"HtmlSelect",configFields:[os("options"),hu("selectBehaviours",[oh,pu]),ys("selectClasses",[]),ys("selectAttributes",{}),us("data")],factory:(e,t)=>{const o=H(e.options,(e=>({dom:{tag:"option",value:e.value,innerHtml:e.text}}))),n=e.data.map((e=>Ms("initialValue",e))).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:o,behaviours:bu(e.selectBehaviours,[oh.config({}),pu.config({store:{mode:"manual",getValue:e=>$a(e.element),setValue:(t,o)=>{const n=oe(e.options);G(e.options,(e=>e.value===o)).isSome()?qa(t.element,o):-1===t.element.dom.selectedIndex&&""===o&&n.each((e=>qa(t.element,e.value)))},...n}})])}}}),MO=x([ys("field1Name","field1"),ys("field2Name","field2"),Fi("onLockedChange"),Ai(["lockClass"]),ys("locked",!1),vu("coupledFieldBehaviours",[wm,pu])]),DO=(e,t)=>Uu({factory:aS,name:e,overrides:e=>({fieldBehaviours:kl([Jp("coupled-input-behaviour",[Ur(Zs(),(o=>{((e,t,o)=>om(e,t,o).bind(wm.getCurrent))(o,e,t).each((t=>{om(o,e,"lock").each((n=>{dh.isOn(n)&&e.onLockedChange(o,t,n)}))}))}))])])})}),BO=x([DO("field1","field2"),DO("field2","field1"),Uu({factory:Wh,schema:[os("dom")],name:"lock",overrides:e=>({buttonBehaviours:kl([dh.config({selected:e.locked,toggleClass:e.markers.lockClass,aria:{mode:"pressed"}})])})})]),FO=bm({name:"FormCoupledInputs",configFields:MO(),partFields:BO(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:yu(e.coupledFieldBehaviours,[wm.config({find:A.some}),pu.config({store:{mode:"manual",getValue:t=>{const o=im(t,e,["field1","field2"]);return{[e.field1Name]:pu.getValue(o.field1()),[e.field2Name]:pu.getValue(o.field2())}},setValue:(t,o)=>{const n=im(t,e,["field1","field2"]);ye(o,e.field1Name)&&pu.setValue(n.field1(),o[e.field1Name]),ye(o,e.field2Name)&&pu.setValue(n.field2(),o[e.field2Name])}}})]),apis:{getField1:t=>om(t,e,"field1"),getField2:t=>om(t,e,"field2"),getLock:t=>om(t,e,"lock")}}),apis:{getField1:(e,t)=>e.getField1(t),getField2:(e,t)=>e.getField2(t),getLock:(e,t)=>e.getLock(t)}}),IO=e=>{const t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(e);if(null!==t){const e=parseFloat(t[1]),o=t[2];return sn.value({value:e,unit:o})}return sn.error(e)},RO=(e,t)=>{const o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,in:1},n=e=>ve(o,e);return e.unit===t?A.some(e.value):n(e.unit)&&n(t)?o[e.unit]===o[t]?A.some(e.value):A.some(e.value/o[e.unit]*o[t]):A.none()},NO=e=>A.none(),VO=(e,t)=>{const o=e.label.map((e=>pS(e,t))),n=[Rm.config({disabled:()=>e.disabled||t.isDisabled()}),Sy(),Pp.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:e=>(Fr(e,yS),A.some(!0))}),Jp("textfield-change",[Ur(Zs(),((t,o)=>{Ir(t,hS,{name:e.name})})),Ur(cr(),((t,o)=>{Ir(t,hS,{name:e.name})}))]),cS.config({})],s=e.validation.map((e=>FS.config({getRoot:e=>rt(e.element),invalidClass:"tox-invalid",validator:{validate:t=>{const o=pu.getValue(t),n=e.validator(o);return bw(!0===n?sn.value(o):sn.error(n))},validateOnLoad:e.validateOnLoad}}))).toArray(),r={...e.placeholder.fold(x({}),(e=>({placeholder:t.translate(e)}))),...e.inputMode.fold(x({}),(e=>({inputmode:e})))},a=aS.parts.field({tag:!0===e.multiline?"textarea":"input",...e.data.map((e=>({data:e}))).getOr({}),inputAttributes:r,inputClasses:[e.classname],inputBehaviours:kl(q([n,s])),selectOnFocus:!1,factory:Vb}),i=e.multiline?{dom:{tag:"div",classes:["tox-textarea-wrap"]},components:[a]}:a,l=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),c=[Rm.config({disabled:()=>e.disabled||t.isDisabled(),onDisabled:e=>{aS.getField(e).each(Rm.disable)},onEnabled:e=>{aS.getField(e).each(Rm.enable)}}),Sy()];return uS(o,i,l,c)},zO=(e,t)=>t.getAnimationRoot.fold((()=>e.element),(t=>t(e))),HO=e=>e.dimension.property,LO=(e,t)=>e.dimension.getDimension(t),PO=(e,t)=>{const o=zO(e,t);ja(o,[t.shrinkingClass,t.growingClass])},UO=(e,t)=>{Pa(e.element,t.openClass),La(e.element,t.closedClass),Dt(e.element,HO(t),"0px"),Lt(e.element)},WO=(e,t)=>{Pa(e.element,t.closedClass),La(e.element,t.openClass),Ht(e.element,HO(t))},jO=(e,t,o,n)=>{o.setCollapsed(),Dt(e.element,HO(t),LO(t,e.element)),PO(e,t),UO(e,t),t.onStartShrink(e),t.onShrunk(e)},GO=(e,t,o,n)=>{const s=n.getOrThunk((()=>LO(t,e.element)));o.setCollapsed(),Dt(e.element,HO(t),s),Lt(e.element);const r=zO(e,t);Pa(r,t.growingClass),La(r,t.shrinkingClass),UO(e,t),t.onStartShrink(e)},$O=(e,t,o)=>{const n=LO(t,e.element);("0px"===n?jO:GO)(e,t,o,A.some(n))},qO=(e,t,o)=>{const n=zO(e,t),s=Ua(n,t.shrinkingClass),r=LO(t,e.element);WO(e,t);const a=LO(t,e.element);(s?()=>{Dt(e.element,HO(t),r),Lt(e.element)}:()=>{UO(e,t)})(),Pa(n,t.shrinkingClass),La(n,t.growingClass),WO(e,t),Dt(e.element,HO(t),a),o.setExpanded(),t.onStartGrow(e)},XO=(e,t,o)=>{const n=zO(e,t);return!0===Ua(n,t.growingClass)},YO=(e,t,o)=>{const n=zO(e,t);return!0===Ua(n,t.shrinkingClass)};var KO=Object.freeze({__proto__:null,refresh:(e,t,o)=>{if(o.isExpanded()){Ht(e.element,HO(t));const o=LO(t,e.element);Dt(e.element,HO(t),o)}},grow:(e,t,o)=>{o.isExpanded()||qO(e,t,o)},shrink:(e,t,o)=>{o.isExpanded()&&$O(e,t,o)},immediateShrink:(e,t,o)=>{o.isExpanded()&&jO(e,t,o)},hasGrown:(e,t,o)=>o.isExpanded(),hasShrunk:(e,t,o)=>o.isCollapsed(),isGrowing:XO,isShrinking:YO,isTransitioning:(e,t,o)=>XO(e,t)||YO(e,t),toggleGrow:(e,t,o)=>{(o.isExpanded()?$O:qO)(e,t,o)},disableTransitions:PO,immediateGrow:(e,t,o)=>{o.isExpanded()||(WO(e,t),Dt(e.element,HO(t),LO(t,e.element)),PO(e,t),o.setExpanded(),t.onStartGrow(e),t.onGrown(e))}}),JO=Object.freeze({__proto__:null,exhibit:(e,t,o)=>{const n=t.expanded;return Ea(n?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:Ms(t.dimension.property,"0px")})},events:(e,t)=>Hr([Yr(or(),((o,n)=>{n.event.raw.propertyName===e.dimension.property&&(PO(o,e),t.isExpanded()&&Ht(o.element,e.dimension.property),(t.isExpanded()?e.onGrown:e.onShrunk)(o))}))])}),ZO=[os("closedClass"),os("openClass"),os("shrinkingClass"),os("growingClass"),us("getAnimationRoot"),Di("onShrunk"),Di("onStartShrink"),Di("onGrown"),Di("onStartGrow"),ys("expanded",!1),ns("dimension",Jn("property",{width:[Ri("property","width"),Ri("getDimension",(e=>Jt(e)+"px"))],height:[Ri("property","height"),Ri("getDimension",(e=>Wt(e)+"px"))]}))];const QO=Ol({fields:ZO,name:"sliding",active:JO,apis:KO,state:Object.freeze({__proto__:null,init:e=>{const t=Es(e.expanded);return _a({isExpanded:()=>!0===t.get(),isCollapsed:()=>!1===t.get(),setCollapsed:k(t.set,!1),setExpanded:k(t.set,!0),readState:()=>"expanded: "+t.get()})}})}),e_=e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setActive:t=>{const o=e.element;t?(La(o,"tox-tbtn--enabled"),kt(o,"aria-pressed",!0)):(Pa(o,"tox-tbtn--enabled"),Et(o,"aria-pressed"))},isActive:()=>Ua(e.element,"tox-tbtn--enabled"),setText:t=>{Ir(e,vO,{text:t})},setIcon:t=>Ir(e,yO,{icon:t})}),t_=(e,t,o,n,s=!0)=>xO({text:e.text,icon:e.icon,tooltip:e.tooltip,searchable:e.search.isSome(),role:n,fetch:(t,n)=>{const s={pattern:e.search.isSome()?Iw(t):""};e.fetch((t=>{n(CO(t,pb.CLOSE_ON_EXECUTE,o,{isHorizontalMenu:!1,search:e.search}))}),s,e_(t))},onSetup:e.onSetup,getApi:e_,columns:1,presets:"normal",classes:[],dropdownBehaviours:[...s?[cS.config({})]:[]]},t,o.shared),o_=(e,t,o)=>{const n=e=>n=>{const s=!n.isActive();n.setActive(s),e.storage.set(s),o.shared.getSink().each((o=>{t().getOpt(o).each((t=>{Dl(t.element),Ir(t,vS,{name:e.name,value:e.storage.get()})}))}))},s=e=>t=>{t.setActive(e.storage.get())};return t=>{t(H(e,(e=>{const t=e.text.fold((()=>({})),(e=>({text:e})));return{type:e.type,active:!1,...t,onAction:n(e),onSetup:s(e)}})))}},n_=e=>({dom:{tag:"span",classes:["tox-tree__label"],attributes:{title:e,"aria-label":e}},components:[ti(e)]}),s_=la("leaf-label-event-id"),r_=({leaf:e,onLeafAction:t,visible:o,treeId:n,selectedId:s,backstage:r})=>{const a=e.menu.map((e=>t_(e,"tox-mbtn",r,A.none(),o))),i=[n_(e.title)];return a.each((e=>i.push(e))),Wh.sketch({dom:{tag:"div",classes:["tox-tree--leaf__label","tox-trbtn"].concat(o?["tox-tree--leaf__label--visible"]:[])},components:i,role:"treeitem",action:o=>{t(e.id),o.getSystem().broadcastOn([`update-active-item-${n}`],{value:e.id})},eventOrder:{[Ks()]:[s_,"keying"]},buttonBehaviours:kl([...o?[cS.config({})]:[],dh.config({toggleClass:"tox-trbtn--enabled",toggleOnExecute:!1,aria:{mode:"selected"}}),Al.config({channels:{[`update-active-item-${n}`]:{onReceive:(t,o)=>{(o.value===e.id?dh.on:dh.off)(t)}}}}),Jp(s_,[Kr(((t,o)=>{s.each((o=>{(o===e.id?dh.on:dh.off)(t)}))})),Ur(Ks(),((e,t)=>{const o="ArrowLeft"===t.event.raw.code,n="ArrowRight"===t.event.raw.code;o?(mi(e.element,".tox-tree--directory").each((t=>{e.getSystem().getByDom(t).each((e=>{gi(t,".tox-tree--directory__label").each((t=>{e.getSystem().getByDom(t).each(oh.focus)}))}))})),t.stop()):n&&t.stop()}))])])})},a_=la("directory-label-event-id"),i_=({directory:e,visible:t,noChildren:o,backstage:n})=>{const s=e.menu.map((e=>t_(e,"tox-mbtn",n,A.none()))),r=[{dom:{tag:"div",classes:["tox-chevron"]},components:[(a="chevron-right",i=n.shared.providers.icons,((e,t,o)=>ef(e,{tag:"span",classes:["tox-tree__icon-wrap","tox-icon"],behaviours:[]},t))(a,i))]},n_(e.title)];var a,i;s.each((e=>{r.push(e)}));const l=t=>{mi(t.element,".tox-tree--directory").each((o=>{t.getSystem().getByDom(o).each((o=>{const n=!dh.isOn(o);dh.toggle(o),Ir(t,"expand-tree-node",{expanded:n,node:e.id})}))}))};return Wh.sketch({dom:{tag:"div",classes:["tox-tree--directory__label","tox-trbtn"].concat(t?["tox-tree--directory__label--visible"]:[])},components:r,action:l,eventOrder:{[Ks()]:[a_,"keying"]},buttonBehaviours:kl([...t?[cS.config({})]:[],Jp(a_,[Ur(Ks(),((e,t)=>{const n="ArrowRight"===t.event.raw.code,s="ArrowLeft"===t.event.raw.code;n&&o&&t.stop(),(n||s)&&mi(e.element,".tox-tree--directory").each((o=>{e.getSystem().getByDom(o).each((o=>{!dh.isOn(o)&&n||dh.isOn(o)&&s?(l(e),t.stop()):s&&!dh.isOn(o)&&(mi(o.element,".tox-tree--directory").each((e=>{gi(e,".tox-tree--directory__label").each((e=>{o.getSystem().getByDom(e).each(oh.focus)}))})),t.stop())}))}))}))])])})},l_=({children:e,onLeafAction:t,visible:o,treeId:n,expandedIds:s,selectedId:r,backstage:a})=>({dom:{tag:"div",classes:["tox-tree--directory__children"]},components:e.map((e=>"leaf"===e.type?r_({leaf:e,selectedId:r,onLeafAction:t,visible:o,treeId:n,backstage:a}):d_({directory:e,expandedIds:s,selectedId:r,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:a}))),behaviours:kl([QO.config({dimension:{property:"height"},closedClass:"tox-tree--directory__children--closed",openClass:"tox-tree--directory__children--open",growingClass:"tox-tree--directory__children--growing",shrinkingClass:"tox-tree--directory__children--shrinking",expanded:o}),Kp.config({})])}),c_=la("directory-event-id"),d_=({directory:e,onLeafAction:t,labelTabstopping:o,treeId:n,backstage:s,expandedIds:r,selectedId:a})=>{const{children:i}=e,l=Es(r),c=r.includes(e.id);return{dom:{tag:"div",classes:["tox-tree--directory"],attributes:{role:"treeitem"}},components:[i_({directory:e,visible:o,noChildren:0===e.children.length,backstage:s}),l_({children:i,expandedIds:r,selectedId:a,onLeafAction:t,visible:c,treeId:n,backstage:s})],behaviours:kl([Jp(c_,[Kr(((e,t)=>{dh.set(e,c)})),Ur("expand-tree-node",((e,t)=>{const{expanded:o,node:n}=t.event;l.set(o?[...l.get(),n]:l.get().filter((e=>e!==n)))}))]),dh.config({...e.children.length>0?{aria:{mode:"expanded"}}:{},toggleClass:"tox-tree--directory--expanded",onToggled:(e,o)=>{const r=e.components()[1],c=(d=o,i.map((e=>"leaf"===e.type?r_({leaf:e,selectedId:a,onLeafAction:t,visible:d,treeId:n,backstage:s}):d_({directory:e,expandedIds:l.get(),selectedId:a,onLeafAction:t,labelTabstopping:d,treeId:n,backstage:s}))));var d;o?QO.grow(r):QO.shrink(r),Kp.set(r,c)}})])}},u_=la("tree-event-id");var m_=Object.freeze({__proto__:null,events:(e,t)=>{const o=e.stream.streams.setup(e,t);return Hr([Ur(e.event,o),Jr((()=>t.cancel()))].concat(e.cancelEvent.map((e=>[Ur(e,(()=>t.cancel()))])).getOr([])))}});const g_=e=>{const t=Es(null);return _a({readState:()=>({timer:null!==t.get()?"set":"unset"}),setTimer:e=>{t.set(e)},cancel:()=>{const e=t.get();null!==e&&e.cancel()}})};var p_=Object.freeze({__proto__:null,throttle:g_,init:e=>e.stream.streams.state(e)}),h_=[ns("stream",Jn("mode",{throttle:[os("delay"),ys("stopEvent",!0),Ri("streams",{setup:(e,t)=>{const o=e.stream,n=PC(e.onStream,o.delay);return t.setTimer(n),(e,t)=>{n.throttle(e,t),o.stopEvent&&t.stop()}},state:g_})]})),ys("event","input"),us("cancelEvent"),Fi("onStream")];const f_=Ol({fields:h_,name:"streaming",active:m_,state:p_}),b_=(e,t,o)=>{const n=pu.getValue(o);pu.setValue(t,n),y_(t)},v_=(e,t)=>{const o=e.element,n=$a(o),s=o.dom;"number"!==Ot(o,"type")&&t(s,n)},y_=e=>{v_(e,((e,t)=>e.setSelectionRange(t.length,t.length)))},x_=x("alloy.typeahead.itemexecute"),w_=x([us("lazySink"),os("fetch"),ys("minChars",5),ys("responseTime",1e3),Di("onOpen"),ys("getHotspot",A.some),ys("getAnchorOverrides",x({})),ys("layouts",A.none()),ys("eventOrder",{}),Ts("model",{},[ys("getDisplayText",(e=>void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.value)),ys("selectsOver",!0),ys("populateFromBrowse",!0)]),Di("onSetValue"),Bi("onExecute"),Di("onItemExecute"),ys("inputClasses",[]),ys("inputAttributes",{}),ys("inputStyles",{}),ys("matchWidth",!0),ys("useMinWidth",!1),ys("dismissOnBlur",!0),Ai(["openClass"]),us("initialData"),hu("typeaheadBehaviours",[oh,pu,f_,Pp,dh,uw]),es("lazyTypeaheadComp",(()=>Es(A.none))),es("previewing",(()=>Es(!0)))].concat(Fb()).concat(Ew())),S_=x([Wu({schema:[Ei()],name:"menu",overrides:e=>({fakeFocus:!0,onHighlightItem:(t,o,n)=>{e.previewing.get()?e.lazyTypeaheadComp.get().each((t=>{((e,t,o)=>{if(e.selectsOver){const n=pu.getValue(t),s=e.getDisplayText(n),r=pu.getValue(o);return 0===e.getDisplayText(r).indexOf(s)?A.some((()=>{b_(0,t,o),((e,t)=>{v_(e,((e,o)=>e.setSelectionRange(t,o.length)))})(t,s.length)})):A.none()}return A.none()})(e.model,t,n).fold((()=>{e.model.selectsOver?(Gm.dehighlight(o,n),e.previewing.set(!0)):e.previewing.set(!1)}),(t=>{t(),e.previewing.set(!1)}))})):e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&b_(e.model,t,n),_t(n.element,"id").each((e=>kt(t.element,"aria-activedescendant",e)))}))},onExecute:(t,o)=>e.lazyTypeaheadComp.get().map((e=>(Ir(e,x_(),{item:o}),!0))),onHover:(t,o)=>{e.previewing.set(!1),e.lazyTypeaheadComp.get().each((t=>{e.model.populateFromBrowse&&b_(e.model,t,o)}))}})})]),k_=bm({name:"Typeahead",configFields:w_(),partFields:S_(),factory:(e,t,o,n)=>{const s=(t,o,s)=>{e.previewing.set(!1);const r=uw.getCoupled(t,"sandbox");if(Xd.isOpen(r))wm.getCurrent(r).each((e=>{Gm.getHighlighted(e).fold((()=>{s(e)}),(()=>{zr(r,e.element,"keydown",o)}))}));else{const o=e=>{wm.getCurrent(e).each(s)};ww(e,a(t),t,r,n,o,zh.HighlightMenuAndItem).get(b)}},r=Ib(e),a=e=>t=>t.map((t=>{const o=fe(t.menus),n=X(o,(e=>U(e.items,(e=>"item"===e.type))));return pu.getState(e).update(H(n,(e=>e.data))),t})),i=e=>wm.getCurrent(e),l="typeaheadevents",c=[oh.config({}),pu.config({onSetValue:e.onSetValue,store:{mode:"dataset",getDataKey:e=>$a(e.element),getFallbackEntry:e=>({value:e,meta:{}}),setValue:(t,o)=>{qa(t.element,e.model.getDisplayText(o))},...e.initialData.map((e=>Ms("initialValue",e))).getOr({})}}),f_.config({stream:{mode:"throttle",delay:e.responseTime,stopEvent:!1},onStream:(t,o)=>{const s=uw.getCoupled(t,"sandbox");if(oh.isFocused(t)&&$a(t.element).length>=e.minChars){const o=i(s).bind((e=>Gm.getHighlighted(e).map(pu.getValue)));e.previewing.set(!0);const r=t=>{i(s).each((t=>{o.fold((()=>{e.model.selectsOver&&Gm.highlightFirst(t)}),(e=>{Gm.highlightBy(t,(t=>pu.getValue(t).value===e.value)),Gm.getHighlighted(t).orThunk((()=>(Gm.highlightFirst(t),A.none())))}))}))};ww(e,a(t),t,s,n,r,zh.HighlightJustMenu).get(b)}},cancelEvent:fr()}),Pp.config({mode:"special",onDown:(e,t)=>(s(e,t,Gm.highlightFirst),A.some(!0)),onEscape:e=>{const t=uw.getCoupled(e,"sandbox");return Xd.isOpen(t)?(Xd.close(t),A.some(!0)):A.none()},onUp:(e,t)=>(s(e,t,Gm.highlightLast),A.some(!0)),onEnter:t=>{const o=uw.getCoupled(t,"sandbox"),n=Xd.isOpen(o);if(n&&!e.previewing.get())return i(o).bind((e=>Gm.getHighlighted(e))).map((e=>(Ir(t,x_(),{item:e}),!0)));{const s=pu.getValue(t);return Fr(t,fr()),e.onExecute(o,t,s),n&&Xd.close(o),A.some(!0)}}}),dh.config({toggleClass:e.markers.openClass,aria:{mode:"expanded"}}),uw.config({others:{sandbox:t=>_w(e,t,{onOpen:()=>dh.on(t),onClose:()=>{e.lazyTypeaheadComp.get().each((e=>Et(e.element,"aria-activedescendant"))),dh.off(t)}})}}),Jp(l,[Kr((t=>{e.lazyTypeaheadComp.set(A.some(t))})),Jr((t=>{e.lazyTypeaheadComp.set(A.none())})),Qr((t=>{const o=b;kw(e,a(t),t,n,o,zh.HighlightMenuAndItem).get(b)})),Ur(x_(),((t,o)=>{const n=uw.getCoupled(t,"sandbox");b_(e.model,t,o.event.item),Fr(t,fr()),e.onItemExecute(t,n,o.event.item,pu.getValue(t)),Xd.close(n),y_(t)}))].concat(e.dismissOnBlur?[Ur(lr(),(e=>{const t=uw.getCoupled(e,"sandbox");Rl(t.element).isNone()&&Xd.close(t)}))]:[]))],d={[kr()]:[pu.name(),f_.name(),l],...e.eventOrder};return{uid:e.uid,dom:Nb(fn(e,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:{...r,...bu(e.typeaheadBehaviours,c)},eventOrder:d}}}),C_=e=>({...e,toCached:()=>C_(e.toCached()),bindFuture:t=>C_(e.bind((e=>e.fold((e=>bw(sn.error(e))),(e=>t(e)))))),bindResult:t=>C_(e.map((e=>e.bind(t)))),mapResult:t=>C_(e.map((e=>e.map(t)))),mapError:t=>C_(e.map((e=>e.mapError(t)))),foldResult:(t,o)=>e.map((e=>e.fold(t,o))),withTimeout:(t,o)=>C_(fw((n=>{let s=!1;const r=setTimeout((()=>{s=!0,n(sn.error(o()))}),t);e.get((e=>{s||(clearTimeout(r),n(e))}))})))}),O_=e=>C_(fw(e)),__=(e,t,o=[],n,s,r)=>{const a=t.fold((()=>({})),(e=>({action:e}))),i={buttonBehaviours:kl([ky((()=>!e.enabled||r.isDisabled())),Sy(),cS.config({}),Jp("button press",[Pr("click"),Pr("mousedown")])].concat(o)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]},...a},l=fn(i,{dom:n});return fn(l,{components:s})},T_=(e,t,o,n=[])=>{const s={tag:"button",classes:["tox-tbtn"],attributes:e.tooltip.map((e=>({"aria-label":o.translate(e),title:o.translate(e)}))).getOr({})},r=e.icon.map((e=>hO(e,o.icons))),a=Dy([r]);return __(e,t,n,s,a,o)},E_=e=>{switch(e){case"primary":return["tox-button"];case"toolbar":return["tox-tbtn"];default:return["tox-button","tox-button--secondary"]}},A_=(e,t,o,n=[],s=[])=>{const r=o.translate(e.text),a=e.icon.map((e=>hO(e,o.icons))),i=[a.getOrThunk((()=>ti(r)))],l=e.buttonType.getOr(e.primary||e.borderless?"primary":"secondary"),c=[...E_(l),...a.isSome()?["tox-button--icon"]:[],...e.borderless?["tox-button--naked"]:[],...s];return __(e,t,n,{tag:"button",classes:c,attributes:{title:r}},i,o)},M_=(e,t,o,n=[],s=[])=>{const r=A_(e,A.some(t),o,n,s);return Wh.sketch(r)},D_=(e,t)=>o=>{"custom"===t?Ir(o,vS,{name:e,value:{}}):"submit"===t?Fr(o,yS):"cancel"===t?Fr(o,bS):console.error("Unknown button type: ",t)},B_=(e,t,o)=>{if(((e,t)=>"menu"===t)(0,t)){const t=()=>r,n=e,s={...e,type:"menubutton",search:A.none(),onSetup:t=>(t.setEnabled(e.enabled),b),fetch:o_(n.items,t,o)},r=jh(t_(s,"tox-tbtn",o,A.none()));return r.asSpec()}if(((e,t)=>"custom"===t||"cancel"===t||"submit"===t)(0,t)){const n=D_(e.name,t),s={...e,borderless:!1};return M_(s,n,o.shared.providers,[])}if(((e,t)=>"togglebutton"===t)(0,t))return((e,t)=>{var o,n;const s=e.icon.map((e=>fO(e,t.icons))).map(jh),r=e.buttonType.getOr(e.primary?"primary":"secondary"),a={...e,name:null!==(o=e.name)&&void 0!==o?o:"",primary:"primary"===r,tooltip:A.from(e.tooltip),enabled:null!==(n=e.enabled)&&void 0!==n&&n,borderless:!1},i=a.tooltip.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),l=E_(null!=r?r:"secondary"),c=e.icon.isSome()&&e.text.isSome(),d={tag:"button",classes:[...l.concat(e.icon.isSome()?["tox-button--icon"]:[]),...e.active?["tox-button--enabled"]:[],...c?["tox-button--icon-and-text"]:[]],attributes:i},u=t.translate(e.text.getOr("")),m=ti(u),g=[...Dy([s.map((e=>e.asSpec()))]),...e.text.isSome()?[m]:[]],p=__(a,A.some((o=>{Ir(o,vS,{name:e.name,value:{setIcon:e=>{s.map((n=>n.getOpt(o).each((o=>{Kp.set(o,[fO(e,t.icons)])}))))}}})})),[],d,g,t);return Wh.sketch(p)})(e,o.shared.providers);throw console.error("Unknown footer button type: ",t),new Error("Unknown footer button type")},F_={type:"separator"},I_=e=>({type:"menuitem",value:e.url,text:e.title,meta:{attach:e.attach},onAction:b}),R_=(e,t)=>({type:"menuitem",value:t,text:e,meta:{attach:void 0},onAction:b}),N_=(e,t)=>(e=>H(e,I_))(((e,t)=>U(t,(t=>t.type===e)))(e,t)),V_=e=>N_("header",e.targets),z_=e=>N_("anchor",e.targets),H_=e=>A.from(e.anchorTop).map((e=>R_("<top>",e))).toArray(),L_=e=>A.from(e.anchorBottom).map((e=>R_("<bottom>",e))).toArray(),P_=(e,t)=>{const o=e.toLowerCase();return U(t,(e=>{var t;const n=void 0!==e.meta&&void 0!==e.meta.text?e.meta.text:e.text,s=null!==(t=e.value)&&void 0!==t?t:"";return Te(n.toLowerCase(),o)||Te(s.toLowerCase(),o)}))},U_=la("aria-invalid"),W_=(e,t)=>{e.dom.checked=t},j_=e=>e.dom.checked,G_=e=>(t,o,n,s)=>be(o,"name").fold((()=>e(o,s,A.none())),(r=>t.field(r,e(o,s,be(n,r))))),$_={bar:G_(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:H(e.items,t.interpreter)}))(e,t.shared))),collection:G_(((e,t,o)=>((e,t,o)=>{const n=e.label.map((e=>pS(e,t))),s=e=>(t,o)=>{hi(o.event.target,"[data-collection-item-value]").each((n=>{e(t,o,n,Ot(n,"data-collection-item-value"))}))},r=s(((o,n,s,r)=>{n.stop(),t.isDisabled()||Ir(o,vS,{name:e.name,value:r})})),a=[Ur(qs(),s(((e,t,o)=>{Dl(o)}))),Ur(er(),r),Ur(gr(),r),Ur(Xs(),s(((e,t,o)=>{pi(e.element,"."+kb).each((e=>{Pa(e,kb)})),La(o,kb)}))),Ur(Ys(),s((e=>{pi(e.element,"."+kb).each((e=>{Pa(e,kb)}))}))),Qr(s(((t,o,n,s)=>{Ir(t,vS,{name:e.name,value:s})})))],i=(e,t)=>H(Xc(e.element,".tox-collection__item"),t),l=aS.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==e.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:w},behaviours:kl([Rm.config({disabled:t.isDisabled,onDisabled:e=>{i(e,(e=>{La(e,"tox-collection__item--state-disabled"),kt(e,"aria-disabled",!0)}))},onEnabled:e=>{i(e,(e=>{Pa(e,"tox-collection__item--state-disabled"),Et(e,"aria-disabled")}))}}),Sy(),Kp.config({}),pu.config({store:{mode:"memory",initialValue:o.getOr([])},onSetValue:(o,n)=>{((o,n)=>{const s=H(n,(o=>{const n=Gh.translate(o.text),s=1===e.columns?`<div class="tox-collection__item-label">${n}</div>`:"",r=`<div class="tox-collection__item-icon">${o.icon}</div>`,a={_:" "," - ":" ","-":" "},i=n.replace(/\_| \- |\-/g,(e=>a[e]));return`<div class="tox-collection__item${t.isDisabled()?" tox-collection__item--state-disabled":""}" tabindex="-1" data-collection-item-value="${dS.encodeAllRaw(o.value)}" title="${i}" aria-label="${i}">${r}${s}</div>`})),r="auto"!==e.columns&&e.columns>1?z(s,e.columns):[s],a=H(r,(e=>`<div class="tox-collection__group">${e.join("")}</div>`));ta(o.element,a.join(""))})(o,n),"auto"===e.columns&&iy(o,5,"tox-collection__item").each((({numRows:e,numColumns:t})=>{Pp.setGridSize(o,e,t)})),Fr(o,kS)}}),cS.config({}),Pp.config((c=e.columns,1===c?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===c?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:".tox-collection__group",cell:`.${fb}`}})),Jp("collection-events",a)]),eventOrder:{[ur()]:["disabling","alloy.base.behaviour","collection-events"]}});var c;return uS(n,l,["tox-form__group--collection"],[])})(e,t.shared.providers,o))),alertbanner:G_(((e,t)=>((e,t)=>{const o=Jh(e.icon,t.icons);return oS.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in",`tox-notification--${e.level}`]},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:e.url?void 0:o},components:e.url?[Wh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:o,attributes:{title:t.translate(e.iconTooltip)}},action:t=>Ir(t,vS,{name:"alert-banner",value:e.url}),buttonBehaviours:kl([Zh()])})]:void 0},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:t.translate(e.text)}}]})})(e,t.shared.providers))),input:G_(((e,t,o)=>((e,t,o)=>VO({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:!e.enabled,classname:"tox-textfield",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),textarea:G_(((e,t,o)=>((e,t,o)=>VO({name:e.name,multiline:!0,label:e.label,inputMode:A.none(),placeholder:e.placeholder,flex:!0,disabled:!e.enabled,classname:"tox-textarea",validation:A.none(),maximized:e.maximized,data:o},t))(e,t.shared.providers,o))),label:G_(((e,t)=>((e,t)=>{const o="tox-label";return{dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"label",classes:[o,..."center"===e.align?[`${o}--center`]:[],..."end"===e.align?[`${o}--end`]:[]]},components:[ti(t.providers.translate(e.label))]},...H(e.items,t.interpreter)],behaviours:kl([DC(),Kp.config({}),(n=A.none(),NC(n,ea,ta)),Pp.config({mode:"acyclic"})])};var n})(e,t.shared))),iframe:(vE=(e,t,o)=>((e,t,o)=>{const n="tox-dialog__iframe",s=e.transparent?[]:[`${n}--opaque`],r=e.border?["tox-navobj-bordered"]:[],a={...e.label.map((e=>({title:e}))).getOr({}),...o.map((e=>({srcdoc:e}))).getOr({}),...e.sandboxed?{sandbox:"allow-scripts allow-same-origin"}:{}},i=((e,t)=>{const o=Es(e.getOr(""));return{getValue:e=>o.get(),setValue:(e,n)=>{if(o.get()!==n){const o=e.element,s=()=>kt(o,"srcdoc",n);t?cO.fold(x(lO),(e=>e.throttle))(o,n,s):s()}o.set(n)}}})(o,e.streamContent),l=e.label.map((e=>pS(e,t))),c=aS.parts.field({factory:{sketch:e=>GC(A.from(r),{uid:e.uid,dom:{tag:"iframe",attributes:a,classes:[n,...s]},behaviours:kl([cS.config({}),oh.config({}),RC(o,i.getValue,i.setValue),Al.config({channels:{[eO]:{onReceive:(e,t)=>{t.newFocus.each((t=>{rt(e.element).each((o=>{(Ze(e.element,t)?La:Pa)(o,"tox-navobj-bordered-focus")}))}))}}}})])})}});return uS(l,c,["tox-form__group--stretched"],[])})(e,t.shared.providers,o),(e,t,o,n)=>{const s=fn(t,{source:"dynamic"});return G_(vE)(e,s,o,n)}),button:G_(((e,t)=>((e,t)=>{const o=D_(e.name,"custom");return n=A.none(),s=aS.parts.field({factory:Wh,...A_(e,A.some(o),t,[VC(""),DC()])}),uS(n,s,[],[]);var n,s})(e,t.shared.providers))),checkbox:G_(((e,t,o)=>((e,t,o)=>{const n=e=>(e.element.dom.click(),A.some(!0)),s=aS.parts.field({factory:{sketch:w},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:kl([DC(),Rm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{rt(e.element).each((e=>La(e,"tox-checkbox--disabled")))},onEnabled:e=>{rt(e.element).each((e=>Pa(e,"tox-checkbox--disabled")))}}),cS.config({}),oh.config({}),NC(o,j_,W_),Pp.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Jp("checkbox-events",[Ur(Qs(),((t,o)=>{Ir(t,hS,{name:e.name})}))])])}),r=aS.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"]},components:[ti(t.translate(e.label))],behaviours:kl([IS.config({})])}),a=e=>ef("checked"===e?"selected":"unselected",{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+e]},t.icons),i=jh({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[a("checked"),a("unchecked")]});return aS.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[s,i.asSpec(),r],fieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),Sy()])})})(e,t.shared.providers,o))),colorinput:G_(((e,t,o)=>((e,t,o,n)=>{const s=aS.parts.field({factory:Vb,inputClasses:["tox-textfield"],data:n,onSetValue:e=>FS.run(e).get(b),inputBehaviours:kl([Rm.config({disabled:t.providers.isDisabled}),Sy(),cS.config({}),FS.config({invalidClass:"tox-textbox-field-invalid",getRoot:e=>rt(e.element),notify:{onValid:e=>{const t=pu.getValue(e);Ir(e,RS,{color:t})}},validator:{validateOnLoad:!1,validate:e=>{const t=pu.getValue(e);if(0===t.length)return bw(sn.value(!0));{const e=Re("span");Dt(e,"background-color",t);const o=Nt(e,"background-color").fold((()=>sn.error("blah")),(e=>sn.value(t)));return bw(o)}}}})]),selectOnFocus:!1}),r=e.label.map((e=>pS(e,t.providers))),a=(e,t)=>{Ir(e,NS,{value:t})},i=jh(((e,t)=>Dw.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:kl([ky(t.providers.isDisabled),Sy(),IS.config({}),cS.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:t.getSink,fetch:o=>fw((t=>e.fetch(t))).map((n=>A.from(Hw(fn(Zx(la("menu-value"),n,(t=>{e.onItemAction(o,t)}),e.columns,e.presets,pb.CLOSE_ON_EXECUTE,T,t.providers),{movement:ew(e.columns,e.presets)}))))),parts:{menu:Bb(0,0,e.presets)}}))({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:{onRtl:()=>[rl,sl,cl],onLtr:()=>[sl,rl,cl]},components:[],fetch:$x(o.getColors(e.storageKey),e.storageKey,o.hasCustomColors()),columns:o.getColorCols(e.storageKey),presets:"color",onItemAction:(t,n)=>{i.getOpt(t).each((t=>{"custom"===n?o.colorPicker((o=>{o.fold((()=>Fr(t,VS)),(o=>{a(t,o),Ox(e.storageKey,o)}))}),"#ffffff"):a(t,"remove"===n?"":n)}))}},t));return aS.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:r.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[s,i.asSpec()]}]),fieldBehaviours:kl([Jp("form-field-events",[Ur(RS,((t,o)=>{i.getOpt(t).each((e=>{Dt(e.element,"background-color",o.event.color)})),Ir(t,hS,{name:e.name})})),Ur(NS,((e,t)=>{aS.getField(e).each((o=>{pu.setValue(o,t.event.value),wm.getCurrent(e).each(oh.focus)}))})),Ur(VS,((e,t)=>{aS.getField(e).each((t=>{wm.getCurrent(e).each(oh.focus)}))}))])])})})(e,t.shared,t.colorinput,o))),colorpicker:G_(((e,t,o)=>((e,t,o)=>{const n=e=>"tox-"+e,s=MC((e=>t=>e.translate(zC[t]))(t),n),r=jh(s.sketch({dom:{tag:"div",classes:[n("color-picker-container")],attributes:{role:"presentation"}},onValidHex:e=>{Ir(e,vS,{name:"hex-valid",value:!0})},onInvalidHex:e=>{Ir(e,vS,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:kl([RC(o,(e=>{const t=r.get(e);return wm.getCurrent(t).bind((e=>pu.getValue(e).hex)).map((e=>"#"+_e(e,"#"))).getOr("")}),((e,t)=>{const o=A.from(/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t)).bind((e=>te(e,1))),n=r.get(e);wm.getCurrent(n).fold((()=>{console.log("Can not find form")}),(e=>{pu.setValue(e,{hex:o.getOr("")}),CC.getField(e,"hex").each((e=>{Fr(e,Zs())}))}))})),DC()])}})(0,t.shared.providers,o))),dropzone:G_(((e,t,o)=>((e,t,o)=>{const n=(e,t)=>{t.stop()},s=e=>(t,o)=>{L(e,(e=>{e(t,o)}))},r=(e,t)=>{var o;if(!Rm.isDisabled(e)){const n=t.event.raw;i(e,null===(o=n.dataTransfer)||void 0===o?void 0:o.files)}},a=(e,t)=>{const o=t.event.raw.target;i(e,o.files)},i=(o,n)=>{n&&(pu.setValue(o,((e,t)=>{const o=LC.explode(t.getOption("images_file_types"));return U(se(e),(e=>N(o,(t=>Ae(e.name.toLowerCase(),`.${t.toLowerCase()}`)))))})(n,t)),Ir(o,hS,{name:e.name}))},l=jh({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:kl([Jp("input-file-events",[qr(er()),qr(gr())])])}),c=e.label.map((e=>pS(e,t))),d=aS.parts.field({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:kl([VC(o.getOr([])),DC(),Rm.config({}),dh.config({toggleClass:"dragenter",toggleOnExecute:!1}),Jp("dropzone-events",[Ur("dragenter",s([n,dh.toggle])),Ur("dragleave",s([n,dh.toggle])),Ur("dragover",n),Ur("drop",s([n,r])),Ur(Qs(),a)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p"},components:[ti(t.translate("Drop an image here"))]},Wh.sketch({dom:{tag:"button",styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[ti(t.translate("Browse for an image")),l.asSpec()],action:e=>{l.get(e).element.dom.click()},buttonBehaviours:kl([cS.config({}),ky(t.isDisabled),Sy()])})]}]})}});return uS(c,d,["tox-form__group--stretched"],[])})(e,t.shared.providers,o))),grid:G_(((e,t)=>((e,t)=>({dom:{tag:"div",classes:["tox-form__grid",`tox-form__grid--${e.columns}col`]},components:H(e.items,t.interpreter)}))(e,t.shared))),listbox:G_(((e,t,o)=>((e,t,o)=>{const n=t.shared.providers,s=o.bind((t=>EO(e.items,t))).orThunk((()=>oe(e.items).filter(OO))),r=e.label.map((e=>pS(e,n))),a=aS.parts.field({dom:{},factory:{sketch:o=>xO({uid:o.uid,text:s.map((e=>e.text)),icon:A.none(),tooltip:e.label,role:A.none(),fetch:(o,n)=>{const s=TO(o,e.name,e.items,pu.getValue(o));n(CO(s,pb.CLOSE_ON_EXECUTE,t,{isHorizontalMenu:!1,search:A.none()}))},onSetup:x(b),getApi:x({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[cS.config({}),RC(s.map((e=>e.value)),(e=>Ot(e.element,_O)),((t,o)=>{EO(e.items,o).each((e=>{kt(t.element,_O,e.value),Ir(t,vO,{text:e.text})}))}))]},"tox-listbox",t.shared)}}),i={dom:{tag:"div",classes:["tox-listboxfield"]},components:[a]};return aS.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([r.toArray(),[i]]),fieldBehaviours:kl([Rm.config({disabled:x(!e.enabled),onDisabled:e=>{aS.getField(e).each(Rm.disable)},onEnabled:e=>{aS.getField(e).each(Rm.enable)}})])})})(e,t,o))),selectbox:G_(((e,t,o)=>((e,t,o)=>{const n=H(e.items,(e=>({text:t.translate(e.text),value:e.value}))),s=e.label.map((e=>pS(e,t))),r=aS.parts.field({dom:{},...o.map((e=>({data:e}))).getOr({}),selectAttributes:{size:e.size},options:n,factory:AO,selectBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),cS.config({}),Jp("selectbox-change",[Ur(Qs(),((t,o)=>{Ir(t,hS,{name:e.name})}))])])}),a=e.size>1?A.none():A.some(ef("chevron-down",{tag:"div",classes:["tox-selectfield__icon-js"]},t.icons)),i={dom:{tag:"div",classes:["tox-selectfield"]},components:q([[r],a.toArray()])};return aS.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:q([s.toArray(),[i]]),fieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{aS.getField(e).each(Rm.disable)},onEnabled:e=>{aS.getField(e).each(Rm.enable)}}),Sy()])})})(e,t.shared.providers,o))),sizeinput:G_(((e,t)=>((e,t)=>{let o=NO;const n=la("ratio-event"),s=e=>ef(e,{tag:"span",classes:["tox-icon","tox-lock-icon__"+e]},t.icons),r=FO.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(e.label.getOr("Constrain proportions"))}},components:[s("lock"),s("unlock")],buttonBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),Sy(),cS.config({})])}),a=e=>({dom:{tag:"div",classes:["tox-form__group"]},components:e}),i=o=>aS.parts.field({factory:Vb,inputClasses:["tox-textfield"],inputBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled()}),Sy(),cS.config({}),Jp("size-input-events",[Ur(Xs(),((e,t)=>{Ir(e,n,{isField1:o})})),Ur(Qs(),((t,o)=>{Ir(t,hS,{name:e.name})}))])]),selectOnFocus:!1}),l=e=>({dom:{tag:"label",classes:["tox-label"]},components:[ti(t.translate(e))]}),c=FO.parts.field1(a([aS.parts.label(l("Width")),i(!0)])),d=FO.parts.field2(a([aS.parts.label(l("Height")),i(!1)]));return FO.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,d,a([l("\xa0"),r])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:(e,t,n)=>{IO(pu.getValue(e)).each((e=>{o(e).each((e=>{pu.setValue(t,(e=>{const t={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,in:4,"%":4};let o=e.value.toFixed((n=e.unit)in t?t[n]:1);var n;return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+e.unit})(e))}))}))},coupledFieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||t.isDisabled(),onDisabled:e=>{FO.getField1(e).bind(aS.getField).each(Rm.disable),FO.getField2(e).bind(aS.getField).each(Rm.disable),FO.getLock(e).each(Rm.disable)},onEnabled:e=>{FO.getField1(e).bind(aS.getField).each(Rm.enable),FO.getField2(e).bind(aS.getField).each(Rm.enable),FO.getLock(e).each(Rm.enable)}}),Sy(),Jp("size-input-events2",[Ur(n,((e,t)=>{const n=t.event.isField1,s=n?FO.getField1(e):FO.getField2(e),r=n?FO.getField2(e):FO.getField1(e),a=s.map(pu.getValue).getOr(""),i=r.map(pu.getValue).getOr("");o=((e,t)=>{const o=IO(e).toOptional(),n=IO(t).toOptional();return Se(o,n,((e,t)=>RO(e,t.unit).map((e=>t.value/e)).map((e=>{return o=e,n=t.unit,e=>RO(e,n).map((e=>({value:e*o,unit:n})));var o,n})).getOr(NO))).getOr(NO)})(a,i)}))])])})})(e,t.shared.providers))),slider:G_(((e,t,o)=>((e,t,o)=>{const n=fC.parts.label({dom:{tag:"label",classes:["tox-label"]},components:[ti(t.translate(e.label))]}),s=fC.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),r=fC.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return fC.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e.min,maxX:e.max,getInitialValue:x(o.getOrThunk((()=>(Math.abs(e.max)-Math.abs(e.min))/2)))},components:[n,s,r],sliderBehaviours:kl([DC(),oh.config({})]),onChoose:(t,o,n)=>{Ir(t,hS,{name:e.name,value:n})}})})(e,t.shared.providers,o))),urlinput:G_(((e,t,o)=>((e,t,o,n)=>{const s=t.shared.providers,r=t=>{const n=pu.getValue(t);o.addToHistory(n.value,e.filetype)},a={...n.map((e=>({initialData:e}))).getOr({}),dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":U_,type:"url"},minChars:0,responseTime:0,fetch:n=>{const s=((e,t,o)=>{var n,s;const r=pu.getValue(t),a=null!==(s=null===(n=null==r?void 0:r.meta)||void 0===n?void 0:n.text)&&void 0!==s?s:r.value;return o.getLinkInformation().fold((()=>[]),(t=>{const n=P_(a,(e=>H(e,(e=>R_(e,e))))(o.getHistory(e)));return"file"===e?(s=[n,P_(a,V_(t)),P_(a,q([H_(t),z_(t),L_(t)]))],j(s,((e,t)=>0===e.length||0===t.length?e.concat(t):e.concat(F_,t)),[])):n;var s}))})(e.filetype,n,o),r=CO(s,pb.BUBBLE_TO_SANDBOX,t,{isHorizontalMenu:!1,search:A.none()});return bw(r)},getHotspot:e=>g.getOpt(e),onSetValue:(e,t)=>{e.hasConfigured(FS)&&FS.run(e).get(b)},typeaheadBehaviours:kl([...o.getValidationHandler().map((t=>FS.config({getRoot:e=>rt(e.element),invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:(e,t)=>{c.getOpt(e).each((e=>{kt(e.element,"title",s.translate(t))}))}},validator:{validate:o=>{const n=pu.getValue(o);return O_((o=>{t({type:e.filetype,url:n.value},(e=>{if("invalid"===e.status){const t=sn.error(e.message);o(t)}else{const t=sn.value(e.message);o(t)}}))}))},validateOnLoad:!1}}))).toArray(),Rm.config({disabled:()=>!e.enabled||s.isDisabled()}),cS.config({}),Jp("urlinput-events",[Ur(Zs(),(t=>{const o=$a(t.element),n=o.trim();n!==o&&qa(t.element,n),"file"===e.filetype&&Ir(t,hS,{name:e.name})})),Ur(Qs(),(t=>{Ir(t,hS,{name:e.name}),r(t)})),Ur(cr(),(t=>{Ir(t,hS,{name:e.name}),r(t)}))])]),eventOrder:{[Zs()]:["streaming","urlinput-events","invalidating"]},model:{getDisplayText:e=>e.value,selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:t.shared.getSink,parts:{menu:Bb(0,0,"normal")},onExecute:(e,t,o)=>{Ir(t,yS,{})},onItemExecute:(t,o,n,s)=>{r(t),Ir(t,hS,{name:e.name})}},i=aS.parts.field({...a,factory:k_}),l=e.label.map((e=>pS(e,s))),c=jh(((e,t,o=e,n=e)=>ef(o,{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+e],attributes:{title:s.translate(n),"aria-live":"polite",...t.fold((()=>({})),(e=>({id:e})))}},s.icons))("invalid",A.some(U_),"warning")),d=jh({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[c.asSpec()]}),u=o.getUrlPicker(e.filetype),m=la("browser.url.event"),g=jh({dom:{tag:"div",classes:["tox-control-wrap"]},components:[i,d.asSpec()],behaviours:kl([Rm.config({disabled:()=>!e.enabled||s.isDisabled()})])}),p=jh(M_({name:e.name,icon:A.some("browse"),text:e.label.getOr(""),enabled:e.enabled,primary:!1,buttonType:A.none(),borderless:!0},(e=>Fr(e,m)),s,[],["tox-browse-url"]));return aS.sketch({dom:gS([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:q([[g.asSpec()],u.map((()=>p.asSpec())).toArray()])}]),fieldBehaviours:kl([Rm.config({disabled:()=>!e.enabled||s.isDisabled(),onDisabled:e=>{aS.getField(e).each(Rm.disable),p.getOpt(e).each(Rm.disable)},onEnabled:e=>{aS.getField(e).each(Rm.enable),p.getOpt(e).each(Rm.enable)}}),Sy(),Jp("url-input-events",[Ur(m,(t=>{wm.getCurrent(t).each((o=>{const n=pu.getValue(o),s={fieldname:e.name,...n};u.each((n=>{n(s).get((n=>{pu.setValue(o,n),Ir(t,hS,{name:e.name})}))}))}))}))])])})})(e,t,t.urlinput,o))),customeditor:G_((e=>{const t=Ql(),o=jh({dom:{tag:e.tag}}),n=Ql();return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:kl([Jp("custom-editor-events",[Kr((s=>{o.getOpt(s).each((o=>{((e=>ve(e,"init"))(e)?e.init(o.element.dom):HC.load(e.scriptId,e.scriptUrl).then((t=>t(o.element.dom,e.settings)))).then((e=>{n.on((t=>{e.setValue(t)})),n.clear(),t.set(e)}))}))}))]),RC(A.none(),(()=>t.get().fold((()=>n.get().getOr("")),(e=>e.getValue()))),((e,o)=>{t.get().fold((()=>n.set(o)),(e=>e.setValue(o)))})),DC()]),components:[o.asSpec()]}})),htmlpanel:G_((e=>"presentation"===e.presets?oS.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html}}):oS.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:e.html,attributes:{role:"document"}},containerBehaviours:kl([cS.config({}),oh.config({})])}))),imagepreview:G_(((e,t,o)=>((e,t)=>{const o=Es(t.getOr({url:""})),n=jh({dom:{tag:"img",classes:["tox-imagepreview__image"],attributes:t.map((e=>({src:e.url}))).getOr({})}}),s=jh({dom:{tag:"div",classes:["tox-imagepreview__container"],attributes:{role:"presentation"}},components:[n.asSpec()]}),r={};e.height.each((e=>r.height=e));const a=t.map((e=>({url:e.url,zoom:A.from(e.zoom),cachedWidth:A.from(e.cachedWidth),cachedHeight:A.from(e.cachedHeight)})));return{dom:{tag:"div",classes:["tox-imagepreview"],styles:r,attributes:{role:"presentation"}},components:[s.asSpec()],behaviours:kl([DC(),RC(a,(()=>o.get()),((e,t)=>{const r={url:t.url};t.zoom.each((e=>r.zoom=e)),t.cachedWidth.each((e=>r.cachedWidth=e)),t.cachedHeight.each((e=>r.cachedHeight=e)),o.set(r);const a=()=>{const{cachedWidth:t,cachedHeight:o,zoom:n}=r;if(!u(t)&&!u(o)){if(u(n)){const n=((e,t,o)=>{const n=Jt(e),s=Wt(e);return Math.min(n/t,s/o,1)})(e.element,t,o);r.zoom=n}const a=((e,t,o,n,s)=>{const r=o*s,a=n*s,i=Math.max(0,e/2-r/2),l=Math.max(0,t/2-a/2);return{left:i.toString()+"px",top:l.toString()+"px",width:r.toString()+"px",height:a.toString()+"px"}})(Jt(e.element),Wt(e.element),t,o,r.zoom);s.getOpt(e).each((e=>{Bt(e.element,a)}))}};n.getOpt(e).each((o=>{const n=o.element;var s;t.url!==Ot(n,"src")&&(kt(n,"src",t.url),Pa(e.element,"tox-imagepreview__loaded")),a(),(s=n,new Promise(((e,t)=>{const o=()=>{r(),e(s)},n=[tc(s,"load",o),tc(s,"error",(()=>{r(),t("Unable to load data from image: "+s.dom.src)}))],r=()=>L(n,(e=>e.unbind()));s.dom.complete&&o()}))).then((t=>{e.getSystem().isConnected()&&(La(e.element,"tox-imagepreview__loaded"),r.cachedWidth=t.dom.naturalWidth,r.cachedHeight=t.dom.naturalHeight,a())}))}))}))])}})(e,o))),table:G_(((e,t)=>((e,t)=>{const o=e=>({dom:{tag:"td",innerHtml:t.translate(e)}});return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(s=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:H(s,(e=>({dom:{tag:"th",innerHtml:t.translate(e)}})))}]}),(n=e.cells,{dom:{tag:"tbody"},components:H(n,(e=>({dom:{tag:"tr"},components:H(e,o)})))})],behaviours:kl([cS.config({}),oh.config({})])};var n,s})(e,t.shared.providers))),tree:G_(((e,t)=>((e,t)=>{const o=e.onLeafAction.getOr(b),n=e.onToggleExpand.getOr(b),s=e.defaultExpandedIds,r=Es(s),a=Es(e.defaultSelectedId),i=la("tree-id"),l=(n,s)=>e.items.map((e=>"leaf"===e.type?r_({leaf:e,selectedId:n,onLeafAction:o,visible:!0,treeId:i,backstage:t}):d_({directory:e,selectedId:n,onLeafAction:o,expandedIds:s,labelTabstopping:!0,treeId:i,backstage:t})));return{dom:{tag:"div",classes:["tox-tree"],attributes:{role:"tree"}},components:l(a.get(),r.get()),behaviours:kl([Pp.config({mode:"flow",selector:".tox-tree--leaf__label--visible, .tox-tree--directory__label--visible",cycles:!1}),Jp(u_,[Ur("expand-tree-node",((e,t)=>{const{expanded:o,node:s}=t.event;r.set(o?[...r.get(),s]:r.get().filter((e=>e!==s))),n(r.get(),{expanded:o,node:s})}))]),Al.config({channels:{[`update-active-item-${i}`]:{onReceive:(e,t)=>{a.set(A.some(t.value)),Kp.set(e,l(A.some(t.value),r.get()))}}}}),Kp.config({})])}})(e,t))),panel:G_(((e,t)=>((e,t)=>({dom:{tag:"div",classes:e.classes},components:H(e.items,t.shared.interpreter)}))(e,t)))},q_={field:(e,t)=>t,record:x([])},X_=(e,t,o,n)=>{const s=fn(n,{shared:{interpreter:t=>Y_(e,t,o,s)}});return Y_(e,t,o,s)},Y_=(e,t,o,n)=>be($_,t.type).fold((()=>(console.error(`Unknown factory type "${t.type}", defaulting to container: `,t),t)),(s=>s(e,t,o,n))),K_=(e,t,o)=>Y_(q_,e,t,o),J_="layout-inset",Z_=e=>e.x,Q_=(e,t)=>e.x+e.width/2-t.width/2,eT=(e,t)=>e.x+e.width-t.width,tT=e=>e.y,oT=(e,t)=>e.y+e.height-t.height,nT=(e,t)=>e.y+e.height/2-t.height/2,sT=(e,t,o)=>zi(eT(e,t),oT(e,t),o.insetSouthwest(),Wi(),"southwest",Ki(e,{right:0,bottom:3}),J_),rT=(e,t,o)=>zi(Z_(e),oT(e,t),o.insetSoutheast(),Ui(),"southeast",Ki(e,{left:1,bottom:3}),J_),aT=(e,t,o)=>zi(eT(e,t),tT(e),o.insetNorthwest(),Pi(),"northwest",Ki(e,{right:0,top:2}),J_),iT=(e,t,o)=>zi(Z_(e),tT(e),o.insetNortheast(),Li(),"northeast",Ki(e,{left:1,top:2}),J_),lT=(e,t,o)=>zi(Q_(e,t),tT(e),o.insetNorth(),ji(),"north",Ki(e,{top:2}),J_),cT=(e,t,o)=>zi(Q_(e,t),oT(e,t),o.insetSouth(),Gi(),"south",Ki(e,{bottom:3}),J_),dT=(e,t,o)=>zi(eT(e,t),nT(e,t),o.insetEast(),qi(),"east",Ki(e,{right:0}),J_),uT=(e,t,o)=>zi(Z_(e),nT(e,t),o.insetWest(),$i(),"west",Ki(e,{left:1}),J_),mT=e=>{switch(e){case"north":return lT;case"northeast":return iT;case"northwest":return aT;case"south":return cT;case"southeast":return rT;case"southwest":return sT;case"east":return dT;case"west":return uT}},gT=(e,t,o,n,s)=>Xl(n).map(mT).getOr(lT)(e,t,o,n,s),pT=e=>{switch(e){case"north":return cT;case"northeast":return rT;case"northwest":return sT;case"south":return lT;case"southeast":return iT;case"southwest":return aT;case"east":return uT;case"west":return dT}},hT=(e,t,o,n,s)=>Xl(n).map(pT).getOr(lT)(e,t,o,n,s),fT={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},bT=(e,t,o)=>{const n={maxHeightFunction:cc()};return()=>o()?{type:"node",root:ft(ht(e())),node:A.from(e()),bubble:gc(12,12,fT),layouts:{onRtl:()=>[iT],onLtr:()=>[aT]},overrides:n}:{type:"hotspot",hotspot:t(),bubble:gc(-12,12,fT),layouts:{onRtl:()=>[sl,rl,cl],onLtr:()=>[rl,sl,cl]},overrides:n}},vT=(e,t,o,n)=>{const s={maxHeightFunction:cc()};return()=>n()?{type:"node",root:ft(ht(t())),node:A.from(t()),bubble:gc(12,12,fT),layouts:{onRtl:()=>[lT],onLtr:()=>[lT]},overrides:s}:e?{type:"node",root:ft(ht(t())),node:A.from(t()),bubble:gc(0,-jt(t()),fT),layouts:{onRtl:()=>[ll],onLtr:()=>[ll]},overrides:s}:{type:"hotspot",hotspot:o(),bubble:gc(0,0,fT),layouts:{onRtl:()=>[ll],onLtr:()=>[ll]},overrides:s}},yT=(e,t,o)=>()=>o()?{type:"node",root:ft(ht(e())),node:A.from(e()),layouts:{onRtl:()=>[lT],onLtr:()=>[lT]}}:{type:"hotspot",hotspot:t(),layouts:{onRtl:()=>[cl],onLtr:()=>[cl]}},xT=(e,t)=>()=>({type:"selection",root:t(),getSelection:()=>{const t=e.selection.getRng(),o=e.model.table.getSelectedCells();if(o.length>1){const e=o[0],t=o[o.length-1],n={firstCell:Ve(e),lastCell:Ve(t)};return A.some(n)}return A.some(Lc.range(Ve(t.startContainer),t.startOffset,Ve(t.endContainer),t.endOffset))}}),wT=e=>t=>({type:"node",root:e(),node:t}),ST=(e,t,o,n)=>{const s=sb(e),r=()=>Ve(e.getBody()),a=()=>Ve(e.getContentAreaContainer()),i=()=>s||!n();return{inlineDialog:bT(a,t,i),inlineBottomDialog:vT(e.inline,a,o,i),banner:yT(a,t,i),cursor:xT(e,r),node:wT(r)}},kT=e=>(t,o)=>{Jx(e)(t,o)},CT=e=>()=>Hx(e),OT=e=>t=>Rx(e,t),_T=e=>t=>zx(e,t),TT=e=>()=>Lf(e),ET=e=>ye(e,"items"),AT=e=>ye(e,"format"),MT=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],DT=e=>j(e,((e,t)=>{if(ve(t,"items")){const o=DT(t.items);return{customFormats:e.customFormats.concat(o.customFormats),formats:e.formats.concat([{title:t.title,items:o.formats}])}}if(ve(t,"inline")||(e=>ve(e,"block"))(t)||(e=>ve(e,"selector"))(t)){const o=`custom-${r(t.name)?t.name:t.title.toLowerCase()}`;return{customFormats:e.customFormats.concat([{name:o,format:t}]),formats:e.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return{...e,formats:e.formats.concat(t)}}),{customFormats:[],formats:[]}),BT=e=>yf(e).map((t=>{const o=((e,t)=>{const o=DT(t),n=t=>{L(t,(t=>{e.formatter.has(t.name)||e.formatter.register(t.name,t.format)}))};return e.formatter?n(o.customFormats):e.on("init",(()=>{n(o.customFormats)})),o.formats})(e,t);return xf(e)?MT.concat(o):o})).getOr(MT),FT=(e,t,o)=>({...e,type:"formatter",isSelected:t(e.format),getStylePreview:o(e.format)}),IT=(e,t,o,n)=>{const s=t=>H(t,(t=>ET(t)?(e=>{const t=s(e.items);return{...e,type:"submenu",getStyleItems:x(t)}})(t):AT(t)?(e=>FT(e,o,n))(t):(e=>{const t=ae(e);return 1===t.length&&R(t,"title")})(t)?{...t,type:"separator"}:(t=>{const s=r(t.name)?t.name:la(t.title),a=`custom-${s}`,i={...t,type:"formatter",format:a,isSelected:o(a),getStylePreview:n(a)};return e.formatter.register(s,i),i})(t)));return s(t)},RT=LC.trim,NT=e=>t=>{if((e=>g(e)&&1===e.nodeType)(t)){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}return!1},VT=NT("true"),zT=NT("false"),HT=(e,t,o,n,s)=>({type:e,title:t,url:o,level:n,attach:s}),LT=e=>e.innerText||e.textContent,PT=e=>(e=>e&&"A"===e.nodeName&&void 0!==(e.id||e.name))(e)&&WT(e),UT=e=>e&&/^(H[1-6])$/.test(e.nodeName),WT=e=>(e=>{let t=e;for(;t=t.parentNode;){const e=t.contentEditable;if(e&&"inherit"!==e)return VT(t)}return!1})(e)&&!zT(e),jT=e=>UT(e)&&WT(e),GT=e=>{var t;const o=(e=>e.id?e.id:la("h"))(e);return HT("header",null!==(t=LT(e))&&void 0!==t?t:"","#"+o,(e=>UT(e)?parseInt(e.nodeName.substr(1),10):0)(e),(()=>{e.id=o}))},$T=e=>{const t=e.id||e.name,o=LT(e);return HT("anchor",o||"#"+t,"#"+t,0,b)},qT=e=>RT(e.title).length>0,XT=e=>{const t=(e=>{const t=H(Xc(Ve(e),"h1,h2,h3,h4,h5,h6,a:not([href])"),(e=>e.dom));return t})(e);return U((e=>H(U(e,jT),GT))(t).concat((e=>H(U(e,PT),$T))(t)),qT)},YT="tinymce-url-history",KT=e=>r(e)&&/^https?/.test(e),JT=e=>a(e)&&he(e,(e=>{return!(l(t=e)&&t.length<=5&&Y(t,KT));var t})).isNone(),ZT=()=>{const e=Sx.getItem(YT);if(null===e)return{};let t;try{t=JSON.parse(e)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+YT+" was not valid JSON",e),{};throw e}return JT(t)?t:(console.log("Local storage "+YT+" was not valid format",t),{})},QT=e=>{const t=ZT();return be(t,e).getOr([])},eE=(e,t)=>{if(!KT(e))return;const o=ZT(),n=be(o,t).getOr([]),s=U(n,(t=>t!==e));o[t]=[e].concat(s).slice(0,5),(e=>{if(!JT(e))throw new Error("Bad format for history:\n"+JSON.stringify(e));Sx.setItem(YT,JSON.stringify(e))})(o)},tE=e=>!!e,oE=e=>ce(LC.makeMap(e,/[, ]/),tE),nE=e=>A.from(Ff(e)),sE=e=>A.from(e).filter(r).getOrUndefined(),rE=e=>({getHistory:QT,addToHistory:eE,getLinkInformation:()=>(e=>Vf(e)?A.some({targets:XT(e.getBody()),anchorTop:sE(zf(e)),anchorBottom:sE(Hf(e))}):A.none())(e),getValidationHandler:()=>(e=>A.from(If(e)))(e),getUrlPicker:t=>((e,t)=>((e,t)=>{const o=(e=>{const t=A.from(Nf(e)).filter(tE).map(oE);return nE(e).fold(T,(e=>t.fold(E,(e=>ae(e).length>0&&e))))})(e);return d(o)?o?nE(e):A.none():o[t]?nE(e):A.none()})(e,t).map((o=>n=>fw((s=>{const i={filetype:t,fieldname:n.fieldname,...A.from(n.meta).getOr({})};o.call(e,((e,t)=>{if(!r(e))throw new Error("Expected value to be string");if(void 0!==t&&!a(t))throw new Error("Expected meta to be a object");s({value:e,meta:t})}),n.value,i)})))))(e,t)}),aE=dm,iE=qu,lE=x([ys("shell",!1),os("makeItem"),ys("setupItem",b),vu("listBehaviours",[Kp])]),cE=ju({name:"items",overrides:()=>({behaviours:kl([Kp.config({})])})}),dE=x([cE]),uE=bm({name:x("CustomList")(),configFields:lE(),partFields:dE(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Kp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:bu(e.listBehaviours,s.behaviours),apis:{setItems:(t,o)=>{var n;(n=t,e.shell?A.some(n):om(n,e,"items")).fold((()=>{throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")}),(n=>{const s=Kp.contents(n),r=o.length,a=r-s.length,i=a>0?V(a,(()=>e.makeItem())):[],l=s.slice(r);L(l,(e=>Kp.remove(n,e))),L(i,(e=>Kp.append(n,e)));const c=Kp.contents(n);L(c,((n,s)=>{e.setupItem(t,n,o[s],s)}))}))}}}},apis:{setItems:(e,t,o)=>{e.setItems(t,o)}}}),mE=x([os("dom"),ys("shell",!0),hu("toolbarBehaviours",[Kp])]),gE=x([ju({name:"groups",overrides:()=>({behaviours:kl([Kp.config({})])})})]),pE=bm({name:"Toolbar",configFields:mE(),partFields:gE(),factory:(e,t,o,n)=>{const s=e.shell?{behaviours:[Kp.config({})],components:[]}:{behaviours:[],components:t};return{uid:e.uid,dom:e.dom,components:s.components,behaviours:bu(e.toolbarBehaviours,s.behaviours),apis:{setGroups:(t,o)=>{var n;(n=t,e.shell?A.some(n):om(n,e,"groups")).fold((()=>{throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")}),(e=>{Kp.set(e,o)}))},refresh:b},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)}}}),hE=b,fE=T,bE=x([]);var vE,yE=Object.freeze({__proto__:null,setup:hE,isDocked:fE,getBehaviours:bE});const xE=e=>(xe(Nt(e,"position"),"fixed")?A.none():at(e)).orThunk((()=>{const t=Re("span");return st(e).bind((e=>{zo(e,t);const o=at(t);return Po(t),o}))})),wE=e=>xE(e).map(Xt).getOrThunk((()=>$t(0,0))),SE=(e,t)=>{const o=e.element;La(o,t.transitionClass),Pa(o,t.fadeOutClass),La(o,t.fadeInClass),t.onShow(e)},kE=(e,t)=>{const o=e.element;La(o,t.transitionClass),Pa(o,t.fadeInClass),La(o,t.fadeOutClass),t.onHide(e)},CE=(e,t)=>e.y>=t.y,OE=(e,t)=>e.bottom<=t.bottom,_E=(e,t,o)=>({location:"top",leftX:t,topY:o.bounds.y-e.y}),TE=(e,t,o)=>({location:"bottom",leftX:t,bottomY:e.bottom-o.bounds.bottom}),EE=e=>e.box.x-e.win.x,AE=(e,t,o)=>o.getInitialPos().map((o=>{const n=((e,t)=>{const o=t.optScrollEnv.fold(x(e.bounds.y),(t=>t.scrollElmTop+(e.bounds.y-t.currentScrollTop)));return $t(e.bounds.x,o)})(o,t);return{box:Ko(n.left,n.top,Jt(e),Wt(e)),location:o.location}})),ME=(e,t,o,n,s)=>{const r=((e,t)=>{const o=t.optScrollEnv.fold(x(e.y),(t=>e.y+t.currentScrollTop-t.scrollElmTop));return $t(e.x,o)})(t,o),a=Ko(r.left,r.top,t.width,t.height);n.setInitialPos({style:Vt(e),position:It(e,"position")||"static",bounds:a,location:s.location})},DE=(e,t,o)=>o.getInitialPos().bind((n=>{var s;switch(o.clearInitialPos(),n.position){case"static":return A.some({morph:"static"});case"absolute":const o=xE(e).getOr(xt()),r=Jo(o),a=null!==(s=o.dom.scrollTop)&&void 0!==s?s:0;return A.some({morph:"absolute",positionCss:Vl("absolute",be(n.style,"left").map((e=>t.x-r.x)),be(n.style,"top").map((e=>t.y-r.y+a)),be(n.style,"right").map((e=>r.right-t.right)),be(n.style,"bottom").map((e=>r.bottom-t.bottom)))});default:return A.none()}})),BE=e=>{switch(e.location){case"top":return A.some({morph:"fixed",positionCss:Vl("fixed",A.some(e.leftX),A.some(e.topY),A.none(),A.none())});case"bottom":return A.some({morph:"fixed",positionCss:Vl("fixed",A.some(e.leftX),A.none(),A.none(),A.some(e.bottomY))});default:return A.none()}},FE=(e,t,o)=>{const n=e.element;return xe(Nt(n,"position"),"fixed")?((e,t,o)=>((e,t,o)=>AE(e,t,o).filter((({box:e})=>((e,t,o)=>Y(e,(e=>{switch(e){case"bottom":return OE(t,o.bounds);case"top":return CE(t,o.bounds)}})))(o.getModes(),e,t))).bind((({box:t})=>DE(e,t,o))))(e,t,o).orThunk((()=>t.optScrollEnv.bind((n=>AE(e,t,o))).bind((({box:e,location:o})=>{const n=en(),s=EE({win:n,box:e}),r="top"===o?_E(n,s,t):TE(n,s,t);return BE(r)})))))(n,t,o):((e,t,o)=>{const n=Jo(e),s=en(),r=((e,t,o)=>{const n=t.win,s=t.box,r=EE(t);return re(e,(e=>{switch(e){case"bottom":return OE(s,o.bounds)?A.none():A.some(TE(n,r,o));case"top":return CE(s,o.bounds)?A.none():A.some(_E(n,r,o));default:return A.none()}})).getOr({location:"no-dock"})})(o.getModes(),{win:s,box:n},t);return"top"===r.location||"bottom"===r.location?(ME(e,n,t,o,r),BE(r)):A.none()})(n,t,o)},IE=(e,t,o)=>{o.setDocked(!1),L(["left","right","top","bottom","position"],(t=>Ht(e.element,t))),t.onUndocked(e)},RE=(e,t,o,n)=>{const s="fixed"===n.position;o.setDocked(s),zl(e.element,n),(s?t.onDocked:t.onUndocked)(e)},NE=(e,t,o,n,s=!1)=>{t.contextual.each((t=>{t.lazyContext(e).each((r=>{const a=((e,t)=>e.y<t.bottom&&e.bottom>t.y)(r,n.bounds);a!==o.isVisible()&&(o.setVisible(a),s&&!a?(Wa(e.element,[t.fadeOutClass]),t.onHide(e)):(a?SE:kE)(e,t))}))}))},VE=(e,t,o,n,s)=>{NE(e,t,o,n,!0),RE(e,t,o,s.positionCss)},zE=(e,t,o)=>{e.getSystem().isConnected()&&((e,t,o)=>{const n=t.lazyViewport(e);NE(e,t,o,n),FE(e,n,o).each((s=>{((e,t,o,n,s)=>{switch(s.morph){case"static":return IE(e,t,o);case"absolute":return RE(e,t,o,s.positionCss);case"fixed":VE(e,t,o,n,s)}})(e,t,o,n,s)}))})(e,t,o)},HE=(e,t,o)=>{o.isDocked()&&((e,t,o)=>{const n=e.element;o.setDocked(!1);const s=t.lazyViewport(e);((e,t,o)=>{const n=e.element;return AE(n,t,o).bind((({box:e})=>DE(n,e,o)))})(e,s,o).each((n=>{switch(n.morph){case"static":IE(e,t,o);break;case"absolute":RE(e,t,o,n.positionCss)}})),o.setVisible(!0),t.contextual.each((t=>{ja(n,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(e)})),zE(e,t,o)})(e,t,o)},LE=e=>(t,o,n)=>{const s=o.lazyViewport(t);((e,t,o,n)=>{const s=Jo(e),r=en(),a=n(r,EE({win:r,box:s}),t);return"bottom"===a.location||"top"===a.location?(((e,t,o,n,s)=>{n.getInitialPos().fold((()=>ME(e,t,o,n,s)),(()=>b))})(e,s,t,o,a),BE(a)):A.none()})(t.element,s,n,e).each((e=>{VE(t,o,n,s,e)}))},PE=LE(_E),UE=LE(TE);var WE=Object.freeze({__proto__:null,refresh:zE,reset:HE,isDocked:(e,t,o)=>o.isDocked(),getModes:(e,t,o)=>o.getModes(),setModes:(e,t,o,n)=>o.setModes(n),forceDockToTop:PE,forceDockToBottom:UE}),jE=Object.freeze({__proto__:null,events:(e,t)=>Hr([Yr(or(),((o,n)=>{e.contextual.each((e=>{Ua(o.element,e.transitionClass)&&(ja(o.element,[e.transitionClass,e.fadeInClass]),(t.isVisible()?e.onShown:e.onHidden)(o)),n.stop()}))})),Ur(xr(),((o,n)=>{zE(o,e,t)})),Ur(Er(),((o,n)=>{zE(o,e,t)})),Ur(wr(),((o,n)=>{HE(o,e,t)}))])}),GE=[vs("contextual",[rs("fadeInClass"),rs("fadeOutClass"),rs("transitionClass"),is("lazyContext"),Di("onShow"),Di("onShown"),Di("onHide"),Di("onHidden")]),Os("lazyViewport",(()=>({bounds:en(),optScrollEnv:A.none()}))),_s("modes",["top","bottom"],Hn),Di("onDocked"),Di("onUndocked")];const $E=Ol({fields:GE,name:"docking",active:jE,apis:WE,state:Object.freeze({__proto__:null,init:e=>{const t=Es(!1),o=Es(!0),n=Ql(),s=Es(e.modes);return _a({isDocked:t.get,setDocked:t.set,getInitialPos:n.get,setInitialPos:n.set,clearInitialPos:n.clear,isVisible:o.get,setVisible:o.set,getModes:s.get,setModes:s.set,readState:()=>`docked:  ${t.get()}, visible: ${o.get()}, modes: ${s.get().join(",")}`})}})}),qE=x(la("toolbar-height-change")),XE={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},YE="tox-tinymce--toolbar-sticky-on",KE="tox-tinymce--toolbar-sticky-off",JE=(e,t)=>R($E.getModes(e),t),ZE=e=>{const t=e.element;rt(t).each((o=>{const n="padding-"+$E.getModes(e)[0];if($E.isDocked(e)){const e=Jt(o);Dt(t,"width",e+"px"),Dt(o,n,(e=>jt(e)+(parseInt(It(e,"margin-top"),10)||0)+(parseInt(It(e,"margin-bottom"),10)||0))(t)+"px")}else Ht(t,"width"),Ht(o,n)}))},QE=(e,t)=>{t?(Pa(e,XE.fadeOutClass),Wa(e,[XE.transitionClass,XE.fadeInClass])):(Pa(e,XE.fadeInClass),Wa(e,[XE.fadeOutClass,XE.transitionClass]))},eA=(e,t)=>{const o=Ve(e.getContainer());t?(La(o,YE),Pa(o,KE)):(La(o,KE),Pa(o,YE))},tA=(e,t)=>{const o=Ql(),n=t.getSink,s=e=>{n().each((t=>e(t.element)))},r=t=>{e.inline||ZE(t),eA(e,$E.isDocked(t)),t.getSystem().broadcastOn([Kd()],{}),n().each((e=>e.getSystem().broadcastOn([Kd()],{})))},a=e.inline?[]:[Al.config({channels:{[qE()]:{onReceive:ZE}}})];return[oh.config({}),$E.config({contextual:{lazyContext:t=>{const o=jt(t.element),n=e.inline?e.getContentAreaContainer():e.getContainer();return A.from(n).map((n=>{const s=Jo(Ve(n));return jw(e,t.element).fold((()=>{const e=s.height-o,n=s.y+(JE(t,"top")?0:o);return Ko(s.x,n,s.width,e)}),(e=>{const n=Qo(s,Gw(e)),r=JE(t,"top")?n.y:n.y+o;return Ko(n.x,r,n.width,n.height-o)}))}))},onShow:()=>{s((e=>QE(e,!0)))},onShown:e=>{s((e=>ja(e,[XE.transitionClass,XE.fadeInClass]))),o.get().each((t=>{((e,t)=>{const o=et(t);Il(o).filter((e=>!Ze(t,e))).filter((t=>Ze(t,Ve(o.dom.body))||Qe(e,t))).each((()=>Dl(t)))})(e.element,t),o.clear()}))},onHide:e=>{((e,t)=>Rl(e).orThunk((()=>t().toOptional().bind((e=>Rl(e.element))))))(e.element,n).fold(o.clear,o.set),s((e=>QE(e,!1)))},onHidden:()=>{s((e=>ja(e,[XE.transitionClass])))},...XE},lazyViewport:t=>jw(e,t.element).fold((()=>{const o=en(),n=Mf(e),s=o.y+(JE(t,"top")?n:0),r=o.height-(JE(t,"bottom")?n:0);return{bounds:Ko(o.x,s,o.width,r),optScrollEnv:A.none()}}),(e=>({bounds:Gw(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))),modes:[t.header.getDockingMode()],onDocked:r,onUndocked:r}),...a]};var oA=Object.freeze({__proto__:null,setup:(e,t,o)=>{e.inline||(t.header.isPositionedAtTop()||e.on("ResizeEditor",(()=>{o().each($E.reset)})),e.on("ResizeWindow ResizeEditor",(()=>{o().each(ZE)})),e.on("SkinLoaded",(()=>{o().each((e=>{$E.isDocked(e)?$E.reset(e):$E.refresh(e)}))})),e.on("FullscreenStateChanged",(()=>{o().each($E.reset)}))),e.on("AfterScrollIntoView",(e=>{o().each((t=>{$E.refresh(t);const o=t.element;Ig(o)&&((e,t)=>{const o=et(t),n=nt(t).dom.innerHeight,s=Uo(o),r=Ve(e.elm),a=Zo(r),i=Wt(r),l=a.y,c=l+i,d=Xt(t),u=Wt(t),m=d.top,g=m+u,p=Math.abs(m-s.top)<2,h=Math.abs(g-(s.top+n))<2;if(p&&l<g)Wo(s.left,l-u,o);else if(h&&c>m){const e=l-n+i+u;Wo(s.left,e,o)}})(e,o)}))})),e.on("PostRender",(()=>{eA(e,!1)}))},isDocked:e=>e().map($E.isDocked).getOr(!1),getBehaviours:tA});const nA=Dn([tv,ns("items",Fn([Rn([ov,ds("items",Hn)]),Hn]))].concat(Dv)),sA=[ps("text"),ps("tooltip"),ps("icon"),xs("search",!1,Fn([Ln,Dn([ps("placeholder")])],(e=>d(e)?e?A.some({placeholder:A.none()}):A.none():A.some(e)))),is("fetch"),Os("onSetup",(()=>b))],rA=Dn([tv,...sA]),aA=e=>qn("menubutton",rA,e),iA=Dn([tv,fv,hv,pv,yv,lv,mv,ks("presets","normal",["normal","color","listpreview"]),Cv(1),dv,uv]);var lA=fm({factory:(e,t)=>{const o={focus:Pp.focusIn,setMenus:(e,o)=>{const n=H(o,(e=>{const o={type:"menubutton",text:e.text,fetch:t=>{t(e.getItems())}},n=aA(o).mapError((e=>Kn(e))).getOrDie();return t_(n,"tox-mbtn",t.backstage,A.some("menuitem"))}));Kp.set(e,n)}};return{uid:e.uid,dom:e.dom,components:[],behaviours:kl([Kp.config({}),Jp("menubar-events",[Kr((t=>{e.onSetup(t)})),Ur(qs(),((e,t)=>{pi(e.element,".tox-mbtn--active").each((o=>{hi(t.event.target,".tox-mbtn").each((t=>{Ze(o,t)||e.getSystem().getByDom(o).each((o=>{e.getSystem().getByDom(t).each((e=>{Dw.expand(e),Dw.close(o),oh.focus(e)}))}))}))}))})),Ur(_r(),((e,t)=>{t.event.prevFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((o=>{t.event.newFocus.bind((t=>e.getSystem().getByDom(t).toOptional())).each((e=>{Dw.isOpen(o)&&(Dw.expand(e),Dw.close(o))}))}))}))]),Pp.config({mode:"flow",selector:".tox-mbtn",onEscape:t=>(e.onEscape(t),A.some(!0))}),cS.config({})]),apis:o,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[os("dom"),os("uid"),os("onEscape"),os("backstage"),ys("onSetup",b)],apis:{focus:(e,t)=>{e.focus(t)},setMenus:(e,t,o)=>{e.setMenus(t,o)}}});const cA="container",dA=[hu("slotBehaviours",[])],uA=e=>"<alloy.field."+e+">",mA=(e,t)=>{const o=t=>am(e),n=(t,o)=>(n,s)=>om(n,e,s).map((e=>t(e,s))).getOr(o),s=(e,t)=>"true"!==Ot(e.element,"aria-hidden"),r=n(s,!1),a=n(((e,t)=>{if(s(e)){const o=e.element;Dt(o,"display","none"),kt(o,"aria-hidden","true"),Ir(e,Tr(),{name:t,visible:!1})}})),i=(e=>(t,o)=>{L(o,(o=>e(t,o)))})(a),l=n(((e,t)=>{if(!s(e)){const o=e.element;Ht(o,"display"),Et(o,"aria-hidden"),Ir(e,Tr(),{name:t,visible:!0})}})),c={getSlotNames:o,getSlot:(t,o)=>om(t,e,o),isShowing:r,hideSlot:a,hideAllSlots:e=>i(e,o()),showSlot:l};return{uid:e.uid,dom:e.dom,components:t,behaviours:fu(e.slotBehaviours),apis:c}},gA=ce({getSlotNames:(e,t)=>e.getSlotNames(t),getSlot:(e,t,o)=>e.getSlot(t,o),isShowing:(e,t,o)=>e.isShowing(t,o),hideSlot:(e,t,o)=>e.hideSlot(t,o),hideAllSlots:(e,t)=>e.hideAllSlots(t),showSlot:(e,t,o)=>e.showSlot(t,o)},(e=>Ca(e))),pA={...gA,sketch:e=>{const t=(()=>{const e=[];return{slot:(t,o)=>(e.push(t),Ju(cA,uA(t),o)),record:x(e)}})(),o=e(t),n=t.record(),s=H(n,(e=>Uu({name:e,pname:uA(e)})));return mm(cA,dA,s,mA,o)}},hA=Dn([hv,fv,Os("onShow",b),Os("onHide",b),mv]),fA=e=>({element:()=>e.element.dom}),bA=(e,t)=>{const o=H(ae(t),(e=>{const o=t[e],n=Xn((e=>qn("sidebar",hA,e))(o));return{name:e,getApi:fA,onSetup:n.onSetup,onShow:n.onShow,onHide:n.onHide}}));return H(o,(t=>{const n=Es(b);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:ly([Ty(t,n),Ey(t,n),Ur(Tr(),((e,t)=>{const n=t.event,s=G(o,(e=>e.name===n.name));s.each((t=>{(n.visible?t.onShow:t.onHide)(t.getApi(e))}))}))])})}))},vA=e=>pA.sketch((t=>({dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:bA(t,e),slotBehaviours:ly([Kr((e=>pA.hideAllSlots(e)))])}))),yA=(e,t)=>{kt(e,"role",t)},xA=e=>wm.getCurrent(e).bind((e=>QO.isGrowing(e)||QO.hasGrown(e)?wm.getCurrent(e).bind((e=>G(pA.getSlotNames(e),(t=>pA.isShowing(e,t))))):A.none())),wA=la("FixSizeEvent"),SA=la("AutoSizeEvent");var kA=Object.freeze({__proto__:null,block:(e,t,o,n)=>{kt(e.element,"aria-busy",!0);const s=t.getRoot(e).getOr(e),r=kl([Pp.config({mode:"special",onTab:()=>A.some(!0),onShiftTab:()=>A.some(!0)}),oh.config({})]),a=n(s,r),i=s.getSystem().build(a);Kp.append(s,ai(i)),i.hasConfigured(Pp)&&t.focus&&Pp.focusIn(i),o.isBlocked()||t.onBlock(e),o.blockWith((()=>Kp.remove(s,i)))},unblock:(e,t,o)=>{Et(e.element,"aria-busy"),o.isBlocked()&&t.onUnblock(e),o.clear()},isBlocked:(e,t,o)=>o.isBlocked()}),CA=[Os("getRoot",A.none),Cs("focus",!0),Di("onBlock"),Di("onUnblock")];const OA=Ol({fields:CA,name:"blocking",apis:kA,state:Object.freeze({__proto__:null,init:()=>{const e=Jl((e=>e.destroy()));return _a({readState:e.isSet,blockWith:t=>{e.set({destroy:t})},clear:e.clear,isBlocked:e.isSet})}})}),_A=e=>{const t=Ie(e),o=it(t),n=(e=>{const t=void 0!==e.dom.attributes?e.dom.attributes:[];return j(t,((e,t)=>"class"===t.name?e:{...e,[t.name]:t.value}),{})})(t),s=(e=>Array.prototype.slice.call(e.dom.classList,0))(t),r=0===o.length?{}:{innerHtml:ea(t)};return{tag:Ue(t),classes:s,attributes:n,...r}},TA=e=>wm.getCurrent(e).each((e=>Dl(e.element))),EA=(e,t,o)=>{const n=Es(!1),s=Ql(),r=o=>{var s;n.get()&&(!(e=>"focusin"===e.type)(s=o)||!(s.composed?oe(s.composedPath()):A.from(s.target)).map(Ve).filter(Ge).exists((e=>Ua(e,"mce-pastebin"))))&&(o.preventDefault(),TA(t()),e.editorManager.setActive(e))};e.inline||e.on("PreInit",(()=>{e.dom.bind(e.getWin(),"focusin",r),e.on("BeforeExecCommand",(e=>{"mcefocus"===e.command.toLowerCase()&&!0!==e.value&&r(e)}))}));const a=s=>{s!==n.get()&&(n.set(s),((e,t,o,n)=>{const s=t.element;if(((e,t)=>{const o="tabindex",n=`data-mce-${o}`;A.from(e.iframeElement).map(Ve).each((e=>{t?(_t(e,o).each((t=>kt(e,n,t))),kt(e,o,-1)):(Et(e,o),_t(e,n).each((t=>{kt(e,o,t),Et(e,n)})))}))})(e,o),o)OA.block(t,(e=>(t,o)=>({dom:{tag:"div",attributes:{"aria-label":e.translate("Loading..."),tabindex:"0"},classes:["tox-throbber__busy-spinner"]},components:[{dom:_A('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}))(n)),Ht(s,"display"),Et(s,"aria-hidden"),e.hasFocus()&&TA(t);else{const o=wm.getCurrent(t).exists((e=>Fl(e.element)));OA.unblock(t),Dt(s,"display","none"),kt(s,"aria-hidden","true"),o&&e.focus()}})(e,t(),s,o.providers),((e,t)=>{e.dispatch("AfterProgressState",{state:t})})(e,s))};e.on("ProgressState",(t=>{if(s.on(clearTimeout),h(t.time)){const o=Uh.setEditorTimeout(e,(()=>a(t.state)),t.time);s.set(o)}else a(t.state),s.clear()}))},AA=(e,t,o)=>({within:e,extra:t,withinWidth:o}),MA=(e,t,o)=>{const n=j(e,((e,t)=>((e,t)=>{const n=o(e);return A.some({element:e,start:t,finish:t+n,width:n})})(t,e.len).fold(x(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:0,list:[]}).list,s=U(n,(e=>e.finish<=t)),r=W(s,((e,t)=>e+t.width),0);return{within:s,extra:n.slice(s.length),withinWidth:r}},DA=e=>H(e,(e=>e.element)),BA=(e,t)=>{const o=H(t,(e=>ai(e)));pE.setGroups(e,o)},FA=(e,t,o)=>{const n=t.builtGroups.get();if(0===n.length)return;const s=nm(e,t,"primary"),r=uw.getCoupled(e,"overflowGroup");Dt(s.element,"visibility","hidden");const a=n.concat([r]),i=re(a,(e=>Rl(e.element).bind((t=>e.getSystem().getByDom(t).toOptional()))));o([]),BA(s,a);const l=((e,t,o,n)=>{const s=((e,t,o)=>{const n=MA(t,e,o);return 0===n.extra.length?A.some(n):A.none()})(e,t,o).getOrThunk((()=>MA(t,e-o(n),o))),r=s.within,a=s.extra,i=s.withinWidth;return 1===a.length&&a[0].width<=o(n)?((e,t,o)=>{const n=DA(e.concat(t));return AA(n,[],o)})(r,a,i):a.length>=1?((e,t,o,n)=>{const s=DA(e).concat([o]);return AA(s,DA(t),n)})(r,a,n,i):((e,t,o)=>AA(DA(e),[],o))(r,0,i)})(Jt(s.element),t.builtGroups.get(),(e=>Jt(e.element)),r);0===l.extra.length?(Kp.remove(s,r),o([])):(BA(s,l.within),o(l.extra)),Ht(s.element,"visibility"),Lt(s.element),i.each(oh.focus)},IA=x([hu("splitToolbarBehaviours",[uw]),es("builtGroups",(()=>Es([])))]),RA=x([Ai(["overflowToggledClass"]),fs("getOverflowBounds"),os("lazySink"),es("overflowGroups",(()=>Es([]))),Di("onOpened"),Di("onClosed")].concat(IA())),NA=x([Uu({factory:pE,schema:mE(),name:"primary"}),Wu({schema:mE(),name:"overflow"}),Wu({name:"overflow-button"}),Wu({name:"overflow-group"})]),VA=x(((e,t)=>{((e,t)=>{const o=Kt.max(e,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);Dt(e,"max-width",o+"px")})(e,Math.floor(t))})),zA=x([Ai(["toggledClass"]),os("lazySink"),is("fetch"),fs("getBounds"),vs("fireDismissalEventInstead",[ys("event",Cr())]),wc(),Di("onToggled")]),HA=x([Wu({name:"button",overrides:e=>({dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:kl([dh.config({toggleClass:e.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1,onToggled:e.onToggled})])})}),Wu({factory:pE,schema:mE(),name:"toolbar",overrides:e=>({toolbarBehaviours:kl([Pp.config({mode:"cyclic",onEscape:t=>(om(t,e,"button").each(oh.focus),A.none())})])})})]),LA=Ql(),PA=(e,t)=>{const o=uw.getCoupled(e,"toolbarSandbox");Xd.isOpen(o)?Xd.close(o):Xd.open(o,t.toolbar())},UA=(e,t,o,n)=>{const s=o.getBounds.map((e=>e())),r=o.lazySink(e).getOrDie();Sd.positionWithinBounds(r,t,{anchor:{type:"hotspot",hotspot:e,layouts:n,overrides:{maxWidthFunction:VA()}}},s)},WA=(e,t,o,n,s)=>{pE.setGroups(t,s),UA(e,t,o,n),dh.on(e)},jA=bm({name:"FloatingToolbarButton",factory:(e,t,o,n)=>({...Wh.sketch({...n.button(),action:e=>{PA(e,n)},buttonBehaviours:yu({dump:n.button().buttonBehaviours},[uw.config({others:{toolbarSandbox:t=>((e,t,o)=>{const n=bi();return{dom:{tag:"div",attributes:{id:n.id}},behaviours:kl([Pp.config({mode:"special",onEscape:e=>(Xd.close(e),A.some(!0))}),Xd.config({onOpen:(s,r)=>{const a=LA.get().getOr(!1);o.fetch().get((s=>{WA(e,r,o,t.layouts,s),n.link(e.element),a||Pp.focusIn(r)}))},onClose:()=>{dh.off(e),LA.get().getOr(!1)||oh.focus(e),n.unlink(e.element)},isPartOf:(t,o,n)=>vi(o,n)||vi(e,n),getAttachPoint:()=>o.lazySink(e).getOrDie()}),Al.config({channels:{...Qd({isExtraPart:T,...o.fireDismissalEventInstead.map((e=>({fireEventInstead:{event:e.event}}))).getOr({})}),...tu({doReposition:()=>{Xd.getState(uw.getCoupled(e,"toolbarSandbox")).each((n=>{UA(e,n,o,t.layouts)}))}})}})])}})(t,o,e)}})])}),apis:{setGroups:(t,n)=>{Xd.getState(uw.getCoupled(t,"toolbarSandbox")).each((s=>{WA(t,s,e,o.layouts,n)}))},reposition:t=>{Xd.getState(uw.getCoupled(t,"toolbarSandbox")).each((n=>{UA(t,n,e,o.layouts)}))},toggle:e=>{PA(e,n)},toggleWithoutFocusing:e=>{((e,t)=>{LA.set(!0),PA(e,t),LA.clear()})(e,n)},getToolbar:e=>Xd.getState(uw.getCoupled(e,"toolbarSandbox")),isOpen:e=>Xd.isOpen(uw.getCoupled(e,"toolbarSandbox"))}}),configFields:zA(),partFields:HA(),apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggleWithoutFocusing(t)},getToolbar:(e,t)=>e.getToolbar(t),isOpen:(e,t)=>e.isOpen(t)}}),GA=x([os("items"),Ai(["itemSelector"]),hu("tgroupBehaviours",[Pp])]),$A=x([Gu({name:"items",unit:"item"})]),qA=bm({name:"ToolbarGroup",configFields:GA(),partFields:$A(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.tgroupBehaviours,[Pp.config({mode:"flow",selector:e.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}})}),XA=e=>H(e,(e=>ai(e))),YA=(e,t,o)=>{FA(e,o,(n=>{o.overflowGroups.set(n),t.getOpt(e).each((e=>{jA.setGroups(e,XA(n))}))}))},KA=bm({name:"SplitFloatingToolbar",configFields:RA(),partFields:NA(),factory:(e,t,o,n)=>{const s=jh(jA.sketch({fetch:()=>fw((t=>{t(XA(e.overflowGroups.get()))})),layouts:{onLtr:()=>[rl,sl],onRtl:()=>[sl,rl],onBottomLtr:()=>[il,al],onBottomRtl:()=>[al,il]},getBounds:o.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:n["overflow-button"](),toolbar:n.overflow()},onToggled:(t,o)=>e[o?"onOpened":"onClosed"](t)}));return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.splitToolbarBehaviours,[uw.config({others:{overflowGroup:()=>qA.sketch({...n["overflow-group"](),items:[s.asSpec()]})}})]),apis:{setGroups:(t,o)=>{e.builtGroups.set(H(o,t.getSystem().build)),YA(t,s,e)},refresh:t=>YA(t,s,e),toggle:e=>{s.getOpt(e).each((e=>{jA.toggle(e)}))},toggleWithoutFocusing:e=>{s.getOpt(e).each(jA.toggleWithoutFocusing)},isOpen:e=>s.getOpt(e).map(jA.isOpen).getOr(!1),reposition:e=>{s.getOpt(e).each((e=>{jA.reposition(e)}))},getOverflow:e=>s.getOpt(e).bind(jA.getToolbar)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},reposition:(e,t)=>{e.reposition(t)},toggle:(e,t)=>{e.toggle(t)},toggleWithoutFocusing:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t),getOverflow:(e,t)=>e.getOverflow(t)}}),JA=x([Ai(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),Di("onOpened"),Di("onClosed")].concat(IA())),ZA=x([Uu({factory:pE,schema:mE(),name:"primary"}),Uu({factory:pE,schema:mE(),name:"overflow",overrides:e=>({toolbarBehaviours:kl([QO.config({dimension:{property:"height"},closedClass:e.markers.closedClass,openClass:e.markers.openClass,shrinkingClass:e.markers.shrinkingClass,growingClass:e.markers.growingClass,onShrunk:t=>{om(t,e,"overflow-button").each((e=>{dh.off(e),oh.focus(e)})),e.onClosed(t)},onGrown:t=>{Pp.focusIn(t),e.onOpened(t)},onStartGrow:t=>{om(t,e,"overflow-button").each(dh.on)}}),Pp.config({mode:"acyclic",onEscape:t=>(om(t,e,"overflow-button").each(oh.focus),A.some(!0))})])})}),Wu({name:"overflow-button",overrides:e=>({buttonBehaviours:kl([dh.config({toggleClass:e.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])})}),Wu({name:"overflow-group"})]),QA=(e,t)=>{om(e,t,"overflow-button").bind((()=>om(e,t,"overflow"))).each((o=>{eM(e,t),QO.toggleGrow(o)}))},eM=(e,t)=>{om(e,t,"overflow").each((o=>{FA(e,t,(e=>{const t=H(e,(e=>ai(e)));pE.setGroups(o,t)})),om(e,t,"overflow-button").each((e=>{QO.hasGrown(o)&&dh.on(e)})),QO.refresh(o)}))},tM=bm({name:"SplitSlidingToolbar",configFields:JA(),partFields:ZA(),factory:(e,t,o,n)=>{const s="alloy.toolbar.toggle";return{uid:e.uid,dom:e.dom,components:t,behaviours:bu(e.splitToolbarBehaviours,[uw.config({others:{overflowGroup:e=>qA.sketch({...n["overflow-group"](),items:[Wh.sketch({...n["overflow-button"](),action:t=>{Fr(e,s)}})]})}}),Jp("toolbar-toggle-events",[Ur(s,(t=>{QA(t,e)}))])]),apis:{setGroups:(t,o)=>{((t,o)=>{const n=H(o,t.getSystem().build);e.builtGroups.set(n)})(t,o),eM(t,e)},refresh:t=>eM(t,e),toggle:t=>QA(t,e),isOpen:t=>((e,t)=>om(e,t,"overflow").map(QO.hasGrown).getOr(!1))(t,e)},domModification:{attributes:{role:"group"}}}},apis:{setGroups:(e,t,o)=>{e.setGroups(t,o)},refresh:(e,t)=>{e.refresh(t)},toggle:(e,t)=>{e.toggle(t)},isOpen:(e,t)=>e.isOpen(t)}}),oM=e=>{const t=e.title.fold((()=>({})),(e=>({attributes:{title:e}})));return{dom:{tag:"div",classes:["tox-toolbar__group"],...t},components:[qA.parts.items({})],items:e.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled]), .tox-number-input:not([disabled])"},tgroupBehaviours:kl([cS.config({}),oh.config({})])}},nM=e=>qA.sketch(oM(e)),sM=(e,t)=>{const o=Kr((t=>{const o=H(e.initGroups,nM);pE.setGroups(t,o)}));return kl([Oy(e.providers.isDisabled),Sy(),Pp.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Jp("toolbar-events",[o])])},rM=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return{uid:e.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":oM({title:A.none(),items:[]}),"overflow-button":T_({name:"more",icon:A.some("more-drawer"),enabled:!0,tooltip:A.some("Reveal or hide additional toolbar items"),primary:!1,buttonType:A.none(),borderless:!1},A.none(),e.providers)},splitToolbarBehaviours:sM(e,t)}},aM=e=>{const t=rM(e),o=KA.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return KA.sketch({...t,lazySink:e.getSink,getOverflowBounds:()=>{const t=e.moreDrawerData.lazyHeader().element,o=Zo(t),n=ot(t),s=Zo(n),r=Math.max(n.dom.scrollHeight,s.height);return Ko(o.x+4,s.y,o.width-8,r)},parts:{...t.parts,overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:e.attributes}}},components:[o],markers:{overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>e.onToggled(t,!0),onClosed:t=>e.onToggled(t,!1)})},iM=e=>{const t=tM.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),o=tM.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),n=rM(e);return tM.sketch({...n,components:[t,o],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:t=>{t.getSystem().broadcastOn([qE()],{type:"opened"}),e.onToggled(t,!0)},onClosed:t=>{t.getSystem().broadcastOn([qE()],{type:"closed"}),e.onToggled(t,!1)}})},lM=e=>{const t=e.cyclicKeying?"cyclic":"acyclic";return pE.sketch({uid:e.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(e.type===nf.scrolling?["tox-toolbar--scrolling"]:[])},components:[pE.parts.groups({})],toolbarBehaviours:sM(e,t)})},cM=[pv,hv,ps("tooltip"),ks("buttonType","secondary",["primary","secondary"]),Cs("borderless",!1),is("onAction")],dM={button:[...cM,sv,as("type",["button"])],togglebutton:[...cM,Cs("active",!1),as("type",["togglebutton"])]},uM=[as("type",["group"]),_s("buttons",[],Jn("type",dM))],mM=Jn("type",{...dM,group:uM}),gM=Dn([_s("buttons",[],mM),is("onShow"),is("onHide")]),pM=(e,t)=>((e,t)=>{var o,n;const s="togglebutton"===e.type,r=e.icon.map((e=>fO(e,t.icons))).map(jh),a={...e,name:s?e.text.getOr(e.icon.getOr("")):null!==(o=e.text)&&void 0!==o?o:e.icon.getOr(""),primary:"primary"===e.buttonType,buttonType:A.from(e.buttonType),tooltip:e.tooltip,icon:e.icon,enabled:!0,borderless:e.borderless},i=E_(null!==(n=e.buttonType)&&void 0!==n?n:"secondary"),l=s?e.text.map(t.translate):A.some(t.translate(e.text)),c=l.map(ti),d=a.tooltip.or(l).map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),u=r.map((e=>e.asSpec())),m=Dy([u,c]),g=e.icon.isSome()&&c.isSome(),p={tag:"button",classes:i.concat(...e.icon.isSome()&&!g?["tox-button--icon"]:[]).concat(...g?["tox-button--icon-and-text"]:[]).concat(...e.borderless?["tox-button--naked"]:[]).concat(..."togglebutton"===e.type&&e.active?["tox-button--enabled"]:[]),attributes:d},h=__(a,A.some((o=>{const n=e=>{r.map((n=>n.getOpt(o).each((o=>{Kp.set(o,[fO(e,t.icons)])}))))};return s?e.onAction({setIcon:n,setActive:e=>{const t=o.element;e?(La(t,"tox-button--enabled"),kt(t,"aria-pressed",!0)):(Pa(t,"tox-button--enabled"),Et(t,"aria-pressed"))},isActive:()=>Ua(o.element,"tox-button--enabled")}):"button"===e.type?e.onAction({setIcon:n}):void 0})),[],p,m,t);return Wh.sketch(h)})(e,t),hM=Do().deviceType,fM=hM.isPhone(),bM=hM.isTablet();var vM=bm({name:"silver.View",configFields:[os("viewConfig")],partFields:[ju({factory:{sketch:e=>{let t=!1;const o=H(e.buttons,(o=>"group"===o.type?(t=!0,((e,t)=>({dom:{tag:"div",classes:["tox-view__toolbar__group"]},components:H(e.buttons,(e=>pM(e,t)))}))(o,e.providers)):pM(o,e.providers)));return{uid:e.uid,dom:{tag:"div",classes:[t?"tox-view__toolbar":"tox-view__header",...fM||bM?["tox-view--mobile","tox-view--scrolling"]:[]]},behaviours:kl([oh.config({}),Pp.config({mode:"flow",selector:"button, .tox-button",focusInside:pg.OnEnterOrSpaceMode})]),components:t?o:[oS.sketch({dom:{tag:"div",classes:["tox-view__header-start"]},components:[]}),oS.sketch({dom:{tag:"div",classes:["tox-view__header-end"]},components:o})]}}},schema:[os("buttons"),os("providers")],name:"header"}),ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-view__pane"]}})},schema:[],name:"pane"})],factory:(e,t,o,n)=>{const s={getPane:t=>aE.getPart(t,e,"pane"),getOnShow:t=>e.viewConfig.onShow,getOnHide:t=>e.viewConfig.onHide};return{uid:e.uid,dom:e.dom,components:t,apis:s}},apis:{getPane:(e,t)=>e.getPane(t),getOnShow:(e,t)=>e.getOnShow(t),getOnHide:(e,t)=>e.getOnHide(t)}});const yM=(e,t,o)=>pe(t,((t,n)=>{const s=Xn(qn("view",gM,t));return e.slot(n,vM.sketch({dom:{tag:"div",classes:["tox-view"]},viewConfig:s,components:[...s.buttons.length>0?[vM.parts.header({buttons:s.buttons,providers:o})]:[],vM.parts.pane({})]}))})),xM=(e,t)=>pA.sketch((o=>({dom:{tag:"div",classes:["tox-view-wrap__slot-container"]},components:yM(o,e,t),slotBehaviours:ly([Kr((e=>pA.hideAllSlots(e)))])}))),wM=e=>G(pA.getSlotNames(e),(t=>pA.isShowing(e,t))),SM=(e,t,o)=>{pA.getSlot(e,t).each((e=>{vM.getPane(e).each((t=>{var n;o(e)((n=t.element.dom,{getContainer:x(n)}))}))}))};var kM=fm({factory:(e,t)=>{const o={setViews:(e,o)=>{Kp.set(e,[xM(o,t.backstage.shared.providers)])},whichView:e=>wm.getCurrent(e).bind(wM),toggleView:(e,t,o,n)=>wm.getCurrent(e).exists((s=>{const r=wM(s),a=r.exists((e=>n===e)),i=pA.getSlot(s,n).isSome();return i&&(pA.hideAllSlots(s),a?((e=>{const t=e.element;Dt(t,"display","none"),kt(t,"aria-hidden","true")})(e),t()):(o(),(e=>{const t=e.element;Ht(t,"display"),Et(t,"aria-hidden")})(e),pA.showSlot(s,n),((e,t)=>{SM(e,t,vM.getOnShow)})(s,n)),r.each((e=>((e,t)=>SM(e,t,vM.getOnHide))(s,e)))),i}))};return{uid:e.uid,dom:{tag:"div",classes:["tox-view-wrap"],attributes:{"aria-hidden":"true"},styles:{display:"none"}},components:[],behaviours:kl([Kp.config({}),wm.config({find:e=>{const t=Kp.contents(e);return oe(t)}})]),apis:o}},name:"silver.ViewWrapper",configFields:[os("backstage")],apis:{setViews:(e,t,o)=>e.setViews(t,o),toggleView:(e,t,o,n,s)=>e.toggleView(t,o,n,s),whichView:(e,t)=>e.whichView(t)}});const CM=iE.optional({factory:lA,name:"menubar",schema:[os("backstage")]}),OM=iE.optional({factory:{sketch:e=>uE.sketch({uid:e.uid,dom:e.dom,listBehaviours:kl([Pp.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:()=>lM({type:e.type,uid:la("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:e.providers,onEscape:()=>(e.onEscape(),A.some(!0))}),setupItem:(e,t,o,n)=>{pE.setGroups(t,o)},shell:!0})},name:"multiple-toolbar",schema:[os("dom"),os("onEscape")]}),_M=iE.optional({factory:{sketch:e=>{const t=(e=>e.type===nf.sliding?iM:e.type===nf.floating?aM:lM)(e);return t({type:e.type,uid:e.uid,onEscape:()=>(e.onEscape(),A.some(!0)),onToggled:(t,o)=>e.onToolbarToggled(o),cyclicKeying:!1,initGroups:[],getSink:e.getSink,providers:e.providers,moreDrawerData:{lazyToolbar:e.lazyToolbar,lazyMoreButton:e.lazyMoreButton,lazyHeader:e.lazyHeader},attributes:e.attributes})}},name:"toolbar",schema:[os("dom"),os("onEscape"),os("getSink")]}),TM=iE.optional({factory:{sketch:e=>{const t=e.editor,o=e.sticky?tA:bE;return{uid:e.uid,dom:e.dom,components:e.components,behaviours:kl(o(t,e.sharedBackstage))}}},name:"header",schema:[os("dom")]}),EM=iE.optional({factory:{sketch:e=>({uid:e.uid,dom:e.dom,components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/tinymce-self-hosted-premium-features/?utm_source=TinyMCE&utm_medium=SPAP&utm_campaign=SPAP&utm_id=editorreferral",rel:"noopener",target:"_blank","aria-hidden":"true"},classes:["tox-promotion-link"],innerHtml:"\u26a1\ufe0fUpgrade"}}]})},name:"promotion",schema:[os("dom")]}),AM=iE.optional({name:"socket",schema:[os("dom")]}),MM=iE.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"presentation"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:kl([cS.config({}),oh.config({}),QO.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:e=>{wm.getCurrent(e).each(pA.hideAllSlots),Fr(e,SA)},onGrown:e=>{Fr(e,SA)},onStartGrow:e=>{Ir(e,wA,{width:Nt(e.element,"width").getOr("")})},onStartShrink:e=>{Ir(e,wA,{width:Jt(e.element)+"px"})}}),Kp.config({}),wm.config({find:e=>{const t=Kp.contents(e);return oe(t)}})])}],behaviours:kl([BC(0),Jp("sidebar-sliding-events",[Ur(wA,((e,t)=>{Dt(e.element,"width",t.event.width)})),Ur(SA,((e,t)=>{Ht(e.element,"width")}))])])})},name:"sidebar",schema:[os("dom")]}),DM=iE.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:kl([Kp.config({}),OA.config({focus:!1}),wm.config({find:e=>oe(e.components())})]),components:[]})},name:"throbber",schema:[os("dom")]}),BM=iE.optional({factory:kM,name:"viewWrapper",schema:[os("backstage")]}),FM=iE.optional({factory:{sketch:e=>({uid:e.uid,dom:{tag:"div",classes:["tox-editor-container"]},components:e.components})},name:"editorContainer",schema:[]});var IM=bm({name:"OuterContainer",factory:(e,t,o)=>{let n=!1;const s={getSocket:t=>aE.getPart(t,e,"socket"),setSidebar:(t,o,n)=>{aE.getPart(t,e,"sidebar").each((e=>((e,t,o)=>{wm.getCurrent(e).each((n=>{Kp.set(n,[vA(t)]);const s=null==o?void 0:o.toLowerCase();r(s)&&ve(t,s)&&wm.getCurrent(n).each((t=>{pA.showSlot(t,s),QO.immediateGrow(n),Ht(n.element,"width"),yA(e.element,"region")}))}))})(e,o,n)))},toggleSidebar:(t,o)=>{aE.getPart(t,e,"sidebar").each((e=>((e,t)=>{wm.getCurrent(e).each((o=>{wm.getCurrent(o).each((n=>{QO.hasGrown(o)?pA.isShowing(n,t)?(QO.shrink(o),yA(e.element,"presentation")):(pA.hideAllSlots(n),pA.showSlot(n,t),yA(e.element,"region")):(pA.hideAllSlots(n),pA.showSlot(n,t),QO.grow(o),yA(e.element,"region"))}))}))})(e,o)))},whichSidebar:t=>aE.getPart(t,e,"sidebar").bind(xA).getOrNull(),getHeader:t=>aE.getPart(t,e,"header"),getToolbar:t=>aE.getPart(t,e,"toolbar"),setToolbar:(t,o)=>{aE.getPart(t,e,"toolbar").each((e=>{const t=H(o,nM);e.getApis().setGroups(e,t)}))},setToolbars:(t,o)=>{aE.getPart(t,e,"multiple-toolbar").each((e=>{const t=H(o,(e=>H(e,nM)));uE.setItems(e,t)}))},refreshToolbar:t=>{aE.getPart(t,e,"toolbar").each((e=>e.getApis().refresh(e)))},toggleToolbarDrawer:t=>{aE.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggle,(t=>t(e)))}))},toggleToolbarDrawerWithoutFocusing:t=>{aE.getPart(t,e,"toolbar").each((e=>{ke(e.getApis().toggleWithoutFocusing,(t=>t(e)))}))},isToolbarDrawerToggled:t=>aE.getPart(t,e,"toolbar").bind((e=>A.from(e.getApis().isOpen).map((t=>t(e))))).getOr(!1),getThrobber:t=>aE.getPart(t,e,"throbber"),focusToolbar:t=>{aE.getPart(t,e,"toolbar").orThunk((()=>aE.getPart(t,e,"multiple-toolbar"))).each((e=>{Pp.focusIn(e)}))},setMenubar:(t,o)=>{aE.getPart(t,e,"menubar").each((e=>{lA.setMenus(e,o)}))},focusMenubar:t=>{aE.getPart(t,e,"menubar").each((e=>{lA.focus(e)}))},setViews:(t,o)=>{aE.getPart(t,e,"viewWrapper").each((e=>{kM.setViews(e,o)}))},toggleView:(t,o)=>aE.getPart(t,e,"viewWrapper").exists((e=>kM.toggleView(e,(()=>s.showMainView(t)),(()=>s.hideMainView(t)),o))),whichView:t=>aE.getPart(t,e,"viewWrapper").bind(kM.whichView).getOrNull(),hideMainView:t=>{n=s.isToolbarDrawerToggled(t),n&&s.toggleToolbarDrawer(t),aE.getPart(t,e,"editorContainer").each((e=>{const t=e.element;Dt(t,"display","none"),kt(t,"aria-hidden","true")}))},showMainView:t=>{n&&s.toggleToolbarDrawer(t),aE.getPart(t,e,"editorContainer").each((e=>{const t=e.element;Ht(t,"display"),Et(t,"aria-hidden")}))}};return{uid:e.uid,dom:e.dom,components:t,apis:s,behaviours:e.behaviours}},configFields:[os("dom"),os("behaviours")],partFields:[TM,CM,_M,OM,AM,MM,EM,DM,BM,FM],apis:{getSocket:(e,t)=>e.getSocket(t),setSidebar:(e,t,o,n)=>{e.setSidebar(t,o,n)},toggleSidebar:(e,t,o)=>{e.toggleSidebar(t,o)},whichSidebar:(e,t)=>e.whichSidebar(t),getHeader:(e,t)=>e.getHeader(t),getToolbar:(e,t)=>e.getToolbar(t),setToolbar:(e,t,o)=>{e.setToolbar(t,o)},setToolbars:(e,t,o)=>{e.setToolbars(t,o)},refreshToolbar:(e,t)=>e.refreshToolbar(t),toggleToolbarDrawer:(e,t)=>{e.toggleToolbarDrawer(t)},toggleToolbarDrawerWithoutFocusing:(e,t)=>{e.toggleToolbarDrawerWithoutFocusing(t)},isToolbarDrawerToggled:(e,t)=>e.isToolbarDrawerToggled(t),getThrobber:(e,t)=>e.getThrobber(t),setMenubar:(e,t,o)=>{e.setMenubar(t,o)},focusMenubar:(e,t)=>{e.focusMenubar(t)},focusToolbar:(e,t)=>{e.focusToolbar(t)},setViews:(e,t,o)=>{e.setViews(t,o)},toggleView:(e,t,o)=>e.toggleView(t,o),whichView:(e,t)=>e.whichView(t)}});const RM={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template inserttemplate codesample inserttable accordion | charmap emoticons hr | pagebreak nonbreaking anchor tableofcontents footnotes | mergetags | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | styles blocks fontfamily fontsize align lineheight | forecolor backcolor | language | removeformat"},tools:{title:"Tools",items:"aidialog aishortcuts | spellchecker spellcheckerlanguage | autocorrect capitalization | a11ycheck code typography wordcount addtemplate"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},NM=e=>e.split(" "),VM=(e,t)=>{const o={...RM,...t.menus},n=ae(t.menus).length>0,s=void 0===t.menubar||!0===t.menubar?NM("file edit view insert format tools table help"):NM(!1===t.menubar?"":t.menubar),a=U(s,(e=>{const o=ve(RM,e);return n?o||be(t.menus,e).exists((e=>ve(e,"items"))):o})),i=H(a,(n=>{const s=o[n];return((e,t,o)=>{const n=kf(o).split(/[ ,]/);return{text:e.title,getItems:()=>X(e.items,(e=>{const o=e.toLowerCase();return 0===o.trim().length||N(n,(e=>e===o))?[]:"separator"===o||"|"===o?[{type:"separator"}]:t.menuItems[o]?[t.menuItems[o]]:[]}))}})({title:s.title,items:NM(s.items)},t,e)}));return U(i,(e=>e.getItems().length>0&&N(e.getItems(),(e=>r(e)||"separator"!==e.type))))},zM=e=>{const t=()=>{e._skinLoaded=!0,(e=>{e.dispatch("SkinLoaded")})(e)};return()=>{e.initialized?t():e.on("init",t)}},HM=(e,t,o)=>(e.on("remove",(()=>o.unload(t))),o.load(t)),LM=(e,t)=>HM(e,t+"/skin.min.css",e.ui.styleSheetLoader),PM=(e,t)=>{var o;return o=Ve(e.getElement()),bt(o).isSome()?HM(e,t+"/skin.shadowdom.min.css",rf.DOM.styleSheetLoader):Promise.resolve()},UM=(e,t)=>{const o=Jf(t);return o&&t.contentCSS.push(o+(e?"/content.inline":"/content")+".min.css"),!Yf(t)&&r(o)?Promise.all([LM(t,o),PM(t,o)]).then(zM(t),((e,t)=>()=>((e,t)=>{e.dispatch("SkinLoadError",t)})(e,{message:"Skin could not be loaded"}))(t)):Promise.resolve(zM(t)())},WM=k(UM,!1),jM=k(UM,!0),GM=(e,t,o)=>{const n=(e,n,r,a)=>{const i=t.shared.providers.translate(e.title);if("separator"===e.type)return A.some({type:"separator",text:i});if("submenu"===e.type){const t=X(e.getStyleItems(),(e=>s(e,n,a)));return 0===n&&t.length<=0?A.none():A.some({type:"nestedmenuitem",text:i,enabled:t.length>0,getSubmenuItems:()=>X(e.getStyleItems(),(e=>s(e,n,a)))})}return A.some({type:"togglemenuitem",text:i,icon:e.icon,active:e.isSelected(a),enabled:!r,onAction:o.onAction(e),...e.getStylePreview().fold((()=>({})),(e=>({meta:{style:e}})))})},s=(e,t,s)=>{const r="formatter"===e.type&&o.isInvalid(e);return 0===t?r?[]:n(e,t,!1,s).toArray():n(e,t,r,s).toArray()},r=e=>{const t=o.getCurrentValue(),n=o.shouldHide?0:1;return X(e,(e=>s(e,n,t)))};return{validateItems:r,getFetch:(e,t)=>(o,n)=>{const s=t(),a=r(s);n(CO(a,pb.CLOSE_ON_EXECUTE,e,{isHorizontalMenu:!1,search:A.none()}))}}},$M=(e,t,o)=>{const n=o.dataset,s="basic"===n.type?()=>H(n.data,(e=>FT(e,o.isSelectedFor,o.getPreviewFor))):n.getData;return{items:GM(0,t,o),getStyleItems:s}},qM=(e,t,o)=>{const{items:n,getStyleItems:s}=$M(0,t,o),r=yx(e,"NodeChange",(t=>{const n=t.getComponent();o.updateText(n),Rm.set(t.getComponent(),!e.selection.isEditable())}));return xO({text:o.icon.isSome()?A.none():o.text,icon:o.icon,tooltip:A.from(o.tooltip),role:A.none(),fetch:n.getFetch(t,s),onSetup:r,getApi:e=>({getComponent:x(e)}),columns:1,presets:"normal",classes:o.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};var XM;!function(e){e[e.SemiColon=0]="SemiColon",e[e.Space=1]="Space"}(XM||(XM={}));const YM=(e,t,o)=>{const n=(s=((e,t)=>t===XM.SemiColon?e.replace(/;$/,"").split(";"):e.split(" "))(e.options.get(t),o),H(s,(e=>{let t=e,o=e;const n=e.split("=");return n.length>1&&(t=n[0],o=n[1]),{title:t,format:o}})));var s;return{type:"basic",data:n}},KM=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],JM=e=>{const t={type:"basic",data:KM};return{tooltip:"Align",text:A.none(),icon:A.some("align-left"),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:e=>A.none,onAction:t=>()=>G(KM,(e=>e.format===t.format)).each((t=>e.execCommand(t.command))),updateText:t=>{const o=G(KM,(t=>e.formatter.match(t.format))).fold(x("left"),(e=>e.title.toLowerCase()));Ir(t,yO,{icon:`align-${o}`})},dataset:t,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},ZM=(e,t)=>{const o=t(),n=H(o,(e=>e.format));return A.from(e.formatter.closest(n)).bind((e=>G(o,(t=>t.format===e)))).orThunk((()=>Ce(e.formatter.match("p"),{title:"Paragraph",format:"p"})))},QM=e=>{const t="Paragraph",o=YM(e,"block_formats",XM.SemiColon);return{tooltip:"Blocks",text:A.some(t),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:xx(e),updateText:n=>{const s=ZM(e,(()=>o.data)).fold(x(t),(e=>e.title));Ir(n,vO,{text:s})},dataset:o,shouldHide:!1,isInvalid:t=>!e.formatter.canApply(t.format)}},eD=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],tD=e=>{const t=e.split(/\s*,\s*/);return H(t,(e=>e.replace(/^['"]+|['"]+$/g,"")))},oD=e=>{const t="System Font",o=()=>{const o=e=>e?tD(e)[0]:"",s=e.queryCommandValue("FontName"),r=n.data,a=s?s.toLowerCase():"",i=G(r,(e=>{const t=e.format;return t.toLowerCase()===a||o(t).toLowerCase()===o(a).toLowerCase()})).orThunk((()=>Ce((e=>0===e.indexOf("-apple-system")&&(()=>{const t=tD(e.toLowerCase());return Y(eD,(e=>t.indexOf(e.toLowerCase())>-1))})())(a),{title:t,format:a})));return{matchOpt:i,font:s}},n=YM(e,"font_family_formats",XM.SemiColon);return{tooltip:"Fonts",text:A.some(t),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getCurrentValue:()=>{const{matchOpt:e}=o();return e},getPreviewFor:e=>()=>A.some({tag:"div",styles:-1===e.indexOf("dings")?{"font-family":e}:{}}),onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontName",!1,t.format)}))},updateText:e=>{const{matchOpt:t,font:n}=o(),s=t.fold(x(n),(e=>e.title));Ir(e,vO,{text:s})},dataset:n,shouldHide:!1,isInvalid:T}},nD={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},sD=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),rD=(e,t)=>A.from(sD.exec(e)).bind((e=>{const o=Number(e[1]),n=e[2];return((e,t)=>N(t,(t=>N(nD[t],(t=>e===t)))))(n,t)?A.some({value:o,unit:n}):A.none()})),aD={tab:x(9),escape:x(27),enter:x(13),backspace:x(8),delete:x(46),left:x(37),up:x(38),right:x(39),down:x(40),space:x(32),home:x(36),end:x(35),pageUp:x(33),pageDown:x(34)},iD={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},lD={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},cD=(e,t)=>/[0-9.]+px$/.test(e)?((e,t)=>{const o=Math.pow(10,t);return Math.round(e*o)/o})(72*parseInt(e,10)/96,t||0)+"pt":be(lD,e).getOr(e),dD=e=>be(iD,e).getOr(""),uD=e=>{const t=()=>{let t=A.none();const o=n.data,s=e.queryCommandValue("FontSize");if(s)for(let e=3;t.isNone()&&e>=0;e--){const n=cD(s,e),r=dD(n);t=G(o,(e=>e.format===s||e.format===n||e.format===r))}return{matchOpt:t,size:s}},o=x(A.none),n=YM(e,"font_size_formats",XM.Space);return{tooltip:"Font sizes",text:A.some("12pt"),icon:A.none(),isSelectedFor:e=>t=>t.exists((t=>t.format===e)),getPreviewFor:o,getCurrentValue:()=>{const{matchOpt:e}=t();return e},onAction:t=>()=>{e.undoManager.transact((()=>{e.focus(),e.execCommand("FontSize",!1,t.format)}))},updateText:e=>{const{matchOpt:o,size:n}=t(),s=o.fold(x(n),(e=>e.title));Ir(e,vO,{text:s})},dataset:n,shouldHide:!1,isInvalid:T}},mD=(e,t)=>{const o="Paragraph";return{tooltip:"Formats",text:A.some(o),icon:A.none(),isSelectedFor:t=>()=>e.formatter.match(t),getCurrentValue:A.none,getPreviewFor:t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},onAction:xx(e),updateText:t=>{const n=e=>ET(e)?X(e.items,n):AT(e)?[{title:e.title,format:e.format}]:[],s=X(BT(e),n),r=ZM(e,x(s)).fold(x(o),(e=>e.title));Ir(t,vO,{text:r})},shouldHide:wf(e),isInvalid:t=>!e.formatter.canApply(t.format),dataset:t}},gD=x([os("toggleClass"),os("fetch"),Fi("onExecute"),ys("getHotspot",A.some),ys("getAnchorOverrides",x({})),wc(),Fi("onItemExecute"),us("lazySink"),os("dom"),Di("onOpen"),hu("splitDropdownBehaviours",[uw,Pp,oh]),ys("matchWidth",!1),ys("useMinWidth",!1),ys("eventOrder",{}),us("role")].concat(Ew())),pD=Uu({factory:Wh,schema:[os("dom")],name:"arrow",defaults:()=>({buttonBehaviours:kl([oh.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each(Rr)},buttonBehaviours:kl([dh.config({toggleOnExecute:!1,toggleClass:e.toggleClass})])})}),hD=Uu({factory:Wh,schema:[os("dom")],name:"button",defaults:()=>({buttonBehaviours:kl([oh.revoke()])}),overrides:e=>({dom:{tag:"span",attributes:{role:"presentation"}},action:t=>{t.getSystem().getByUid(e.uid).each((o=>{e.onExecute(o,t)}))}})}),fD=x([pD,hD,ju({factory:{sketch:e=>({uid:e.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:e.text}})},schema:[os("text")],name:"aria-descriptor"}),Wu({schema:[Ei()],name:"menu",defaults:e=>({onExecute:(t,o)=>{t.getSystem().getByUid(e.uid).each((n=>{e.onItemExecute(n,t,o)}))}})}),yw()]),bD=bm({name:"SplitDropdown",configFields:gD(),partFields:fD(),factory:(e,t,o,n)=>{const s=e=>{wm.getCurrent(e).each((e=>{Gm.highlightFirst(e),Pp.focusIn(e)}))},r=t=>{kw(e,w,t,n,s,zh.HighlightMenuAndItem).get(b)},a=t=>{const o=nm(t,e,"button");return Rr(o),A.some(!0)},i={...Hr([Kr(((t,o)=>{om(t,e,"aria-descriptor").each((e=>{const o=la("aria");kt(e.element,"id",o),kt(t.element,"aria-describedby",o)}))}))]),...mh(A.some(r))},l={repositionMenus:e=>{dh.isOn(e)&&Tw(e)}};return{uid:e.uid,dom:e.dom,components:t,apis:l,eventOrder:{...e.eventOrder,[ur()]:["disabling","toggling","alloy.base.behaviour"]},events:i,behaviours:bu(e.splitDropdownBehaviours,[uw.config({others:{sandbox:t=>{const o=nm(t,e,"arrow");return _w(e,t,{onOpen:()=>{dh.on(o),dh.on(t)},onClose:()=>{dh.off(o),dh.off(t)}})}}}),Pp.config({mode:"special",onSpace:a,onEnter:a,onDown:e=>(r(e),A.some(!0))}),oh.config({}),dh.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:e.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:(e,t)=>e.repositionMenus(t)}}),vD=e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setText:t=>Ir(e,vO,{text:t}),setIcon:t=>Ir(e,yO,{icon:t})}),yD=e=>({setActive:t=>{dh.set(e,t)},isActive:()=>dh.isOn(e),isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setText:t=>Ir(e,vO,{text:t}),setIcon:t=>Ir(e,yO,{icon:t})}),xD=(e,t)=>e.map((e=>({"aria-label":t.translate(e),title:t.translate(e)}))).getOr({}),wD=la("focus-button"),SD=(e,t,o,n,s)=>{const r=t.map((e=>jh(bO(e,"tox-tbtn",s)))),a=e.map((e=>jh(fO(e,s.icons))));return{dom:{tag:"button",classes:["tox-tbtn"].concat(t.isSome()?["tox-tbtn--select"]:[]),attributes:xD(o,s)},components:Dy([a.map((e=>e.asSpec())),r.map((e=>e.asSpec()))]),eventOrder:{[Ws()]:["focusing","alloy.base.behaviour",uO],[Sr()]:[uO,"toolbar-group-button-events"]},buttonBehaviours:kl([Oy(s.isDisabled),Sy(),Jp(uO,[Kr(((e,t)=>gO(e))),Ur(vO,((e,t)=>{r.bind((t=>t.getOpt(e))).each((e=>{Kp.set(e,[ti(s.translate(t.event.text))])}))})),Ur(yO,((e,t)=>{a.bind((t=>t.getOpt(e))).each((e=>{Kp.set(e,[fO(t.event.icon,s.icons)])}))})),Ur(Ws(),((e,t)=>{t.event.prevent(),Fr(e,wD)}))])].concat(n.getOr([])))}},kD=(e,t,o)=>{var n;const s=Es(b),r=SD(e.icon,e.text,e.tooltip,A.none(),o);return Wh.sketch({dom:r.dom,components:r.components,eventOrder:mO,buttonBehaviours:{...kl([Jp("toolbar-button-events",[(a={onAction:e.onAction,getApi:t.getApi},Qr(((e,t)=>{_y(a,e)((t=>{Ir(e,dO,{buttonApi:t}),a.onAction(t)}))}))),Ty(t,s),Ey(t,s)]),Oy((()=>!e.enabled||o.isDisabled())),Sy()].concat(t.toolbarButtonBehaviours)),[uO]:null===(n=r.buttonBehaviours)||void 0===n?void 0:n[uO]}});var a},CD=(e,t,o)=>kD(e,{toolbarButtonBehaviours:o.length>0?[Jp("toolbarButtonWith",o)]:[],getApi:vD,onSetup:e.onSetup},t),OD=(e,t,o)=>kD(e,{toolbarButtonBehaviours:[Kp.config({}),dh.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(o.length>0?[Jp("toolbarToggleButtonWith",o)]:[]),getApi:yD,onSetup:e.onSetup},t),_D=(e,t,o)=>n=>fw((e=>t.fetch(e))).map((s=>A.from(Hw(fn(Zx(la("menu-value"),s,(o=>{t.onItemAction(e(n),o)}),t.columns,t.presets,pb.CLOSE_ON_EXECUTE,t.select.getOr(T),o),{movement:ew(t.columns,t.presets),menuBehaviours:ly("auto"!==t.columns?[]:[Kr(((e,o)=>{iy(e,4,_b(t.presets)).each((({numRows:t,numColumns:o})=>{Pp.setGridSize(e,t,o)}))}))])}))))),TD=[{name:"history",items:["undo","redo"]},{name:"ai",items:["aidialog","aishortcuts"]},{name:"styles",items:["styles"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],ED=(e,t)=>(o,n,s)=>{const r=e(o).mapError((e=>Kn(e))).getOrDie();return t(r,n,s)},AD={button:ED(Fv,((e,t)=>{return o=e,n=t.shared.providers,CD(o,n,[]);var o,n})),togglebutton:ED(Nv,((e,t)=>{return o=e,n=t.shared.providers,OD(o,n,[]);var o,n})),menubutton:ED(aA,((e,t)=>t_(e,"tox-tbtn",t,A.none(),!1))),splitbutton:ED((e=>qn("SplitButton",iA,e)),((e,t)=>((e,t)=>{const o=e=>({isEnabled:()=>!Rm.isDisabled(e),setEnabled:t=>Rm.set(e,!t),setIconFill:(t,o)=>{pi(e.element,`svg path[class="${t}"], rect[class="${t}"]`).each((e=>{kt(e,"fill",o)}))},setActive:t=>{kt(e.element,"aria-pressed",t),pi(e.element,"span").each((o=>{e.getSystem().getByDom(o).each((e=>dh.set(e,t)))}))},isActive:()=>pi(e.element,"span").exists((t=>e.getSystem().getByDom(t).exists(dh.isOn))),setText:t=>pi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Ir(e,vO,{text:t}))))),setIcon:t=>pi(e.element,"span").each((o=>e.getSystem().getByDom(o).each((e=>Ir(e,yO,{icon:t})))))}),n=Es(b),s={getApi:o,onSetup:e.onSetup};return bD.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:{"aria-pressed":!1,...xD(e.tooltip,t.providers)}},onExecute:t=>{const n=o(t);n.isEnabled()&&e.onAction(n)},onItemExecute:(e,t,o)=>{},splitDropdownBehaviours:kl([Cy(t.providers.isDisabled),Sy(),Jp("split-dropdown-events",[Kr(((e,t)=>gO(e))),Ur(wD,oh.focus),Ty(s,n),Ey(s,n)]),IS.config({})]),eventOrder:{[Sr()]:["alloy.base.behaviour","split-dropdown-events"]},toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:_D(o,e,t.providers),parts:{menu:Bb(0,e.columns,e.presets)},components:[bD.parts.button(SD(e.icon,e.text,A.none(),A.some([dh.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),bD.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:Jh("chevron-down",t.providers.icons)},buttonBehaviours:kl([Cy(t.providers.isDisabled),Sy(),Zh()])}),bD.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})})(e,t.shared))),grouptoolbarbutton:ED((e=>qn("GroupToolbarButton",nA,e)),((e,t,o)=>{const n=o.ui.registry.getAll().buttons,s={[yc]:t.shared.header.isPositionedAtTop()?vc.TopToBottom:vc.BottomToTop};if(Cf(o)===nf.floating)return((e,t,o,n)=>{const s=t.shared,r=Es(b),a={toolbarButtonBehaviours:[],getApi:vD,onSetup:e.onSetup},i=[Jp("toolbar-group-button-events",[Ty(a,r),Ey(a,r)])];return jA.sketch({lazySink:s.getSink,fetch:()=>fw((t=>{t(H(o(e.items),nM))})),markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:SD(e.icon,e.text,e.tooltip,A.some(i),s.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:n}}}})})(e,t,(e=>DD(o,{buttons:n,toolbar:e,allowToolbarGroups:!1},t,A.none())),s);throw new Error("Toolbar groups are only supported when using floating toolbar mode")}))},MD={styles:(e,t)=>{const o={type:"advanced",...t.styles};return qM(e,t,mD(e,o))},fontsize:(e,t)=>qM(e,t,uD(e)),fontsizeinput:(e,t)=>((e,t,o)=>{let n=A.none();const s=yx(e,"NodeChange SwitchMode",(t=>{const s=t.getComponent();n=A.some(s),o.updateInputValue(s),Rm.set(s,!e.selection.isEditable())})),r=e=>({getComponent:x(e)}),a=Es(b),i=la("custom-number-input-events"),l=(e,t,s)=>{const r=n.map((e=>pu.getValue(e))).getOr(""),a=o.getNewValue(r,e),i=r.length-`${a}`.length,l=n.map((e=>e.element.dom.selectionStart-i)),c=n.map((e=>e.element.dom.selectionEnd-i));o.onAction(a,s),n.each((e=>{pu.setValue(e,a),t&&(l.each((t=>e.element.dom.selectionStart=t)),c.each((t=>e.element.dom.selectionEnd=t)))}))},c=(e,t)=>l(((e,t)=>e-t),e,t),d=(e,t)=>l(((e,t)=>e+t),e,t),u=e=>rt(e.element).fold(A.none,(e=>(Dl(e),A.some(!0)))),m=e=>Fl(e.element)?(ct(e.element).each((e=>Dl(e))),A.some(!0)):A.none(),g=(o,n,s,i)=>{const l=t.shared.providers.translate(s),c=la("altExecuting"),d=yx(e,"NodeChange SwitchMode",(t=>{Rm.set(t.getComponent(),!e.selection.isEditable())})),u=e=>{Rm.isDisabled(e)||o(!0)};return Wh.sketch({dom:{tag:"button",attributes:{title:l,"aria-label":l},classes:i.concat(n)},components:[hO(n,t.shared.providers.icons)],buttonBehaviours:kl([Rm.config({}),Jp(c,[Ty({onSetup:d,getApi:r},a),Ey({getApi:r},a),Ur(Ks(),((e,t)=>{t.event.raw.keyCode!==aD.space()&&t.event.raw.keyCode!==aD.enter()||Rm.isDisabled(e)||o(!1)})),Ur(er(),u),Ur(Ps(),u)])]),eventOrder:{[Ks()]:[c,"keying"],[er()]:[c,"alloy.base.behaviour"],[Ps()]:[c,"alloy.base.behaviour"]}})},p=jh(g((e=>c(!1,e)),"minus","Decrease font size",["highlight-on-focus"])),h=jh(g((e=>d(!1,e)),"plus","Increase font size",["highlight-on-focus"])),f=jh({dom:{tag:"div",classes:["tox-input-wrapper","highlight-on-focus"]},components:[Vb.sketch({inputBehaviours:kl([Rm.config({}),Jp(i,[Ty({onSetup:s,getApi:r},a),Ey({getApi:r},a)]),Jp("input-update-display-text",[Ur(vO,((e,t)=>{pu.setValue(e,t.event.text)})),Ur(Ys(),(e=>{o.onAction(pu.getValue(e))})),Ur(Qs(),(e=>{o.onAction(pu.getValue(e))}))]),Pp.config({mode:"special",onEnter:e=>(l(w,!0,!0),A.some(!0)),onEscape:u,onUp:e=>(d(!0,!1),A.some(!0)),onDown:e=>(c(!0,!1),A.some(!0)),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})],behaviours:kl([oh.config({}),Pp.config({mode:"special",onEnter:m,onSpace:m,onEscape:u}),Jp("input-wrapper-events",[Ur(qs(),(e=>{L([p,h],(t=>{const o=Ve(t.get(e).element.dom);Fl(o)&&Bl(o)}))}))])])});return{dom:{tag:"div",classes:["tox-number-input"]},components:[p.asSpec(),f.asSpec(),h.asSpec()],behaviours:kl([oh.config({}),Pp.config({mode:"flow",focusInside:pg.OnEnterOrSpaceMode,cycles:!1,selector:"button, .tox-input-wrapper",onEscape:e=>Fl(e.element)?A.none():(Dl(e.element),A.some(!0))})])}})(e,t,(e=>{const t=()=>e.queryCommandValue("FontSize");return{updateInputValue:e=>Ir(e,vO,{text:t()}),onAction:(t,o)=>e.execCommand("FontSize",!1,t,{skip_focus:!o}),getNewValue:(o,n)=>{rD(o,["unsupportedLength","empty"]);const s=rD(o,["unsupportedLength","empty"]).or(rD(t(),["unsupportedLength","empty"])),r=s.map((e=>e.value)).getOr(16),a=Rf(e),i=s.map((e=>e.unit)).filter((e=>""!==e)).getOr(a),l=n(r,(e=>{var t;return null!==(t={em:{step:.1},cm:{step:.1},in:{step:.1},pc:{step:.1},ch:{step:.1},rem:{step:.1}}[e])&&void 0!==t?t:{step:1}})(i).step);return`${(e=>e>=0)(l)?l:r}${i}`}}})(e)),fontfamily:(e,t)=>qM(e,t,oD(e)),blocks:(e,t)=>qM(e,t,QM(e)),align:(e,t)=>qM(e,t,JM(e))},DD=(e,t,o,n)=>{const s=(e=>{const t=e.toolbar,o=e.buttons;return!1===t?[]:void 0===t||!0===t?(e=>{const t=H(TD,(t=>{const o=U(t.items,(t=>ve(e,t)||ve(MD,t)));return{name:t.name,items:o}}));return U(t,(e=>e.items.length>0))})(o):r(t)?(e=>{const t=e.split("|");return H(t,(e=>({items:e.trim().split(" ")})))})(t):(e=>f(e,(e=>ve(e,"name")&&ve(e,"items"))))(t)?t:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])})(t),a=H(s,(s=>{const r=X(s.items,(s=>0===s.trim().length?[]:((e,t,o,n,s,r)=>be(t,o.toLowerCase()).orThunk((()=>r.bind((e=>re(e,(e=>be(t,e+o.toLowerCase()))))))).fold((()=>be(MD,o.toLowerCase()).map((t=>t(e,s)))),(t=>"grouptoolbarbutton"!==t.type||n?((e,t,o)=>be(AD,e.type).fold((()=>(console.error("skipping button defined by",e),A.none())),(n=>A.some(n(e,t,o)))))(t,s,e):(console.warn(`Ignoring the '${o}' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested.`),A.none()))))(e,t.buttons,s,t.allowToolbarGroups,o,n).toArray()));return{title:A.from(e.translate(s.name)),items:r}}));return U(a,(e=>e.items.length>0))},BD=(e,t,o,n)=>{const s=t.mainUi.outerContainer,a=o.toolbar,i=o.buttons;if(f(a,r)){const t=a.map((t=>{const s={toolbar:t,buttons:i,allowToolbarGroups:o.allowToolbarGroups};return DD(e,s,n,A.none())}));IM.setToolbars(s,t)}else IM.setToolbar(s,DD(e,o,n,A.none()))},FD=Do(),ID=FD.os.isiOS()&&FD.os.version.major<=12;var RD=Object.freeze({__proto__:null,render:async(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=t,i=Es(0),l=r.outerContainer;await WM(e);const d=Ve(s.targetNode),u=ft(ht(d));Rd(d,r.mothership),((e,t,o)=>{lb(e)&&Rd(o.mainUi.mothership.element,o.popupUi.mothership),Id(t,o.dialogUi.mothership)})(e,u,t),e.on("PostRender",(()=>{IM.setSidebar(l,o.sidebar,$f(e)),BD(e,t,o,n),i.set(e.getWin().innerWidth),IM.setMenubar(l,VM(e,o)),IM.setViews(l,o.views),((e,t)=>{const{uiMotherships:o}=t,n=e.dom;let s=e.getWin();const r=e.getDoc().documentElement,a=Es($t(s.innerWidth,s.innerHeight)),i=Es($t(r.offsetWidth,r.offsetHeight)),l=()=>{const t=a.get();t.left===s.innerWidth&&t.top===s.innerHeight||(a.set($t(s.innerWidth,s.innerHeight)),gx(e))},c=()=>{const t=e.getDoc().documentElement,o=i.get();o.left===t.offsetWidth&&o.top===t.offsetHeight||(i.set($t(t.offsetWidth,t.offsetHeight)),gx(e))},d=t=>{((e,t)=>{e.dispatch("ScrollContent",t)})(e,t)};n.bind(s,"resize",l),n.bind(s,"scroll",d);const u=oc(Ve(e.getBody()),"load",c);e.on("hide",(()=>{L(o,(e=>{Dt(e.element,"display","none")}))})),e.on("show",(()=>{L(o,(e=>{Ht(e.element,"display")}))})),e.on("NodeChange",c),e.on("remove",(()=>{u.unbind(),n.unbind(s,"resize",l),n.unbind(s,"scroll",d),s=null}))})(e,t)}));const m=IM.getSocket(l).getOrDie("Could not find expected socket element");if(ID){Bt(m.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"});const t=((e,t)=>{let o=null;return{cancel:()=>{c(o)||(clearTimeout(o),o=null)},throttle:(...t)=>{c(o)&&(o=setTimeout((()=>{o=null,e.apply(null,t)}),20))}}})((()=>{e.dispatch("ScrollContent")})),o=tc(m.element,"scroll",t.throttle);e.on("remove",o.unbind)}wy(e,t),e.addCommand("ToggleSidebar",((t,o)=>{IM.toggleSidebar(l,o),e.dispatch("ToggleSidebar")})),e.addQueryValueHandler("ToggleSidebar",(()=>{var e;return null!==(e=IM.whichSidebar(l))&&void 0!==e?e:""})),e.addCommand("ToggleView",((t,o)=>{if(IM.toggleView(l,o)){const t=l.element;r.mothership.broadcastOn([Yd()],{target:t}),L(a,(e=>{e.broadcastOn([Yd()],{target:t})})),c(IM.whichView(l))&&(e.focus(),e.nodeChanged(),IM.refreshToolbar(l))}})),e.addQueryValueHandler("ToggleView",(()=>{var e;return null!==(e=IM.whichView(l))&&void 0!==e?e:""}));const g=Cf(e);g!==nf.sliding&&g!==nf.floating||e.on("ResizeWindow ResizeEditor ResizeContent",(()=>{const o=e.getWin().innerWidth;o!==i.get()&&(IM.refreshToolbar(t.mainUi.outerContainer),i.set(o))}));const p={setEnabled:e=>{xy(t,!e)},isEnabled:()=>!Rm.isDisabled(l)};return{iframeContainer:m.element.dom,editorContainer:l.element.dom,api:p}}});const ND=e=>/^[0-9\.]+(|px)$/i.test(""+e)?A.some(parseInt(""+e,10)):A.none(),VD=e=>h(e)?e+"px":e,zD=(e,t,o)=>{const n=t.filter((t=>e<t)),s=o.filter((t=>e>t));return n.or(s).getOr(e)},HD=e=>{const t=pf(e),o=hf(e),n=bf(e);return ND(t).map((e=>zD(e,o,n)))},{ToolbarLocation:LD,ToolbarMode:PD}=db,UD=(e,t,o,n,s)=>{const{mainUi:r,uiMotherships:a}=o,i=rf.DOM,l=sb(e),c=ib(e),d=bf(e).or(HD(e)),u=n.shared.header,m=u.isPositionedAtTop,g=Cf(e),p=g===PD.sliding||g===PD.floating,h=Es(!1),f=()=>h.get()&&!e.removed,b=e=>p?e.fold(x(0),(e=>e.components().length>1?Wt(e.components()[1].element):0)):0,v=()=>{L(a,(e=>{e.broadcastOn([Kd()],{})}))},y=o=>{if(!f())return;l||s.on((e=>{const o=d.getOrThunk((()=>{const e=ND(It(xt(),"margin-left")).getOr(0);return Jt(xt())-Xt(t).left+e}));Dt(e.element,"max-width",o+"px")}));const n=l?A.none():(()=>{if(l)return A.none();if(Xt(r.outerContainer.element).left+Zt(r.outerContainer.element)>=window.innerWidth-40||Nt(r.outerContainer.element,"width").isSome()){Dt(r.outerContainer.element,"position","absolute"),Dt(r.outerContainer.element,"left","0px"),Ht(r.outerContainer.element,"width");const e=Zt(r.outerContainer.element);return A.some(e)}return A.none()})();p&&IM.refreshToolbar(r.outerContainer),l||(o=>{s.on((n=>{const s=IM.getToolbar(r.outerContainer),a=b(s),i=Jo(t),{top:l,left:c}=((e,t)=>lb(e)?xE(t):A.none())(e,r.outerContainer.element).fold((()=>({top:m()?Math.max(i.y-Wt(n.element)+a,0):i.bottom,left:i.x})),(e=>{var t;const o=Jo(e),s=null!==(t=e.dom.scrollTop)&&void 0!==t?t:0,r=Ze(e,xt()),l=r?Math.max(i.y-Wt(n.element)+a,0):i.y-o.y+s-Wt(n.element)+a;return{top:m()?l:i.bottom,left:r?i.x:i.x-o.x}})),d={position:"absolute",left:Math.round(c)+"px",top:Math.round(l)+"px"},u=o.map((e=>{const t=Uo(),o=window.innerWidth-(c-t.left);return{width:Math.max(Math.min(e,o),150)+"px"}})).getOr({});Bt(r.outerContainer.element,{...d,...u})}))})(n),c&&s.on(o),v()},w=()=>!(l||!c||!f())&&s.get().exists((o=>{const n=u.getDockingMode(),a=(o=>{switch(_f(e)){case LD.auto:const e=IM.getToolbar(r.outerContainer),n=b(e),s=Wt(o.element)-n,a=Jo(t);if(a.y>s)return"top";{const e=ot(t),o=Math.max(e.dom.scrollHeight,Wt(e));return a.bottom<o-s||en().bottom<a.bottom-s?"bottom":"top"}case LD.bottom:return"bottom";case LD.top:default:return"top"}})(o);return a!==n&&(i=a,s.on((e=>{$E.setModes(e,[i]),u.setDockingMode(i);const t=m()?vc.TopToBottom:vc.BottomToTop;kt(e.element,yc,t)})),!0);var i}));return{isVisible:f,isPositionedAtTop:m,show:()=>{h.set(!0),Dt(r.outerContainer.element,"display","flex"),i.addClass(e.getBody(),"mce-edit-focus"),L(a,(e=>{Ht(e.element,"display")})),w(),lb(e)?y((e=>$E.isDocked(e)?$E.reset(e):$E.refresh(e))):y($E.refresh)},hide:()=>{h.set(!1),Dt(r.outerContainer.element,"display","none"),i.removeClass(e.getBody(),"mce-edit-focus"),L(a,(e=>{Dt(e.element,"display","none")}))},update:y,updateMode:()=>{w()&&y($E.reset)},repositionPopups:v}},WD=(e,t)=>{const o=Jo(e);return{pos:t?o.y:o.bottom,bounds:o}};var jD=Object.freeze({__proto__:null,render:async(e,t,o,n,s)=>{const{mainUi:r}=t,a=Ql(),i=Ve(s.targetNode),l=UD(e,i,t,n,a),c=Af(e);await jM(e);const d=()=>{if(a.isSet())return void l.show();a.set(IM.getHeader(r.outerContainer).getOrDie());const s=rb(e);lb(e)?(Rd(i,r.mothership),Rd(i,t.popupUi.mothership)):Id(s,r.mothership),Id(s,t.dialogUi.mothership),BD(e,t,o,n),IM.setMenubar(r.outerContainer,VM(e,o)),l.show(),((e,t,o,n)=>{const s=Es(WD(t,o.isPositionedAtTop())),r=n=>{const{pos:r,bounds:a}=WD(t,o.isPositionedAtTop()),{pos:i,bounds:l}=s.get(),c=a.height!==l.height||a.width!==l.width;s.set({pos:r,bounds:a}),c&&gx(e,n),o.isVisible()&&(i!==r?o.update($E.reset):c&&(o.updateMode(),o.repositionPopups()))};n||(e.on("activate",o.show),e.on("deactivate",o.hide)),e.on("SkinLoaded ResizeWindow",(()=>o.update($E.reset))),e.on("NodeChange keydown",(e=>{requestAnimationFrame((()=>r(e)))}));let a=0;const i=PC((()=>o.update($E.refresh)),33);e.on("ScrollWindow",(()=>{const e=Uo().left;e!==a&&(a=e,i.throttle()),o.updateMode()})),lb(e)&&e.on("ElementScroll",(e=>{o.update($E.refresh)}));const l=Zl();l.set(oc(Ve(e.getBody()),"load",(e=>r(e.raw)))),e.on("remove",(()=>{l.clear()}))})(e,i,l,c),e.nodeChanged()};e.on("show",d),e.on("hide",l.hide),c||(e.on("focus",d),e.on("blur",l.hide)),e.on("init",(()=>{(e.hasFocus()||c)&&d()})),wy(e,t);const u={show:d,hide:l.hide,setEnabled:e=>{xy(t,!e)},isEnabled:()=>!Rm.isDisabled(r.outerContainer)};return{editorContainer:r.outerContainer.element.dom,api:u}}});const GD="contexttoolbar-hide",$D=(e,t)=>Ur(dO,((o,n)=>{const s=(e=>({hide:()=>Fr(e,hr()),getValue:()=>pu.getValue(e)}))(e.get(o));t.onAction(s,n.event.buttonApi)})),qD=(e,t)=>{const o=e.label.fold((()=>({})),(e=>({"aria-label":e}))),n=jh(Vb.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:e.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:kl([Pp.config({mode:"special",onEnter:e=>s.findPrimary(e).map((e=>(Rr(e),!0))),onLeft:(e,t)=>(t.cut(),A.none()),onRight:(e,t)=>(t.cut(),A.none())})])})),s=((e,t,o)=>{const n=H(t,(t=>jh(((e,t,o)=>(e=>"contextformtogglebutton"===e.type)(t)?((e,t,o)=>{const{primary:n,...s}=t.original,r=Xn(Nv({...s,type:"togglebutton",onAction:b}));return OD(r,o,[$D(e,t)])})(e,t,o):((e,t,o)=>{const{primary:n,...s}=t.original,r=Xn(Fv({...s,type:"button",onAction:b}));return CD(r,o,[$D(e,t)])})(e,t,o))(e,t,o))));return{asSpecs:()=>H(n,(e=>e.asSpec())),findPrimary:e=>re(t,((t,o)=>t.primary?A.from(n[o]).bind((t=>t.getOpt(e))).filter(C(Rm.isDisabled)):A.none()))}})(n,e.commands,t);return[{title:A.none(),items:[n.asSpec()]},{title:A.none(),items:s.asSpecs()}]},XD=(e,t,o)=>t.bottom-e.y>=o&&e.bottom-t.y>=o,YD=e=>{const t=(e=>{const t=e.getBoundingClientRect();if(t.height<=0&&t.width<=0){const o=ut(Ve(e.startContainer),e.startOffset).element;return($e(o)?st(o):A.some(o)).filter(Ge).map((e=>e.dom.getBoundingClientRect())).getOr(t)}return t})(e.selection.getRng());if(e.inline){const e=Uo();return Ko(e.left+t.left,e.top+t.top,t.width,t.height)}{const o=Zo(Ve(e.getBody()));return Ko(o.x+t.left,o.y+t.top,t.width,t.height)}},KD=(e,t,o,n=0)=>{const s=Go(window),r=Jo(Ve(e.getContentAreaContainer())),a=Kf(e)||Qf(e)||tb(e),{x:i,width:l}=((e,t,o)=>{const n=Math.max(e.x+o,t.x);return{x:n,width:Math.min(e.right-o,t.right)-n}})(r,s,n);if(e.inline&&!a)return Ko(i,s.y,l,s.height);{const a=t.header.isPositionedAtTop(),{y:c,bottom:d}=((e,t,o,n,s,r)=>{const a=Ve(e.getContainer()),i=pi(a,".tox-editor-header").getOr(a),l=Jo(i),c=l.y>=t.bottom,d=n&&!c;if(e.inline&&d)return{y:Math.max(l.bottom+r,o.y),bottom:o.bottom};if(e.inline&&!d)return{y:o.y,bottom:Math.min(l.y-r,o.bottom)};const u="line"===s?Jo(a):t;return d?{y:Math.max(l.bottom+r,o.y),bottom:Math.min(u.bottom-r,o.bottom)}:{y:Math.max(u.y+r,o.y),bottom:Math.min(l.y-r,o.bottom)}})(e,r,s,a,o,n);return Ko(i,c,l,d-c)}},JD={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"],inset:["tox-pop--inset"]},ZD={maxHeightFunction:cc(),maxWidthFunction:VA()},QD=e=>"node"===e,eB=(e,t,o,n,s)=>{const r=YD(e),a=n.lastElement().exists((e=>Ze(o,e)));return((e,t)=>{const o=e.selection.getRng(),n=ut(Ve(o.startContainer),o.startOffset);return o.startContainer===o.endContainer&&o.startOffset===o.endOffset-1&&Ze(n.element,t)})(e,o)?a?gT:lT:a?((e,o,s)=>{const a=Nt(e,"position");Dt(e,"position",o);const i=XD(r,Jo(t),-20)&&!n.isReposition()?hT:gT;return a.each((t=>Dt(e,"position",t))),i})(t,n.getMode()):("fixed"===n.getMode()?s.y+Uo().top:s.y)+(Wt(t)+12)<=r.y?lT:cT},tB=(e,t,o,n)=>{const s=t=>(n,s,r,a,i)=>({...eB(e,a,t,o,i)({...n,y:i.y,height:i.height},s,r,a,i),alwaysFit:!0}),r=e=>QD(n)?[s(e)]:[];return t?{onLtr:e=>[cl,sl,rl,al,il,ll].concat(r(e)),onRtl:e=>[cl,rl,sl,il,al,ll].concat(r(e))}:{onLtr:e=>[ll,cl,al,sl,il,rl].concat(r(e)),onRtl:e=>[ll,cl,il,rl,al,sl].concat(r(e))}},oB=(e,t)=>{const o=U(t,(t=>t.predicate(e.dom))),{pass:n,fail:s}=P(o,(e=>"contexttoolbar"===e.type));return{contextToolbars:n,contextForms:s}},nB=(e,t)=>{const o={},n=[],s=[],r={},a={},i=ae(e);return L(i,(i=>{const l=e[i];"contextform"===l.type?((e,i)=>{const l=Xn(qn("ContextForm",Wv,i));o[e]=l,l.launch.map((o=>{r["form:"+e]={...i.launch,type:"contextformtogglebutton"===o.type?"togglebutton":"button",onAction:()=>{t(l)}}})),"editor"===l.scope?s.push(l):n.push(l),a[e]=l})(i,l):"contexttoolbar"===l.type&&((e,t)=>{var o;(o=t,qn("ContextToolbar",jv,o)).each((o=>{"editor"===t.scope?s.push(o):n.push(o),a[e]=o}))})(i,l)})),{forms:o,inNodeScope:n,inEditorScope:s,lookupTable:a,formNavigators:r}},sB=la("forward-slide"),rB=la("backward-slide"),aB=la("change-slide-event"),iB="tox-pop--resizing",lB="tox-pop--transition",cB=(e,t,o,n)=>{const s=n.backstage,r=s.shared,a=Do().deviceType.isTouch,i=Ql(),l=Ql(),c=Ql(),d=ri((e=>{const t=Es([]);return Ph.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:e=>{t.set([]),Ph.getContent(e).each((e=>{Ht(e.element,"visibility")})),Pa(e.element,iB),Ht(e.element,"width")},inlineBehaviours:kl([Jp("context-toolbar-events",[Yr(or(),((e,t)=>{"width"===t.event.raw.propertyName&&(Pa(e.element,iB),Ht(e.element,"width"))})),Ur(aB,((e,t)=>{const o=e.element;Ht(o,"width");const n=Jt(o);Ph.setContent(e,t.event.contents),La(o,iB);const s=Jt(o);Dt(o,"width",n+"px"),Ph.getContent(e).each((e=>{t.event.focus.bind((e=>(Dl(e),Rl(o)))).orThunk((()=>(Pp.focusIn(e),Il(ht(o)))))})),setTimeout((()=>{Dt(e.element,"width",s+"px")}),0)})),Ur(sB,((e,o)=>{Ph.getContent(e).each((o=>{t.set(t.get().concat([{bar:o,focus:Il(ht(e.element))}]))})),Ir(e,aB,{contents:o.event.forwardContents,focus:A.none()})})),Ur(rB,((e,o)=>{ne(t.get()).each((o=>{t.set(t.get().slice(0,t.get().length-1)),Ir(e,aB,{contents:ai(o.bar),focus:o.focus})}))}))]),Pp.config({mode:"special",onEscape:o=>ne(t.get()).fold((()=>e.onEscape()),(e=>(Fr(o,rB),A.some(!0))))})]),lazySink:()=>sn.value(e.sink)})})({sink:o,onEscape:()=>(e.focus(),A.some(!0))})),u=()=>{const t=c.get().getOr("node"),o=QD(t)?1:0;return KD(e,r,t,o)},m=()=>!(e.removed||a()&&s.isContextMenuOpen()),g=()=>{if(m()){const t=u(),o=xe(c.get(),"node")?((e,t)=>t.filter((e=>yt(e)&&je(e))).map(Zo).getOrThunk((()=>YD(e))))(e,i.get()):YD(e);return t.height<=0||!XD(o,t,.01)}return!0},p=()=>{i.clear(),l.clear(),c.clear(),Ph.hide(d)},h=()=>{if(Ph.isOpen(d)){const e=d.element;Ht(e,"display"),g()?Dt(e,"display","none"):(l.set(0),Ph.reposition(d))}},f=t=>({dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:kl([Pp.config({mode:"acyclic"}),Jp("pop-dialog-wrap-events",[Kr((t=>{e.shortcuts.add("ctrl+F9","focus statusbar",(()=>Pp.focusIn(t)))})),Jr((t=>{e.shortcuts.remove("ctrl+F9")}))])])}),v=Qt((()=>nB(t,(e=>{const t=y([e]);Ir(d,sB,{forwardContents:f(t)})})))),y=t=>{const{buttons:o}=e.ui.registry.getAll(),s={...o,...v().formNavigators},a=Cf(e)===nf.scrolling?nf.scrolling:nf.default,i=q(H(t,(t=>"contexttoolbar"===t.type?((t,o)=>DD(e,{buttons:t,toolbar:o.items,allowToolbarGroups:!1},n.backstage,A.some(["form:"])))(s,t):((e,t)=>qD(e,t))(t,r.providers))));return lM({type:a,uid:la("context-toolbar"),initGroups:i,onEscape:A.none,cyclicKeying:!0,providers:r.providers})},x=(t,n)=>{if(S.cancel(),!m())return;const s=y(t),p=t[0].position,h=((t,n)=>{const s="node"===t?r.anchors.node(n):r.anchors.cursor(),c=((e,t,o,n)=>"line"===t?{bubble:gc(12,0,JD),layouts:{onLtr:()=>[dl],onRtl:()=>[ul]},overrides:ZD}:{bubble:gc(0,12,JD,1/12),layouts:tB(e,o,n,t),overrides:ZD})(e,t,a(),{lastElement:i.get,isReposition:()=>xe(l.get(),0),getMode:()=>Sd.getMode(o)});return fn(s,c)})(p,n);c.set(p),l.set(1);const b=d.element;Ht(b,"display"),(e=>xe(Se(e,i.get(),Ze),!0))(n)||(Pa(b,lB),Sd.reset(o,d)),Ph.showWithinBounds(d,f(s),{anchor:h,transition:{classes:[lB],mode:"placement"}},(()=>A.some(u()))),n.fold(i.clear,i.set),g()&&Dt(b,"display","none")};let w=!1;const S=PC((()=>{!e.hasFocus()||e.removed||w||(Ua(d.element,lB)?S.throttle():((e,t)=>{const o=Ve(t.getBody()),n=e=>Ze(e,o),s=Ve(t.selection.getNode());return(e=>!n(e)&&!Qe(o,e))(s)?A.none():((e,t,o)=>{const n=oB(e,t);if(n.contextForms.length>0)return A.some({elem:e,toolbars:[n.contextForms[0]]});{const t=oB(e,o);if(t.contextForms.length>0)return A.some({elem:e,toolbars:[t.contextForms[0]]});if(n.contextToolbars.length>0||t.contextToolbars.length>0){const o=(e=>{if(e.length<=1)return e;{const t=t=>N(e,(e=>e.position===t)),o=t=>U(e,(e=>e.position===t)),n=t("selection"),s=t("node");if(n||s){if(s&&n){const e=o("node"),t=H(o("selection"),(e=>({...e,position:"node"})));return e.concat(t)}return o(n?"selection":"node")}return o("line")}})(n.contextToolbars.concat(t.contextToolbars));return A.some({elem:e,toolbars:o})}return A.none()}})(s,e.inNodeScope,e.inEditorScope).orThunk((()=>((e,t,o)=>e(t)?A.none():Fs(t,(e=>{if(Ge(e)){const{contextToolbars:t,contextForms:n}=oB(e,o.inNodeScope),s=n.length>0?n:(e=>{if(e.length<=1)return e;{const t=t=>G(e,(e=>e.position===t));return t("selection").orThunk((()=>t("node"))).orThunk((()=>t("line"))).map((e=>e.position)).fold((()=>[]),(t=>U(e,(e=>e.position===t))))}})(t);return s.length>0?A.some({elem:e,toolbars:s}):A.none()}return A.none()}),e))(n,s,e)))})(v(),e).fold(p,(e=>{x(e.toolbars,A.some(e.elem))})))}),17);e.on("init",(()=>{e.on("remove",p),e.on("ScrollContent ScrollWindow ObjectResized ResizeEditor longpress",h),e.on("click keyup focus SetContent",S.throttle),e.on(GD,p),e.on("contexttoolbar-show",(t=>{const o=v();be(o.lookupTable,t.toolbarKey).each((o=>{x([o],Ce(t.target!==e,t.target)),Ph.getContent(d).each(Pp.focusIn)}))})),e.on("focusout",(t=>{Uh.setEditorTimeout(e,(()=>{Rl(o.element).isNone()&&Rl(d.element).isNone()&&p()}),0)})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()&&p()})),e.on("AfterProgressState",(t=>{t.state?p():e.hasFocus()&&S.throttle()})),e.on("dragstart",(()=>{w=!0})),e.on("dragend drop",(()=>{w=!1})),e.on("NodeChange",(e=>{Rl(d.element).fold(S.throttle,b)}))}))},dB=(e,t)=>{const o=()=>{const o=t.getOptions(e),n=t.getCurrent(e).map(t.hash),s=Ql();return H(o,(o=>({type:"togglemenuitem",text:t.display(o),onSetup:r=>{const a=e=>{e&&(s.on((e=>e.setActive(!1))),s.set(r)),r.setActive(e)};a(xe(n,t.hash(o)));const i=t.watcher(e,o,a);return()=>{s.clear(),i()}},onAction:()=>t.setCurrent(e,o)})))};e.ui.registry.addMenuButton(t.name,{tooltip:t.text,icon:t.icon,fetch:e=>e(o()),onSetup:t.onToolbarSetup}),e.ui.registry.addNestedMenuItem(t.name,{type:"nestedmenuitem",text:t.text,getSubmenuItems:o,onSetup:t.onMenuSetup})},uB=e=>{dB(e,(e=>({name:"lineheight",text:"Line height",icon:"line-height",getOptions:Zf,hash:e=>((e,t)=>rD(e,["fixed","relative","empty"]).map((({value:e,unit:t})=>e+t)))(e).getOr(e),display:w,watcher:(e,t,o)=>e.formatter.formatChanged("lineheight",o,!1,{value:t}).unbind,getCurrent:e=>A.from(e.queryCommandValue("LineHeight")),setCurrent:(e,t)=>e.execCommand("LineHeight",!1,t),onToolbarSetup:bx(e),onMenuSetup:bx(e)}))(e)),(e=>A.from(Sf(e)).map((t=>({name:"language",text:"Language",icon:"language",getOptions:x(t),hash:e=>u(e.customCode)?e.code:`${e.code}/${e.customCode}`,display:e=>e.title,watcher:(e,t,o)=>{var n;return e.formatter.formatChanged("lang",o,!1,{value:t.code,customValue:null!==(n=t.customCode)&&void 0!==n?n:null}).unbind},getCurrent:e=>{const t=Ve(e.selection.getNode());return Is(t,(e=>A.some(e).filter(Ge).bind((e=>_t(e,"lang").map((t=>({code:t,customCode:_t(e,"data-mce-lang").getOrUndefined(),title:""})))))))},setCurrent:(e,t)=>e.execCommand("Lang",!1,t),onToolbarSetup:t=>{const o=Zl();return t.setActive(e.formatter.match("lang",{},void 0,!0)),o.set(e.formatter.formatChanged("lang",t.setActive,!0)),fx(o.clear,bx(e)(t))},onMenuSetup:bx(e)}))))(e).each((t=>dB(e,t)))},mB=e=>yx(e,"NodeChange",(t=>{t.setEnabled(e.queryCommandState("outdent")&&e.selection.isEditable())})),gB=(e,t)=>o=>{o.setActive(t.get());const n=e=>{t.set(e.state),o.setActive(e.state)};return e.on("PastePlainTextToggle",n),fx((()=>e.off("PastePlainTextToggle",n)),bx(e)(o))},pB=(e,t)=>()=>{e.execCommand("mceToggleFormat",!1,t)},hB=e=>{(e=>{(e=>{LC.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],((t,o)=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:vx(e,t.name),onAction:pB(e,t.name)})}));for(let t=1;t<=6;t++){const o="h"+t;e.ui.registry.addToggleButton(o,{text:o.toUpperCase(),tooltip:"Heading "+t,onSetup:vx(e,o),onAction:pB(e,o)})}})(e),(e=>{LC.each([{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"print",text:"Print",action:"mcePrint",icon:"print"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:wx(e,t.action)})})),LC.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:bx(e),onAction:wx(e,t.action)})}))})(e),(e=>{LC.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:wx(e,t.action),onSetup:vx(e,t.name)})}))})(e)})(e),(e=>{LC.each([{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"},{name:"print",text:"Print...",action:"mcePrint",icon:"print",shortcut:"Meta+P"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:wx(e,t.action)})})),LC.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"hr",text:"Horizontal line",action:"InsertHorizontalRule",icon:"horizontal-rule"}],(t=>{e.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onSetup:bx(e),onAction:wx(e,t.action)})})),e.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onSetup:bx(e),onAction:pB(e,"code")})})(e)},fB=(e,t)=>yx(e,"Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",(o=>{o.setEnabled(!e.mode.isReadOnly()&&e.undoManager[t]())})),bB=e=>yx(e,"VisualAid",(t=>{t.setActive(e.hasVisual)})),vB=(e,t)=>{(e=>{L([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],(t=>{e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:wx(e,t.cmd),onSetup:vx(e,t.name)})})),e.ui.registry.addButton("alignnone",{tooltip:"No alignment",icon:"align-none",onSetup:bx(e),onAction:wx(e,"JustifyNone")})})(e),hB(e),((e,t)=>{((e,t)=>{const o=$M(0,t,JM(e));e.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),onSetup:bx(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=$M(0,t,oD(e));e.ui.registry.addNestedMenuItem("fontfamily",{text:t.shared.providers.translate("Fonts"),onSetup:bx(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o={type:"advanced",...t.styles},n=$M(0,t,mD(e,o));e.ui.registry.addNestedMenuItem("styles",{text:"Formats",onSetup:bx(e),getSubmenuItems:()=>n.items.validateItems(n.getStyleItems())})})(e,t),((e,t)=>{const o=$M(0,t,QM(e));e.ui.registry.addNestedMenuItem("blocks",{text:"Blocks",onSetup:bx(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t),((e,t)=>{const o=$M(0,t,uD(e));e.ui.registry.addNestedMenuItem("fontsize",{text:"Font sizes",onSetup:bx(e),getSubmenuItems:()=>o.items.validateItems(o.getStyleItems())})})(e,t)})(e,t),(e=>{(e=>{e.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:fB(e,"hasUndo"),onAction:wx(e,"undo")}),e.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:fB(e,"hasRedo"),onAction:wx(e,"redo")})})(e),(e=>{e.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",enabled:!1,onSetup:fB(e,"hasUndo"),onAction:wx(e,"undo")}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",enabled:!1,onSetup:fB(e,"hasRedo"),onAction:wx(e,"redo")})})(e)})(e),(e=>{(e=>{e.addCommand("mceApplyTextcolor",((t,o)=>{((e,t,o)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.apply(t,{value:o}),e.nodeChanged()}))})(e,t,o)})),e.addCommand("mceRemoveTextcolor",(t=>{((e,t)=>{e.undoManager.transact((()=>{e.focus(),e.formatter.remove(t,{value:null},void 0,!0),e.nodeChanged()}))})(e,t)}))})(e);const t=Lx(e),o=Px(e),n=Es(t),s=Es(o);Yx(e,"forecolor","forecolor","Text color",n),Yx(e,"backcolor","hilitecolor","Background color",s),Kx(e,"forecolor","forecolor","Text color",n),Kx(e,"backcolor","hilitecolor","Background color",s)})(e),(e=>{(e=>{e.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:wx(e,"mceToggleVisualAid")})})(e),(e=>{e.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:bB(e),onAction:wx(e,"mceToggleVisualAid")})})(e)})(e),(e=>{(e=>{e.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:mB(e),onAction:wx(e,"outdent")}),e.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onSetup:bx(e),onAction:wx(e,"indent")})})(e)})(e),uB(e),(e=>{const t=Es(Gf(e)),o=()=>e.execCommand("mceTogglePlainTextPaste");e.ui.registry.addToggleButton("pastetext",{active:!1,icon:"paste-text",tooltip:"Paste as text",onAction:o,onSetup:gB(e,t)}),e.ui.registry.addToggleMenuItem("pastetext",{text:"Paste as text",icon:"paste-text",onAction:o,onSetup:gB(e,t)})})(e)},yB=e=>r(e)?e.split(/[ ,]/):e,xB=e=>t=>t.options.get(e),wB=xB("contextmenu_never_use_native"),SB=xB("contextmenu_avoid_overlap"),kB=e=>{const t=e.ui.registry.getAll().contextMenus,o=e.options.get("contextmenu");return e.options.isSet("contextmenu")?o:U(o,(e=>ve(t,e)))},CB=(e,t)=>({type:"makeshift",x:e,y:t}),OB=e=>"longpress"===e.type||0===e.type.indexOf("touch"),_B=(e,t)=>"contextmenu"===t.type||"longpress"===t.type?e.inline?(e=>{if(OB(e)){const t=e.touches[0];return CB(t.pageX,t.pageY)}return CB(e.pageX,e.pageY)})(t):((e,t)=>{const o=rf.DOM.getPos(e);return((e,t,o)=>CB(e.x+t,e.y+o))(t,o.x,o.y)})(e.getContentAreaContainer(),(e=>{if(OB(e)){const t=e.touches[0];return CB(t.clientX,t.clientY)}return CB(e.clientX,e.clientY)})(t)):TB(e),TB=e=>({type:"selection",root:Ve(e.selection.getNode())}),EB=(e,t,o)=>{switch(o){case"node":return(e=>({type:"node",node:A.some(Ve(e.selection.getNode())),root:Ve(e.getBody())}))(e);case"point":return _B(e,t);case"selection":return TB(e)}},AB=(e,t,o,n,s,r)=>{const a=o(),i=EB(e,t,r);CO(a,pb.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!1,search:A.none()}).map((e=>{t.preventDefault(),Ph.showMenuAt(s,{anchor:i},{menu:{markers:Ab("normal")},data:e})}))},MB={onLtr:()=>[cl,sl,rl,al,il,ll,lT,cT,iT,rT,aT,sT],onRtl:()=>[cl,rl,sl,il,al,ll,lT,cT,aT,sT,iT,rT]},DB={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},BB=(e,t,o,n,s,r)=>{const a=Do(),i=a.os.isiOS(),l=a.os.isMacOS(),c=a.os.isAndroid(),d=a.deviceType.isTouch(),u=()=>{const a=o();((e,t,o,n,s,r,a)=>{const i=((e,t,o)=>{const n=EB(e,t,o);return{bubble:gc(0,"point"===o?12:0,DB),layouts:MB,overrides:{maxWidthFunction:VA(),maxHeightFunction:cc()},...n}})(e,t,r);CO(o,pb.CLOSE_ON_EXECUTE,n,{isHorizontalMenu:!0,search:A.none()}).map((o=>{t.preventDefault();const l=a?zh.HighlightMenuAndItem:zh.HighlightNone;Ph.showMenuWithinBounds(s,{anchor:i},{menu:{markers:Ab("normal"),highlightOnOpen:l},data:o,type:"horizontal"},(()=>A.some(KD(e,n.shared,"node"===r?"node":"selection")))),e.dispatch(GD)}))})(e,t,a,n,s,r,!(c||i||l&&d))};if((l||i)&&"node"!==r){const o=()=>{(e=>{const t=e.selection.getRng(),o=()=>{Uh.setEditorTimeout(e,(()=>{e.selection.setRng(t)}),10),r()};e.once("touchend",o);const n=e=>{e.preventDefault(),e.stopImmediatePropagation()};e.on("mousedown",n,!0);const s=()=>r();e.once("longpresscancel",s);const r=()=>{e.off("touchend",o),e.off("longpresscancel",s),e.off("mousedown",n)}})(e),u()};((e,t)=>{const o=e.selection;if(o.isCollapsed()||t.touches.length<1)return!1;{const n=t.touches[0],s=o.getRng();return Jc(e.getWin(),Lc.domRange(s)).exists((e=>e.left<=n.clientX&&e.right>=n.clientX&&e.top<=n.clientY&&e.bottom>=n.clientY))}})(e,t)?o():(e.once("selectionchange",o),e.once("touchend",(()=>e.off("selectionchange",o))))}else u()},FB=e=>r(e)?"|"===e:"separator"===e.type,IB={type:"separator"},RB=e=>{const t=e=>({text:e.text,icon:e.icon,enabled:e.enabled,shortcut:e.shortcut});if(r(e))return e;switch(e.type){case"separator":return IB;case"submenu":return{type:"nestedmenuitem",...t(e),getSubmenuItems:()=>{const t=e.getSubmenuItems();return r(t)?t:H(t,RB)}};default:const o=e;return{type:"menuitem",...t(o),onAction:v(o.onAction)}}},NB=(e,t)=>{if(0===t.length)return e;const o=ne(e).filter((e=>!FB(e))).fold((()=>[]),(e=>[IB]));return e.concat(o).concat(t).concat([IB])},VB=(e,t)=>!(e=>"longpress"===e.type||ve(e,"touches"))(t)&&(2!==t.button||t.target===e.getBody()&&""===t.pointerType),zB=(e,t)=>VB(e,t)?e.selection.getStart(!0):t.target,HB=(e,t,o)=>{const n=Do().deviceType.isTouch,s=ri(Ph.sketch({dom:{tag:"div"},lazySink:t,onEscape:()=>e.focus(),onShow:()=>o.setContextMenuState(!0),onHide:()=>o.setContextMenuState(!1),fireDismissalEventInstead:{},inlineBehaviours:kl([Jp("dismissContextMenu",[Ur(Cr(),((t,o)=>{Xd.close(t),e.focus()}))])])})),a=()=>Ph.hide(s),i=t=>{if(wB(e)&&t.preventDefault(),((e,t)=>t.ctrlKey&&!wB(e))(e,t)||(e=>0===kB(e).length)(e))return;const a=((e,t)=>{const o=SB(e),n=VB(e,t)?"selection":"point";if(De(o)){const s=zB(e,t);return $w(Ve(s),o)?"node":n}return n})(e,t);(n()?BB:AB)(e,t,(()=>{const o=zB(e,t),n=e.ui.registry.getAll(),s=kB(e);return((e,t,o)=>{const n=j(t,((t,n)=>be(e,n.toLowerCase()).map((e=>{const n=e.update(o);if(r(n)&&De(Me(n)))return NB(t,n.split(" "));if(l(n)&&n.length>0){const e=H(n,RB);return NB(t,e)}return t})).getOrThunk((()=>t.concat([n])))),[]);return n.length>0&&FB(n[n.length-1])&&n.pop(),n})(n.contextMenus,s,o)}),o,s,a)};e.on("init",(()=>{const t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(n()?"":" ResizeWindow");e.on(t,a),e.on("longpress contextmenu",i)}))},LB=As([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),PB=e=>t=>t.translate(-e.left,-e.top),UB=e=>t=>t.translate(e.left,e.top),WB=e=>(t,o)=>j(e,((e,t)=>t(e)),$t(t,o)),jB=(e,t,o)=>e.fold(WB([UB(o),PB(t)]),WB([PB(t)]),WB([])),GB=(e,t,o)=>e.fold(WB([UB(o)]),WB([]),WB([UB(t)])),$B=(e,t,o)=>e.fold(WB([]),WB([PB(o)]),WB([UB(t),PB(o)])),qB=(e,t,o)=>{const n=e.fold(((e,t)=>({position:A.some("absolute"),left:A.some(e+"px"),top:A.some(t+"px")})),((e,t)=>({position:A.some("absolute"),left:A.some(e-o.left+"px"),top:A.some(t-o.top+"px")})),((e,t)=>({position:A.some("fixed"),left:A.some(e+"px"),top:A.some(t+"px")})));return{right:A.none(),bottom:A.none(),...n}},XB=(e,t,o,n)=>{const s=(e,s)=>(r,a)=>{const i=e(t,o,n);return s(r.getOr(i.left),a.getOr(i.top))};return e.fold(s($B,YB),s(GB,KB),s(jB,JB))},YB=LB.offset,KB=LB.absolute,JB=LB.fixed,ZB=(e,t)=>{const o=Ot(e,t);return u(o)?NaN:parseInt(o,10)},QB=(e,t,o,n,s,r)=>{const a=((e,t,o,n)=>((e,t)=>{const o=e.element,n=ZB(o,t.leftAttr),s=ZB(o,t.topAttr);return isNaN(n)||isNaN(s)?A.none():A.some($t(n,s))})(e,t).fold((()=>o),(e=>JB(e.left+n.left,e.top+n.top))))(e,t,o,n),i=t.mustSnap?tF(e,t,a,s,r):oF(e,t,a,s,r),l=jB(a,s,r);return((e,t,o)=>{const n=e.element;kt(n,t.leftAttr,o.left+"px"),kt(n,t.topAttr,o.top+"px")})(e,t,l),i.fold((()=>({coord:JB(l.left,l.top),extra:A.none()})),(e=>({coord:e.output,extra:e.extra})))},eF=(e,t,o,n)=>re(e,(e=>{const s=e.sensor,r=((e,t,o,n,s,r)=>{const a=GB(e,s,r),i=GB(t,s,r);return Math.abs(a.left-i.left)<=o&&Math.abs(a.top-i.top)<=n})(t,s,e.range.left,e.range.top,o,n);return r?A.some({output:XB(e.output,t,o,n),extra:e.extra}):A.none()})),tF=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return eF(r,o,n,s).orThunk((()=>{const e=j(r,((e,t)=>{const r=t.sensor,a=((e,t,o,n,s,r)=>{const a=GB(e,s,r),i=GB(t,s,r),l=Math.abs(a.left-i.left),c=Math.abs(a.top-i.top);return $t(l,c)})(o,r,t.range.left,t.range.top,n,s);return e.deltas.fold((()=>({deltas:A.some(a),snap:A.some(t)})),(o=>(a.left+a.top)/2<=(o.left+o.top)/2?{deltas:A.some(a),snap:A.some(t)}:e))}),{deltas:A.none(),snap:A.none()});return e.snap.map((e=>({output:XB(e.output,o,n,s),extra:e.extra})))}))},oF=(e,t,o,n,s)=>{const r=t.getSnapPoints(e);return eF(r,o,n,s)};var nF=Object.freeze({__proto__:null,snapTo:(e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const t=et(e.element),o=Uo(t),r=wE(s),a=((e,t,o)=>({coord:XB(e.output,e.output,t,o),extra:e.extra}))(n,o,r),i=qB(a.coord,0,r);Ft(s,i)}}});const sF="data-initial-z-index",rF=(e,t)=>{e.getSystem().addToGui(t),(e=>{st(e.element).filter(Ge).each((t=>{Nt(t,"z-index").each((e=>{kt(t,sF,e)})),Dt(t,"z-index",It(e.element,"z-index"))}))})(t)},aF=e=>{(e=>{st(e.element).filter(Ge).each((e=>{_t(e,sF).fold((()=>Ht(e,"z-index")),(t=>Dt(e,"z-index",t))),Et(e,sF)}))})(e),e.getSystem().removeFromGui(e)},iF=(e,t,o)=>e.getSystem().build(oS.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:o}));var lF=vs("snaps",[os("getSnapPoints"),Di("onSensor"),os("leftAttr"),os("topAttr"),ys("lazyViewport",en),ys("mustSnap",!1)]);const cF=[ys("useFixed",T),os("blockerClass"),ys("getTarget",w),ys("onDrag",b),ys("repositionTarget",!0),ys("onDrop",b),Os("getBounds",en),lF],dF=e=>{return(t=Nt(e,"left"),o=Nt(e,"top"),n=Nt(e,"position"),t.isSome()&&o.isSome()&&n.isSome()?A.some(((e,t,o)=>("fixed"===o?JB:YB)(parseInt(e,10),parseInt(t,10)))(t.getOrDie(),o.getOrDie(),n.getOrDie())):A.none()).getOrThunk((()=>{const t=Xt(e);return KB(t.left,t.top)}));var t,o,n},uF=(e,t)=>({bounds:e.getBounds(),height:jt(t.element),width:Zt(t.element)}),mF=(e,t,o,n,s)=>{const r=o.update(n,s),a=o.getStartData().getOrThunk((()=>uF(t,e)));r.each((o=>{((e,t,o,n)=>{const s=t.getTarget(e.element);if(t.repositionTarget){const r=et(e.element),a=Uo(r),i=wE(s),l=dF(s),c=((e,t,o,n,s,r,a)=>((e,t,o,n,s)=>{const r=s.bounds,a=GB(t,o,n),i=Yi(a.left,r.x,r.x+r.width-s.width),l=Yi(a.top,r.y,r.y+r.height-s.height),c=KB(i,l);return t.fold((()=>{const e=$B(c,o,n);return YB(e.left,e.top)}),x(c),(()=>{const e=jB(c,o,n);return JB(e.left,e.top)}))})(0,t.fold((()=>{const e=(t=o,a=r.left,i=r.top,t.fold(((e,t)=>YB(e+a,t+i)),((e,t)=>KB(e+a,t+i)),((e,t)=>JB(e+a,t+i))));var t,a,i;const l=jB(e,n,s);return JB(l.left,l.top)}),(t=>{const a=QB(e,t,o,r,n,s);return a.extra.each((o=>{t.onSensor(e,o)})),a.coord})),n,s,a))(e,t.snaps,l,a,i,n,o),d=qB(c,0,i);Ft(s,d)}t.onDrag(e,s,n)})(e,t,a,o)}))},gF=(e,t,o,n)=>{t.each(aF),o.snaps.each((t=>{((e,t)=>{((e,t)=>{const o=e.element;Et(o,t.leftAttr),Et(o,t.topAttr)})(e,t)})(e,t)}));const s=o.getTarget(e.element);n.reset(),o.onDrop(e,s)},pF=e=>(t,o)=>{const n=e=>{o.setStartData(uF(t,e))};return Hr([Ur(xr(),(e=>{o.getStartData().each((()=>n(e)))})),...e(t,o,n)])};var hF=Object.freeze({__proto__:null,getData:e=>A.from($t(e.x,e.y)),getDelta:(e,t)=>$t(t.left-e.left,t.top-e.top)});const fF=(e,t,o)=>[Ur(Ws(),((n,s)=>{if(0!==s.event.raw.button)return;s.stop();const r=()=>gF(n,A.some(l),e,t),a=qw(r,200),i={drop:r,delayDrop:a.schedule,forceDrop:r,move:o=>{a.cancel(),mF(n,e,t,hF,o)}},l=iF(n,e.blockerClass,(e=>Hr([Ur(Ws(),e.forceDrop),Ur($s(),e.drop),Ur(js(),((t,o)=>{e.move(o.event)})),Ur(Gs(),e.delayDrop)]))(i));o(n),rF(n,l)}))],bF=[...cF,Ri("dragger",{handlers:pF(fF)})];var vF=Object.freeze({__proto__:null,getData:e=>{const t=e.raw.touches;return 1===t.length?(e=>{const t=e[0];return A.some($t(t.clientX,t.clientY))})(t):A.none()},getDelta:(e,t)=>$t(t.left-e.left,t.top-e.top)});const yF=(e,t,o)=>{const n=Ql(),s=o=>{gF(o,n.get(),e,t),n.clear()};return[Ur(Hs(),((r,a)=>{a.stop();const i=()=>s(r),l={drop:i,delayDrop:b,forceDrop:i,move:o=>{mF(r,e,t,vF,o)}},c=iF(r,e.blockerClass,(e=>Hr([Ur(Hs(),e.forceDrop),Ur(Ps(),e.drop),Ur(Us(),e.drop),Ur(Ls(),((t,o)=>{e.move(o.event)}))]))(l));n.set(c),o(r),rF(r,c)})),Ur(Ls(),((o,n)=>{n.stop(),mF(o,e,t,vF,n.event)})),Ur(Ps(),((e,t)=>{t.stop(),s(e)})),Ur(Us(),s)]},xF=bF,wF=[...cF,Ri("dragger",{handlers:pF(yF)})],SF=[...cF,Ri("dragger",{handlers:pF(((e,t,o)=>[...fF(e,t,o),...yF(e,t,o)]))})];var kF=Object.freeze({__proto__:null,mouse:xF,touch:wF,mouseOrTouch:SF}),CF=Object.freeze({__proto__:null,init:()=>{let e=A.none(),t=A.none();const o=x({});return _a({readState:o,reset:()=>{e=A.none(),t=A.none()},update:(t,o)=>t.getData(o).bind((o=>((t,o)=>{const n=e.map((e=>t.getDelta(e,o)));return e=A.some(o),n})(t,o))),getStartData:()=>t,setStartData:e=>{t=A.some(e)}})}});const OF=Tl({branchKey:"mode",branches:kF,name:"dragging",active:{events:(e,t)=>e.dragger.handlers(e,t)},extra:{snap:e=>({sensor:e.sensor,range:e.range,output:e.output,extra:A.from(e.extra)})},state:CF,apis:nF}),_F=(e,t,o,n,s,r)=>e.fold((()=>OF.snap({sensor:KB(o-20,n-20),range:$t(s,r),output:KB(A.some(o),A.some(n)),extra:{td:t}})),(e=>{const s=o-20,r=n-20,a=e.element.dom.getBoundingClientRect();return OF.snap({sensor:KB(s,r),range:$t(40,40),output:KB(A.some(o-a.width/2),A.some(n-a.height/2)),extra:{td:t}})})),TF=(e,t,o)=>({getSnapPoints:e,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:(e,n)=>{const s=n.td;((e,t)=>e.exists((e=>Ze(e,t))))(t.get(),s)||(t.set(s),o(s))},mustSnap:!0}),EF=e=>jh(Wh.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:kl([OF.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:e}),IS.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}})),AF=(e,t)=>{const o=Es([]),n=Es([]),s=Es(!1),r=Ql(),a=Ql(),i=e=>{const o=Zo(e);return _F(u.getOpt(t),e,o.x,o.y,o.width,o.height)},l=e=>{const o=Zo(e);return _F(m.getOpt(t),e,o.right,o.bottom,o.width,o.height)},c=TF((()=>H(o.get(),(e=>i(e)))),r,(t=>{a.get().each((o=>{e.dispatch("TableSelectorChange",{start:t,finish:o})}))})),d=TF((()=>H(n.get(),(e=>l(e)))),a,(t=>{r.get().each((o=>{e.dispatch("TableSelectorChange",{start:o,finish:t})}))})),u=EF(c),m=EF(d),g=ri(u.asSpec()),p=ri(m.asSpec()),h=(t,o,n,s)=>{const r=n(o);OF.snapTo(t,r),((t,o,n,r)=>{const a=o.dom.getBoundingClientRect();Ht(t.element,"display");const i=nt(Ve(e.getBody())).dom.innerHeight,l=a[s]<0,c=((e,t)=>e[s]>t)(a,i);(l||c)&&Dt(t.element,"display","none")})(t,o)},f=e=>h(g,e,i,"top"),b=e=>h(p,e,l,"bottom");Do().deviceType.isTouch()&&(e.on("TableSelectionChange",(e=>{s.get()||(Ad(t,g),Ad(t,p),s.set(!0)),r.set(e.start),a.set(e.finish),e.otherCells.each((t=>{o.set(t.upOrLeftCells),n.set(t.downOrRightCells),f(e.start),b(e.finish)}))})),e.on("ResizeEditor ResizeWindow ScrollContent",(()=>{r.get().each(f),a.get().each(b)})),e.on("TableSelectionClear",(()=>{s.get()&&(Bd(g),Bd(p),s.set(!1)),r.clear(),a.clear()})))},MF=(e,t,o)=>{var n;const s=null!==(n=t.delimiter)&&void 0!==n?n:"\u203a";return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:kl([Pp.config({mode:"flow",selector:"div[role=button]"}),Rm.config({disabled:o.isDisabled}),Sy(),cS.config({}),Kp.config({}),Jp("elementPathEvents",[Kr(((t,n)=>{e.shortcuts.add("alt+F11","focus statusbar elementpath",(()=>Pp.focusIn(t))),e.on("NodeChange",(n=>{const r=(t=>{const o=[];let n=t.length;for(;n-- >0;){const r=t[n];if(1===r.nodeType&&"BR"!==(s=r).nodeName&&!s.getAttribute("data-mce-bogus")&&"bookmark"!==s.getAttribute("data-mce-type")){const t=hx(e,r);if(t.isDefaultPrevented()||o.push({name:t.name,element:r}),t.isPropagationStopped())break}}var s;return o})(n.parents),a=r.length>0?j(r,((t,n,r)=>{const a=((t,n,s)=>Wh.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{"data-index":s,"aria-level":s+1}},components:[ti(t)],action:t=>{e.focus(),e.selection.select(n),e.nodeChanged()},buttonBehaviours:kl([ky(o.isDisabled),Sy()])}))(n.name,n.element,r);return 0===r?t.concat([a]):t.concat([{dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0}},components:[ti(` ${s} `)]},a])}),[]):[];Kp.set(t,a)}))}))])]),components:[]}};var DF;!function(e){e[e.None=0]="None",e[e.Both=1]="Both",e[e.Vertical=2]="Vertical"}(DF||(DF={}));const BF=(e,t,o)=>{const n=Ve(e.getContainer()),s=((e,t,o,n,s)=>{const r={height:zD(n+t.top,ff(e),vf(e))};return o===DF.Both&&(r.width=zD(s+t.left,hf(e),bf(e))),r})(e,t,o,Wt(n),Jt(n));le(s,((e,t)=>{h(e)&&Dt(n,t,VD(e))})),(e=>{e.dispatch("ResizeEditor")})(e)},FF=(e,t,o,n)=>{const s=$t(20*o,20*n);return BF(e,s,t),A.some(!0)},IF=(e,t)=>{const o=()=>{const o=[],n=Xf(e),s=Uf(e),r=Wf(e)||e.hasPlugin("wordcount");return s&&o.push(MF(e,{},t)),n&&o.push((()=>{const e=Iy("Alt+0");return{dom:{tag:"div",classes:["tox-statusbar__help-text"]},components:[ti(Gh.translate(["Press {0} for help",e]))]}})()),r&&o.push((()=>{const o=[];return e.hasPlugin("wordcount")&&o.push(((e,t)=>{const o=(e,o,n)=>Kp.set(e,[ti(t.translate(["{0} "+n,o[n]]))]);return Wh.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:kl([ky(t.isDisabled),Sy(),cS.config({}),Kp.config({}),pu.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Jp("wordcount-events",[Qr((e=>{const t=pu.getValue(e),n="words"===t.mode?"characters":"words";pu.setValue(e,{mode:n,count:t.count}),o(e,t.count,n)})),Kr((t=>{e.on("wordCountUpdate",(e=>{const{mode:n}=pu.getValue(t);pu.setValue(t,{mode:n,count:e.wordCount}),o(t,e.wordCount,n)}))}))])]),eventOrder:{[ur()]:["disabling","alloy.base.behaviour","wordcount-events"]}})})(e,t)),Wf(e)&&o.push({dom:{tag:"span",classes:["tox-statusbar__branding"]},components:[{dom:{tag:"a",attributes:{href:"https://www.tiny.cloud/powered-by-tiny?utm_campaign=editor_referral&utm_medium=poweredby&utm_source=tinymce&utm_content=v6",rel:"noopener",target:"_blank","aria-label":Gh.translate(["Powered by {0}","Tiny"])},innerHtml:'<svg width="50px" height="16px" viewBox="0 0 50 16" xmlns="http://www.w3.org/2000/svg">\n  <path fill-rule="evenodd" clip-rule="evenodd" d="M10.143 0c2.608.015 5.186 2.178 5.186 5.331 0 0 .077 3.812-.084 4.87-.361 2.41-2.164 4.074-4.65 4.496-1.453.284-2.523.49-3.212.623-.373.071-.634.122-.785.152-.184.038-.997.145-1.35.145-2.732 0-5.21-2.04-5.248-5.33 0 0 0-3.514.03-4.442.093-2.4 1.758-4.342 4.926-4.963 0 0 3.875-.752 4.036-.782.368-.07.775-.1 1.15-.1Zm1.826 2.8L5.83 3.989v2.393l-2.455.475v5.968l6.137-1.189V9.243l2.456-.476V2.8ZM5.83 6.382l3.682-.713v3.574l-3.682.713V6.382Zm27.173-1.64-.084-1.066h-2.226v9.132h2.456V7.743c-.008-1.151.998-2.064 2.149-2.072 1.15-.008 1.987.92 1.995 2.072v5.065h2.455V7.359c-.015-2.18-1.657-3.929-3.837-3.913a3.993 3.993 0 0 0-2.908 1.296Zm-6.3-4.266L29.16 0v2.387l-2.456.475V.476Zm0 3.2v9.132h2.456V3.676h-2.456Zm18.179 11.787L49.11 3.676H46.58l-1.612 4.527-.46 1.382-.384-1.382-1.611-4.527H39.98l3.3 9.132L42.15 16l2.732-.537ZM22.867 9.738c0 .752.568 1.075.921 1.075.353 0 .668-.047.998-.154l.537 1.765c-.23.154-.92.537-2.225.537-1.305 0-2.655-.997-2.686-2.686a136.877 136.877 0 0 1 0-4.374H18.8V3.676h1.612v-1.98l2.455-.476v2.456h2.302V5.9h-2.302v3.837Z"/>\n</svg>\n'.trim()},behaviours:kl([oh.config({})])}]}),{dom:{tag:"div",classes:["tox-statusbar__right-container"]},components:o}})()),o.length>0?[{dom:{tag:"div",classes:["tox-statusbar__text-container",...(()=>{const e="tox-statusbar__text-container--flex-start",t="tox-statusbar__text-container--flex-end";if(n){const o="tox-statusbar__text-container-3-cols";return r||s?r&&!s?[o,t]:[o,e]:[o,"tox-statusbar__text-container--space-around"]}return[r&&!s?t:e]})()]},components:o}]:[]};return{dom:{tag:"div",classes:["tox-statusbar"]},components:(()=>{const n=o(),s=((e,t)=>{const o=(e=>{const t=jf(e);return!1===t?DF.None:"both"===t?DF.Both:DF.Vertical})(e);if(o===DF.None)return A.none();const n=o===DF.Both?"Press the arrow keys to resize the editor.":"Press the Up and Down arrow keys to resize the editor.";return A.some(ef("resize-handle",{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize"),"aria-label":t.translate(n)},behaviours:[OF.config({mode:"mouse",repositionTarget:!1,onDrag:(t,n,s)=>BF(e,s,o),blockerClass:"tox-blocker"}),Pp.config({mode:"special",onLeft:()=>FF(e,o,-1,0),onRight:()=>FF(e,o,1,0),onUp:()=>FF(e,o,0,-1),onDown:()=>FF(e,o,0,1)}),cS.config({}),oh.config({})]},t.icons))})(e,t);return n.concat(s.toArray())})()}},RF=(e,t)=>t.get().getOrDie(`UI for ${e} has not been rendered`),NF=(e,t)=>{const o=e.inline,n=o?jD:RD,s=ib(e)?oA:yE,r=(()=>{const e=Ql(),t=Ql(),o=Ql();return{dialogUi:e,popupUi:t,mainUi:o,getUiMotherships:()=>{const o=e.get().map((e=>e.mothership)),n=t.get().map((e=>e.mothership));return o.fold((()=>n.toArray()),(e=>n.fold((()=>[e]),(t=>Ze(e.element,t.element)?[e]:[e,t]))))},lazyGetInOuterOrDie:(e,t)=>()=>o.get().bind((e=>t(e.outerContainer))).getOrDie(`Could not find ${e} element in OuterContainer`)}})(),a=Ql(),i=Ql(),l=Ql(),c=Do().deviceType.isTouch()?["tox-platform-touch"]:[],d=ob(e),u=Cf(e),m=jh({dom:{tag:"div",classes:["tox-anchorbar"]}}),g=jh({dom:{tag:"div",classes:["tox-bottom-anchorbar"]}}),p=()=>r.mainUi.get().map((e=>e.outerContainer)).bind(IM.getHeader),h=r.lazyGetInOuterOrDie("anchor bar",m.getOpt),f=r.lazyGetInOuterOrDie("bottom anchor bar",g.getOpt),b=r.lazyGetInOuterOrDie("toolbar",IM.getToolbar),v=r.lazyGetInOuterOrDie("throbber",IM.getThrobber),y=((e,t,o,n)=>{const s=Es(!1),r=(e=>{const t=Es(ob(e)?"bottom":"top");return{isPositionedAtTop:()=>"top"===t.get(),getDockingMode:t.get,setDockingMode:t.set}})(t),a={icons:()=>t.ui.registry.getAll().icons,menuItems:()=>t.ui.registry.getAll().menuItems,translate:Gh.translate,isDisabled:()=>t.mode.isReadOnly()||!t.ui.isEnabled(),getOption:t.options.get},i=rE(t),l=(e=>{const t=t=>()=>e.formatter.match(t),o=t=>()=>{const o=e.formatter.get(t);return void 0!==o?A.some({tag:o.length>0&&(o[0].inline||o[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(t))}):A.none()},n=Es([]),s=Es([]),r=Es(!1);return e.on("PreInit",(s=>{const r=BT(e),a=IT(e,r,t,o);n.set(a)})),e.on("addStyleModifications",(n=>{const a=IT(e,n.items,t,o);s.set(a),r.set(n.replace)})),{getData:()=>{const e=r.get()?[]:n.get(),t=s.get();return e.concat(t)}}})(t),c=(e=>({colorPicker:kT(e),hasCustomColors:CT(e),getColors:OT(e),getColorCols:_T(e)}))(t),d=(e=>({isDraggableModal:TT(e)}))(t),u={shared:{providers:a,anchors:ST(t,o,n,r.isPositionedAtTop),header:r},urlinput:i,styles:l,colorinput:c,dialog:d,isContextMenuOpen:()=>s.get(),setContextMenuState:e=>s.set(e)},m={...u,shared:{...u.shared,interpreter:e=>K_(e,{},m),getSink:e.popup}},g={...u,shared:{...u.shared,interpreter:e=>K_(e,{},g),getSink:e.dialog}};return{popup:m,dialog:g}})({popup:()=>sn.fromOption(r.popupUi.get().map((e=>e.sink)),"(popup) UI has not been rendered"),dialog:()=>sn.fromOption(r.dialogUi.get().map((e=>e.sink)),"UI has not been rendered")},e,h,f),x=()=>{const t=(()=>{const t={attributes:{[yc]:d?vc.BottomToTop:vc.TopToBottom}},o=IM.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:y.popup,onEscape:()=>{e.focus()}}),n=IM.parts.toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:y.popup.shared.getSink,providers:y.popup.shared.providers,onEscape:()=>{e.focus()},onToolbarToggled:t=>{((e,t)=>{e.dispatch("ToggleToolbarDrawer",{state:t})})(e,t)},type:u,lazyToolbar:b,lazyHeader:()=>p().getOrDie("Could not find header element"),...t}),s=IM.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:y.popup.shared.providers,onEscape:()=>{e.focus()},type:u}),r=tb(e),a=Qf(e),i=Kf(e),l=qf(e),c=IM.parts.promotion({dom:{tag:"div",classes:["tox-promotion"]}}),g=r||a||i,h=l?[c,o]:[o];return IM.parts.header({dom:{tag:"div",classes:["tox-editor-header"].concat(g?[]:["tox-editor-header--empty"]),...t},components:q([i?h:[],r?[s]:a?[n]:[],sb(e)?[]:[m.asSpec()]]),sticky:ib(e),editor:e,sharedBackstage:y.popup.shared})})(),n={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[IM.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),IM.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}})]},s=IM.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:y.popup}),r=IM.parts.viewWrapper({backstage:y.popup}),i=Pf(e)&&!o?A.some(IF(e,y.popup.shared.providers)):A.none(),l=q([d?[]:[t],o?[]:[n],d?[t]:[]]),h=IM.parts.editorContainer({components:q([l,o?[]:[g.asSpec(),...i.toArray()]])}),f=ab(e),v={role:"application",...Gh.isRtl()?{dir:"rtl"}:{},...f?{"aria-hidden":"true"}:{}},x=ri(IM.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(o?["tox-tinymce-inline"]:[]).concat(d?["tox-tinymce--toolbar-bottom"]:[]).concat(c),styles:{visibility:"hidden",...f?{opacity:"0",border:"0"}:{}},attributes:v},components:[h,...o?[]:[r],s],behaviours:kl([Sy(),Rm.config({disableClass:"tox-tinymce--disabled"}),Pp.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),w=nS(x);return a.set(w),{mothership:w,outerContainer:x}},w=t=>{const o=VD((e=>{const t=(e=>{const t=gf(e),o=ff(e),n=vf(e);return ND(t).map((e=>zD(e,o,n)))})(e);return t.getOr(gf(e))})(e)),n=VD((e=>HD(e).getOr(pf(e)))(e));return e.inline||(zt("div","width",n)&&Dt(t.element,"width",n),zt("div","height",o)?Dt(t.element,"height",o):Dt(t.element,"height","400px")),o};return{popups:{backstage:y.popup,getMothership:()=>RF("popups",l)},dialogs:{backstage:y.dialog,getMothership:()=>RF("dialogs",i)},renderUI:()=>{const o=x(),a=(()=>{const t=rb(e),o=Ze(xt(),t)&&"grid"===It(t,"display"),n={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(c),attributes:{...Gh.isRtl()?{dir:"rtl"}:{}}},behaviours:kl([Sd.config({useFixed:()=>s.isDocked(p)})])},r={dom:{styles:{width:document.body.clientWidth+"px"}},events:Hr([Ur(wr(),(e=>{Dt(e.element,"width",document.body.clientWidth+"px")}))])},a=ri(fn(n,o?r:{})),l=nS(a);return i.set(l),{sink:a,mothership:l}})(),d=lb(e)?(()=>{const e={dom:{tag:"div",classes:["tox","tox-silver-sink","tox-silver-popup-sink","tox-tinymce-aux"].concat(c),attributes:{...Gh.isRtl()?{dir:"rtl"}:{}}},behaviours:kl([Sd.config({useFixed:()=>s.isDocked(p),getBounds:()=>t.getPopupSinkBounds()})])},o=ri(e),n=nS(o);return l.set(n),{sink:o,mothership:n}})():(e=>(l.set(e.mothership),e))(a);r.dialogUi.set(a),r.popupUi.set(d),r.mainUi.set(o);return(t=>{const{mainUi:o,popupUi:r,uiMotherships:a}=t;ce(Of(e),((t,o)=>{e.ui.registry.addGroupToolbarButton(o,t)}));const{buttons:i,menuItems:l,contextToolbars:c,sidebars:d,views:m}=e.ui.registry.getAll(),g=eb(e),h={menuItems:l,menus:cb(e),menubar:Df(e),toolbar:g.getOrThunk((()=>Bf(e))),allowToolbarGroups:u===nf.floating,buttons:i,sidebar:d,views:m};var f;f=o.outerContainer,e.addShortcut("alt+F9","focus menubar",(()=>{IM.focusMenubar(f)})),e.addShortcut("alt+F10","focus toolbar",(()=>{IM.focusToolbar(f)})),e.addCommand("ToggleToolbarDrawer",((e,t)=>{(null==t?void 0:t.skipFocus)?IM.toggleToolbarDrawerWithoutFocusing(f):IM.toggleToolbarDrawer(f)})),e.addQueryStateHandler("ToggleToolbarDrawer",(()=>IM.isToolbarDrawerToggled(f))),((e,t,o)=>{const n=(e,n)=>{L([t,...o],(t=>{t.broadcastEvent(e,n)}))},s=(e,n)=>{L([t,...o],(t=>{t.broadcastOn([e],n)}))},r=e=>s(Yd(),{target:e.target}),a=$o(),i=tc(a,"touchstart",r),l=tc(a,"touchmove",(e=>n(vr(),e))),c=tc(a,"touchend",(e=>n(yr(),e))),d=tc(a,"mousedown",r),u=tc(a,"mouseup",(e=>{0===e.raw.button&&s(Jd(),{target:e.target})})),m=e=>s(Yd(),{target:Ve(e.target)}),g=e=>{0===e.button&&s(Jd(),{target:Ve(e.target)})},p=()=>{L(e.editorManager.get(),(t=>{e!==t&&t.dispatch("DismissPopups",{relatedTarget:e})}))},h=e=>n(xr(),nc(e)),f=e=>{s(Kd(),{}),n(wr(),nc(e))},b=ht(Ve(e.getElement())),v=oc(b,"scroll",(o=>{requestAnimationFrame((()=>{if(null!=e.getContainer()){const s=jw(e,t.element).map((e=>[e.element,...e.others])).getOr([]);N(s,(e=>Ze(e,o.target)))&&(e.dispatch("ElementScroll",{target:o.target.dom}),n(Er(),o))}}))})),y=()=>s(Kd(),{}),x=t=>{t.state&&s(Yd(),{target:Ve(e.getContainer())})},w=e=>{s(Yd(),{target:Ve(e.relatedTarget.getContainer())})};e.on("PostRender",(()=>{e.on("click",m),e.on("tap",m),e.on("mouseup",g),e.on("mousedown",p),e.on("ScrollWindow",h),e.on("ResizeWindow",f),e.on("ResizeEditor",y),e.on("AfterProgressState",x),e.on("DismissPopups",w)})),e.on("remove",(()=>{e.off("click",m),e.off("tap",m),e.off("mouseup",g),e.off("mousedown",p),e.off("ScrollWindow",h),e.off("ResizeWindow",f),e.off("ResizeEditor",y),e.off("AfterProgressState",x),e.off("DismissPopups",w),d.unbind(),i.unbind(),l.unbind(),c.unbind(),u.unbind(),v.unbind()})),e.on("detach",(()=>{L([t,...o],Vd),L([t,...o],(e=>e.destroy()))}))})(e,o.mothership,a),s.setup(e,y.popup.shared,p),vB(e,y.popup),HB(e,y.popup.shared.getSink,y.popup),(e=>{const{sidebars:t}=e.ui.registry.getAll();L(ae(t),(o=>{const n=t[o],s=()=>xe(A.from(e.queryCommandValue("ToggleSidebar")),o);e.ui.registry.addToggleButton(o,{icon:n.icon,tooltip:n.tooltip,onAction:t=>{e.execCommand("ToggleSidebar",!1,o),t.setActive(s())},onSetup:t=>{t.setActive(s());const o=()=>t.setActive(s());return e.on("ToggleSidebar",o),()=>{e.off("ToggleSidebar",o)}}})}))})(e),EA(e,v,y.popup.shared),cB(e,c,r.sink,{backstage:y.popup}),AF(e,r.sink);const b={targetNode:e.getElement(),height:w(o.outerContainer)};return n.render(e,t,h,y.popup,b)})({popupUi:d,dialogUi:a,mainUi:o,uiMotherships:r.getUiMotherships()})}}},VF=x([os("lazySink"),us("dragBlockClass"),Os("getBounds",en),ys("useTabstopAt",E),ys("firstTabstop",0),ys("eventOrder",{}),hu("modalBehaviours",[Pp]),Bi("onExecute"),Ii("onEscape")]),zF={sketch:w},HF=x([ju({name:"draghandle",overrides:(e,t)=>({behaviours:kl([OF.config({mode:"mouse",getTarget:e=>mi(e,'[role="dialog"]').getOr(e),blockerClass:e.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:e.getDragBounds})])})}),Uu({schema:[os("dom")],name:"title"}),Uu({factory:zF,schema:[os("dom")],name:"close"}),Uu({factory:zF,schema:[os("dom")],name:"body"}),ju({factory:zF,schema:[os("dom")],name:"footer"}),Wu({factory:{sketch:(e,t)=>({...e,dom:t.dom,components:t.components})},schema:[ys("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),ys("components",[])],name:"blocker"})]),LF=bm({name:"ModalDialog",configFields:VF(),partFields:HF(),factory:(e,t,o,n)=>{const s=Ql(),r=la("modal-events"),a={...e.eventOrder,[Sr()]:[r].concat(e.eventOrder["alloy.system.attached"]||[])};return{uid:e.uid,dom:e.dom,components:t,apis:{show:t=>{s.set(t);const o=e.lazySink(t).getOrDie(),r=n.blocker(),a=o.getSystem().build({...r,components:r.components.concat([ai(t)]),behaviours:kl([oh.config({}),Jp("dialog-blocker-events",[Yr(Xs(),(()=>{OA.isBlocked(t)||Pp.focusIn(t)}))])])});Ad(o,a),Pp.focusIn(t)},hide:e=>{s.clear(),st(e.element).each((t=>{e.getSystem().getByDom(t).each((e=>{Bd(e)}))}))},getBody:t=>nm(t,e,"body"),getFooter:t=>om(t,e,"footer"),setIdle:e=>{OA.unblock(e)},setBusy:(e,t)=>{OA.block(e,t)}},eventOrder:a,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:bu(e.modalBehaviours,[Kp.config({}),Pp.config({mode:"cyclic",onEnter:e.onExecute,onEscape:e.onEscape,useTabstopAt:e.useTabstopAt,firstTabstop:e.firstTabstop}),OA.config({getRoot:s.get}),Jp(r,[Kr((t=>{((e,t)=>{const o=_t(e,"id").fold((()=>{const e=la("dialog-label");return kt(t,"id",e),e}),w);kt(e,"aria-labelledby",o)})(t.element,nm(t,e,"title").element)}))])])}},apis:{show:(e,t)=>{e.show(t)},hide:(e,t)=>{e.hide(t)},getBody:(e,t)=>e.getBody(t),getFooter:(e,t)=>e.getFooter(t),setBusy:(e,t,o)=>{e.setBusy(t,o)},setIdle:(e,t)=>{e.setIdle(t)}}}),PF=Dn([tv,ov].concat(Jv)),UF=Ln,WF=[Ev("button"),hv,ks("align","end",["start","end"]),kv,Sv,hs("buttonType",["primary","secondary"])],jF=[...WF,sv],GF=[as("type",["submit","cancel","custom"]),...jF],$F=[as("type",["menu"]),pv,fv,hv,ds("items",PF),...WF],qF=[...WF,as("type",["togglebutton"]),rs("tooltip"),hv,pv,Cs("active",!1)],XF=Jn("type",{submit:GF,cancel:GF,custom:GF,menu:$F,togglebutton:qF}),YF=[tv,sv,as("level",["info","warn","error","success"]),av,ys("url","")],KF=Dn(YF),JF=[tv,sv,Sv,Ev("button"),hv,wv,hs("buttonType",["primary","secondary","toolbar"]),kv],ZF=Dn(JF),QF=[tv,ov],eI=QF.concat([bv]),tI=QF.concat([nv,Sv]),oI=Dn(tI),nI=Ln,sI=eI.concat([Cv("auto")]),rI=Dn(sI),aI=Rn([iv,sv,av]),iI=eI.concat([Ss("storageKey","default")]),lI=Dn(iI),cI=Hn,dI=Dn(eI),uI=Hn,mI=QF.concat([Ss("tag","textarea"),rs("scriptId"),rs("scriptUrl"),xs("settings",void 0,Wn)]),gI=QF.concat([Ss("tag","textarea"),is("init")]),pI=Gn((e=>qn("customeditor.old",Mn(gI),e).orThunk((()=>qn("customeditor.new",Mn(mI),e))))),hI=Hn,fI=Dn(eI),bI=Bn(On),vI=e=>[tv,ss("columns"),e],yI=[tv,rs("html"),ks("presets","presentation",["presentation","document"])],xI=Dn(yI),wI=eI.concat([Cs("border",!1),Cs("sandboxed",!0),Cs("streamContent",!1),Cs("transparent",!0)]),SI=Dn(wI),kI=Hn,CI=Dn(QF.concat([ps("height")])),OI=Dn([rs("url"),gs("zoom"),gs("cachedWidth"),gs("cachedHeight")]),_I=eI.concat([ps("inputMode"),ps("placeholder"),Cs("maximized",!1),Sv]),TI=Dn(_I),EI=Hn,AI=e=>[tv,nv,e,ks("align","start",["start","center","end"])],MI=[sv,iv],DI=[sv,ds("items",Zn(0,(()=>BI)))],BI=Fn([Dn(MI),Dn(DI)]),FI=eI.concat([ds("items",BI),Sv]),II=Dn(FI),RI=Hn,NI=eI.concat([cs("items",[sv,iv]),ws("size",1),Sv]),VI=Dn(NI),zI=Hn,HI=eI.concat([Cs("constrain",!0),Sv]),LI=Dn(HI),PI=Dn([rs("width"),rs("height")]),UI=QF.concat([nv,ws("min",0),ws("max",0)]),WI=Dn(UI),jI=zn,GI=[tv,ds("header",Hn),ds("cells",Bn(Hn))],$I=Dn(GI),qI=eI.concat([ps("placeholder"),Cs("maximized",!1),Sv]),XI=Dn(qI),YI=Hn,KI=[as("type",["directory","leaf"]),rv,rs("id"),ms("menu",rA)],JI=Dn(KI),ZI=KI.concat([ds("children",Zn(0,(()=>jn("type",{directory:QI,leaf:JI}))))]),QI=Dn(ZI),eR=jn("type",{directory:QI,leaf:JI}),tR=[tv,ds("items",eR),fs("onLeafAction"),fs("onToggleExpand"),_s("defaultExpandedIds",[],Hn),ps("defaultSelectedId")],oR=Dn(tR),nR=eI.concat([ks("filetype","file",["image","media","file"]),Sv]),sR=Dn(nR),rR=Dn([iv,Ov]),aR=e=>Qn("items","items",{tag:"required",process:{}},Bn(Gn((t=>qn(`Checking item of ${e}`,iR,t).fold((e=>sn.error(Kn(e))),(e=>sn.value(e))))))),iR=En((()=>{return jn("type",{alertbanner:KF,bar:Dn((e=aR("bar"),[tv,e])),button:ZF,checkbox:oI,colorinput:lI,colorpicker:dI,dropzone:fI,grid:Dn(vI(aR("grid"))),iframe:SI,input:TI,listbox:II,selectbox:VI,sizeinput:LI,slider:WI,textarea:XI,urlinput:sR,customeditor:pI,htmlpanel:xI,imagepreview:CI,collection:rI,label:Dn(AI(aR("label"))),table:$I,tree:oR,panel:cR});var e})),lR=[tv,ys("classes",[]),ds("items",iR)],cR=Dn(lR),dR=[Ev("tab"),rv,ds("items",iR)],uR=[tv,cs("tabs",dR)],mR=Dn(uR),gR=jF,pR=XF,hR=Dn([rs("title"),ns("body",jn("type",{panel:cR,tabpanel:mR})),Ss("size","normal"),_s("buttons",[],pR),ys("initialData",{}),Os("onAction",b),Os("onChange",b),Os("onSubmit",b),Os("onClose",b),Os("onCancel",b),Os("onTabChange",b)]),fR=Dn([as("type",["cancel","custom"]),...gR]),bR=Dn([rs("title"),rs("url"),gs("height"),gs("width"),bs("buttons",fR),Os("onAction",b),Os("onCancel",b),Os("onClose",b),Os("onMessage",b)]),vR=e=>a(e)?[e].concat(X(fe(e),vR)):l(e)?X(e,vR):[],yR=e=>r(e.type)&&r(e.name),xR={checkbox:nI,colorinput:cI,colorpicker:uI,dropzone:bI,input:EI,iframe:kI,imagepreview:OI,selectbox:zI,sizeinput:PI,slider:jI,listbox:RI,size:PI,textarea:YI,urlinput:rR,customeditor:hI,collection:aI,togglemenuitem:UF},wR=e=>{const t=(e=>U(vR(e),yR))(e),o=X(t,(e=>(e=>A.from(xR[e.type]))(e).fold((()=>[]),(t=>[ns(e.name,t)]))));return Dn(o)},SR=e=>{var t;return{internalDialog:Xn(qn("dialog",hR,e)),dataValidator:wR(e),initialData:null!==(t=e.initialData)&&void 0!==t?t:{}}},kR={open:(e,t)=>{const o=SR(t);return e(o.internalDialog,o.initialData,o.dataValidator)},openUrl:(e,t)=>e(Xn(qn("dialog",bR,t))),redial:e=>SR(e)};var CR=Object.freeze({__proto__:null,events:(e,t)=>{const o=(o,n)=>{e.updateState.each((e=>{const s=e(o,n);t.set(s)})),e.renderComponents.each((s=>{const r=s(n,t.get());(e.reuseDom?Wp:Up)(o,r)}))};return Hr([Ur(dr(),((t,n)=>{const s=n;if(!s.universal){const n=e.channel;R(s.channels,n)&&o(t,s.data)}})),Kr(((t,n)=>{e.initialData.each((e=>{o(t,e)}))}))])}}),OR=Object.freeze({__proto__:null,getState:(e,t,o)=>o}),_R=[os("channel"),us("renderComponents"),us("updateState"),us("initialData"),Cs("reuseDom",!0)];const TR=Ol({fields:_R,name:"reflecting",active:CR,apis:OR,state:Object.freeze({__proto__:null,init:()=>{const e=Es(A.none());return{readState:()=>e.get().getOr("none"),get:e.get,set:e.set,clear:()=>e.set(A.none())}}})}),ER=e=>{const t=[],o={};return le(e,((e,n)=>{e.fold((()=>{t.push(n)}),(e=>{o[n]=e}))})),t.length>0?sn.error(t):sn.value(o)},AR=(e,t,o)=>{const n=jh(CC.sketch((n=>({dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:H(e.items,(e=>X_(n,e,t,o)))}))));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[n.asSpec()]}],behaviours:kl([Pp.config({mode:"acyclic",useTabstopAt:C(XC)}),(s=n,wm.config({find:s.getOpt})),IC(n,{postprocess:e=>ER(e).fold((e=>(console.error(e),{})),w)}),Jp("dialog-body-panel",[Ur(Xs(),((e,t)=>{e.getSystem().broadcastOn([eO],{newFocus:A.some(t.event.target)})}))])])};var s},MR=fm({name:"TabButton",configFields:[ys("uid",void 0),os("value"),Qn("dom","dom",xn((()=>({attributes:{role:"tab",id:la("aria"),"aria-selected":"false"}}))),Nn()),us("action"),ys("domModification",{}),hu("tabButtonBehaviours",[oh,Pp,pu]),os("view")],factory:(e,t)=>({uid:e.uid,dom:e.dom,components:e.components,events:mh(e.action),behaviours:bu(e.tabButtonBehaviours,[oh.config({}),Pp.config({mode:"execution",useSpace:!0,useEnter:!0}),pu.config({store:{mode:"memory",initialValue:e.value}})]),domModification:e.domModification})}),DR=x([os("tabs"),os("dom"),ys("clickToDismiss",!1),hu("tabbarBehaviours",[Gm,Pp]),Ai(["tabClass","selectedClass"])]),BR=Gu({factory:MR,name:"tabs",unit:"tab",overrides:e=>{const t=(e,t)=>{Gm.dehighlight(e,t),Ir(e,Mr(),{tabbar:e,button:t})},o=(e,t)=>{Gm.highlight(e,t),Ir(e,Ar(),{tabbar:e,button:t})};return{action:n=>{const s=n.getSystem().getByUid(e.uid).getOrDie(),r=Gm.isHighlighted(s,n);(r&&e.clickToDismiss?t:r?b:o)(s,n)},domModification:{classes:[e.markers.tabClass]}}}}),FR=x([BR]),IR=bm({name:"Tabbar",configFields:DR(),partFields:FR(),factory:(e,t,o,n)=>({uid:e.uid,dom:e.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:bu(e.tabbarBehaviours,[Gm.config({highlightClass:e.markers.selectedClass,itemClass:e.markers.tabClass,onHighlight:(e,t)=>{kt(t.element,"aria-selected","true")},onDehighlight:(e,t)=>{kt(t.element,"aria-selected","false")}}),Pp.config({mode:"flow",getInitial:e=>Gm.getHighlighted(e).map((e=>e.element)),selector:"."+e.markers.tabClass,executeOnMove:!0})])})}),RR=fm({name:"Tabview",configFields:[hu("tabviewBehaviours",[Kp])],factory:(e,t)=>({uid:e.uid,dom:e.dom,behaviours:bu(e.tabviewBehaviours,[Kp.config({})]),domModification:{attributes:{role:"tabpanel"}}})}),NR=x([ys("selectFirst",!0),Di("onChangeTab"),Di("onDismissTab"),ys("tabs",[]),hu("tabSectionBehaviours",[])]),VR=Uu({factory:IR,schema:[os("dom"),ls("markers",[os("tabClass"),os("selectedClass")])],name:"tabbar",defaults:e=>({tabs:e.tabs})}),zR=Uu({factory:RR,name:"tabview"}),HR=x([VR,zR]),LR=bm({name:"TabSection",configFields:NR(),partFields:HR(),factory:(e,t,o,n)=>{const s=(t,o)=>{om(t,e,"tabbar").each((e=>{o(e).each(Rr)}))};return{uid:e.uid,dom:e.dom,components:t,behaviours:fu(e.tabSectionBehaviours),events:Hr(q([e.selectFirst?[Kr(((e,t)=>{s(e,Gm.getFirst)}))]:[],[Ur(Ar(),((t,o)=>{(t=>{const o=pu.getValue(t);om(t,e,"tabview").each((n=>{G(e.tabs,(e=>e.value===o)).each((o=>{const s=o.view();_t(t.element,"id").each((e=>{kt(n.element,"aria-labelledby",e)})),Kp.set(n,s),e.onChangeTab(n,t,s)}))}))})(o.event.button)})),Ur(Mr(),((t,o)=>{const n=o.event.button;e.onDismissTab(t,n)}))]])),apis:{getViewItems:t=>om(t,e,"tabview").map((e=>Kp.contents(e))).getOr([]),showTab:(e,t)=>{s(e,(e=>{const o=Gm.getCandidates(e);return G(o,(e=>pu.getValue(e)===t)).filter((t=>!Gm.isHighlighted(e,t)))}))}}}},apis:{getViewItems:(e,t)=>e.getViewItems(t),showTab:(e,t,o)=>{e.showTab(t,o)}}}),PR=(e,t)=>{Dt(e,"height",t+"px"),Dt(e,"flex-basis",t+"px")},UR=(e,t,o)=>{mi(e,'[role="dialog"]').each((e=>{pi(e,'[role="tablist"]').each((n=>{o.get().map((o=>(Dt(t,"height","0"),Dt(t,"flex-basis","0"),Math.min(o,((e,t,o)=>{const n=ot(e).dom,s=mi(e,".tox-dialog-wrap").getOr(e);let r;r="fixed"===It(s,"position")?Math.max(n.clientHeight,window.innerHeight):Math.max(n.offsetHeight,n.scrollHeight);const a=Wt(t),i=t.dom.offsetLeft>=o.dom.offsetLeft+Jt(o)?Math.max(Wt(o),a):a,l=parseInt(It(e,"margin-top"),10)||0,c=parseInt(It(e,"margin-bottom"),10)||0;return r-(Wt(e)+l+c-i)})(e,t,n))))).each((e=>{PR(t,e)}))}))}))},WR=e=>pi(e,'[role="tabpanel"]'),jR="send-data-to-section",GR="send-data-to-view",$R=(e,t,o)=>{const n=Es({}),s=e=>{const t=pu.getValue(e),o=ER(t).getOr({}),s=n.get(),r=fn(s,o);n.set(r)},r=e=>{const t=n.get();pu.setValue(e,t)},a=Es(null),i=H(e.tabs,(e=>({value:e.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"]},components:[ti(o.shared.providers.translate(e.title))],view:()=>[CC.sketch((n=>({dom:{tag:"div",classes:["tox-form"]},components:H(e.items,(e=>X_(n,e,t,o))),formBehaviours:kl([Pp.config({mode:"acyclic",useTabstopAt:C(XC)}),Jp("TabView.form.events",[Kr(r),Jr(s)]),Al.config({channels:Ds([{key:jR,value:{onReceive:s}},{key:GR,value:{onReceive:r}}])})])})))]}))),l=(e=>{const t=Ql(),o=[Kr((o=>{const n=o.element;WR(n).each((s=>{Dt(s,"visibility","hidden"),o.getSystem().getByDom(s).toOptional().each((o=>{const n=((e,t,o)=>H(e,((n,s)=>{Kp.set(o,e[s].view());const r=t.dom.getBoundingClientRect();return Kp.set(o,[]),r.height})))(e,s,o),r=(e=>oe(ee(e,((e,t)=>e>t?-1:e<t?1:0))))(n);r.fold(t.clear,t.set)})),UR(n,s,t),Ht(s,"visibility"),((e,t)=>{oe(e).each((e=>LR.showTab(t,e.value)))})(e,o),requestAnimationFrame((()=>{UR(n,s,t)}))}))})),Ur(wr(),(e=>{const o=e.element;WR(o).each((e=>{UR(o,e,t)}))})),Ur(kS,((e,o)=>{const n=e.element;WR(n).each((e=>{const o=Il(ht(e));Dt(e,"visibility","hidden");const s=Nt(e,"height").map((e=>parseInt(e,10)));Ht(e,"height"),Ht(e,"flex-basis");const r=e.dom.getBoundingClientRect().height;s.forall((e=>r>e))?(t.set(r),UR(n,e,t)):s.each((t=>{PR(e,t)})),Ht(e,"visibility"),o.each(Dl)}))}))];return{extraEvents:o,selectFirst:!1}})(i);return LR.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:(e,t,o)=>{const n=pu.getValue(t);Ir(e,SS,{name:n,oldName:a.get()}),a.set(n)},tabs:i,components:[LR.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[IR.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:kl([cS.config({})])}),LR.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:l.selectFirst,tabSectionBehaviours:kl([Jp("tabpanel",l.extraEvents),Pp.config({mode:"acyclic"}),wm.config({find:e=>oe(LR.getViewItems(e))}),RC(A.none(),(e=>(e.getSystem().broadcastOn([jR],{}),n.get())),((e,t)=>{n.set(t),e.getSystem().broadcastOn([GR],{})}))])})},qR=(e,t,o,n,s)=>({dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:{...o.map((e=>({id:e}))).getOr({}),...s?{"aria-live":"polite"}:{}}},components:[],behaviours:kl([BC(0),TR.config({channel:`${JC}-${t}`,updateState:(e,t)=>A.some({isTabPanel:()=>"tabpanel"===t.body.type}),renderComponents:e=>{const t=e.body;return"tabpanel"===t.type?[$R(t,e.initialData,n)]:[AR(t,e.initialData,n)]},initialData:e})])});function XR(e){return XR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},XR(e)}function YR(e,t){return YR=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},YR(e,t)}function KR(e,t,o){return KR=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,o){var n=[null];n.push.apply(n,t);var s=new(Function.bind.apply(e,n));return o&&YR(s,o.prototype),s},KR.apply(null,arguments)}function JR(e){return function(e){if(Array.isArray(e))return ZR(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ZR(e,t);var o=Object.prototype.toString.call(e).slice(8,-1);return"Object"===o&&e.constructor&&(o=e.constructor.name),"Map"===o||"Set"===o?Array.from(e):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?ZR(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ZR(e,t){(null==t||t>e.length)&&(t=e.length);for(var o=0,n=new Array(t);o<t;o++)n[o]=e[o];return n}var QR=Object.hasOwnProperty,eN=Object.setPrototypeOf,tN=Object.isFrozen,oN=Object.getPrototypeOf,nN=Object.getOwnPropertyDescriptor,sN=Object.freeze,rN=Object.seal,aN=Object.create,iN="undefined"!=typeof Reflect&&Reflect,lN=iN.apply,cN=iN.construct;lN||(lN=function(e,t,o){return e.apply(t,o)}),sN||(sN=function(e){return e}),rN||(rN=function(e){return e}),cN||(cN=function(e,t){return KR(e,JR(t))});var dN,uN=wN(Array.prototype.forEach),mN=wN(Array.prototype.pop),gN=wN(Array.prototype.push),pN=wN(String.prototype.toLowerCase),hN=wN(String.prototype.match),fN=wN(String.prototype.replace),bN=wN(String.prototype.indexOf),vN=wN(String.prototype.trim),yN=wN(RegExp.prototype.test),xN=(dN=TypeError,function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return cN(dN,t)});function wN(e){return function(t){for(var o=arguments.length,n=new Array(o>1?o-1:0),s=1;s<o;s++)n[s-1]=arguments[s];return lN(e,t,n)}}function SN(e,t){eN&&eN(e,null);for(var o=t.length;o--;){var n=t[o];if("string"==typeof n){var s=pN(n);s!==n&&(tN(t)||(t[o]=s),n=s)}e[n]=!0}return e}function kN(e){var t,o=aN(null);for(t in e)lN(QR,e,[t])&&(o[t]=e[t]);return o}function CN(e,t){for(;null!==e;){var o=nN(e,t);if(o){if(o.get)return wN(o.get);if("function"==typeof o.value)return wN(o.value)}e=oN(e)}return function(e){return console.warn("fallback value for",e),null}}var ON=sN(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),_N=sN(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),TN=sN(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),EN=sN(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),AN=sN(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),MN=sN(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),DN=sN(["#text"]),BN=sN(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),FN=sN(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),IN=sN(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),RN=sN(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),NN=rN(/\{\{[\w\W]*|[\w\W]*\}\}/gm),VN=rN(/<%[\w\W]*|[\w\W]*%>/gm),zN=rN(/^data-[\-\w.\u00B7-\uFFFF]/),HN=rN(/^aria-[\-\w]+$/),LN=rN(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),PN=rN(/^(?:\w+script|data):/i),UN=rN(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),WN=rN(/^html$/i),jN=function(){return"undefined"==typeof window?null:window},GN=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:jN(),o=function(t){return e(t)};if(o.version="2.3.8",o.removed=[],!t||!t.document||9!==t.document.nodeType)return o.isSupported=!1,o;var n=t.document,s=t.document,r=t.DocumentFragment,a=t.HTMLTemplateElement,i=t.Node,l=t.Element,c=t.NodeFilter,d=t.NamedNodeMap,u=void 0===d?t.NamedNodeMap||t.MozNamedAttrMap:d,m=t.HTMLFormElement,g=t.DOMParser,p=t.trustedTypes,h=l.prototype,f=CN(h,"cloneNode"),b=CN(h,"nextSibling"),v=CN(h,"childNodes"),y=CN(h,"parentNode");if("function"==typeof a){var x=s.createElement("template");x.content&&x.content.ownerDocument&&(s=x.content.ownerDocument)}var w=function(e,t){if("object"!==XR(e)||"function"!=typeof e.createPolicy)return null;var o=null,n="data-tt-policy-suffix";t.currentScript&&t.currentScript.hasAttribute(n)&&(o=t.currentScript.getAttribute(n));var s="dompurify"+(o?"#"+o:"");try{return e.createPolicy(s,{createHTML:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+s+" could not be created."),null}}(p,n),S=w?w.createHTML(""):"",k=s,C=k.implementation,O=k.createNodeIterator,_=k.createDocumentFragment,T=k.getElementsByTagName,E=n.importNode,A={};try{A=kN(s).documentMode?s.documentMode:{}}catch(e){}var M={};o.isSupported="function"==typeof y&&C&&void 0!==C.createHTMLDocument&&9!==A;var D,B,F=NN,I=VN,R=zN,N=HN,V=PN,z=UN,H=LN,L=null,P=SN({},[].concat(JR(ON),JR(_N),JR(TN),JR(AN),JR(DN))),U=null,W=SN({},[].concat(JR(BN),JR(FN),JR(IN),JR(RN))),j=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),G=null,$=null,q=!0,X=!0,Y=!1,K=!1,J=!1,Z=!1,Q=!1,ee=!1,te=!1,oe=!1,ne=!0,se=!0,re=!1,ae={},ie=null,le=SN({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),ce=null,de=SN({},["audio","video","img","source","image","track"]),ue=null,me=SN({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ge="http://www.w3.org/1998/Math/MathML",pe="http://www.w3.org/2000/svg",he="http://www.w3.org/1999/xhtml",fe=he,be=!1,ve=["application/xhtml+xml","text/html"],ye=null,xe=s.createElement("form"),we=function(e){return e instanceof RegExp||e instanceof Function},Se=function(e){ye&&ye===e||(e&&"object"===XR(e)||(e={}),e=kN(e),L="ALLOWED_TAGS"in e?SN({},e.ALLOWED_TAGS):P,U="ALLOWED_ATTR"in e?SN({},e.ALLOWED_ATTR):W,ue="ADD_URI_SAFE_ATTR"in e?SN(kN(me),e.ADD_URI_SAFE_ATTR):me,ce="ADD_DATA_URI_TAGS"in e?SN(kN(de),e.ADD_DATA_URI_TAGS):de,ie="FORBID_CONTENTS"in e?SN({},e.FORBID_CONTENTS):le,G="FORBID_TAGS"in e?SN({},e.FORBID_TAGS):{},$="FORBID_ATTR"in e?SN({},e.FORBID_ATTR):{},ae="USE_PROFILES"in e&&e.USE_PROFILES,q=!1!==e.ALLOW_ARIA_ATTR,X=!1!==e.ALLOW_DATA_ATTR,Y=e.ALLOW_UNKNOWN_PROTOCOLS||!1,K=e.SAFE_FOR_TEMPLATES||!1,J=e.WHOLE_DOCUMENT||!1,ee=e.RETURN_DOM||!1,te=e.RETURN_DOM_FRAGMENT||!1,oe=e.RETURN_TRUSTED_TYPE||!1,Q=e.FORCE_BODY||!1,ne=!1!==e.SANITIZE_DOM,se=!1!==e.KEEP_CONTENT,re=e.IN_PLACE||!1,H=e.ALLOWED_URI_REGEXP||H,fe=e.NAMESPACE||he,e.CUSTOM_ELEMENT_HANDLING&&we(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(j.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&we(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(j.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(j.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),D=D=-1===ve.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,B="application/xhtml+xml"===D?function(e){return e}:pN,K&&(X=!1),te&&(ee=!0),ae&&(L=SN({},JR(DN)),U=[],!0===ae.html&&(SN(L,ON),SN(U,BN)),!0===ae.svg&&(SN(L,_N),SN(U,FN),SN(U,RN)),!0===ae.svgFilters&&(SN(L,TN),SN(U,FN),SN(U,RN)),!0===ae.mathMl&&(SN(L,AN),SN(U,IN),SN(U,RN))),e.ADD_TAGS&&(L===P&&(L=kN(L)),SN(L,e.ADD_TAGS)),e.ADD_ATTR&&(U===W&&(U=kN(U)),SN(U,e.ADD_ATTR)),e.ADD_URI_SAFE_ATTR&&SN(ue,e.ADD_URI_SAFE_ATTR),e.FORBID_CONTENTS&&(ie===le&&(ie=kN(ie)),SN(ie,e.FORBID_CONTENTS)),se&&(L["#text"]=!0),J&&SN(L,["html","head","body"]),L.table&&(SN(L,["tbody"]),delete G.tbody),sN&&sN(e),ye=e)},ke=SN({},["mi","mo","mn","ms","mtext"]),Ce=SN({},["foreignobject","desc","title","annotation-xml"]),Oe=SN({},["title","style","font","a","script"]),_e=SN({},_N);SN(_e,TN),SN(_e,EN);var Te=SN({},AN);SN(Te,MN);var Ee=function(e){gN(o.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=S}catch(t){e.remove()}}},Ae=function(e,t){try{gN(o.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){gN(o.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!U[e])if(ee||te)try{Ee(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Me=function(e){var t,o;if(Q)e="<remove></remove>"+e;else{var n=hN(e,/^[\r\n\t ]+/);o=n&&n[0]}"application/xhtml+xml"===D&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var r=w?w.createHTML(e):e;if(fe===he)try{t=(new g).parseFromString(r,D)}catch(e){}if(!t||!t.documentElement){t=C.createDocument(fe,"template",null);try{t.documentElement.innerHTML=be?"":r}catch(e){}}var a=t.body||t.documentElement;return e&&o&&a.insertBefore(s.createTextNode(o),a.childNodes[0]||null),fe===he?T.call(t,J?"html":"body")[0]:J?t.documentElement:a},De=function(e){return O.call(e.ownerDocument||e,e,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT,null,!1)},Be=function(e){return"object"===XR(i)?e instanceof i:e&&"object"===XR(e)&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Fe=function(e,t,n){M[e]&&uN(M[e],(function(e){e.call(o,t,n,ye)}))},Ie=function(e){var t,n;if(Fe("beforeSanitizeElements",e,null),(n=e)instanceof m&&("string"!=typeof n.nodeName||"string"!=typeof n.textContent||"function"!=typeof n.removeChild||!(n.attributes instanceof u)||"function"!=typeof n.removeAttribute||"function"!=typeof n.setAttribute||"string"!=typeof n.namespaceURI||"function"!=typeof n.insertBefore))return Ee(e),!0;if(yN(/[\u0080-\uFFFF]/,e.nodeName))return Ee(e),!0;var s=B(e.nodeName);if(Fe("uponSanitizeElement",e,{tagName:s,allowedTags:L}),e.hasChildNodes()&&!Be(e.firstElementChild)&&(!Be(e.content)||!Be(e.content.firstElementChild))&&yN(/<[/\w]/g,e.innerHTML)&&yN(/<[/\w]/g,e.textContent))return Ee(e),!0;if("select"===s&&yN(/<template/i,e.innerHTML))return Ee(e),!0;if(!L[s]||G[s]){if(!G[s]&&Ne(s)){if(j.tagNameCheck instanceof RegExp&&yN(j.tagNameCheck,s))return!1;if(j.tagNameCheck instanceof Function&&j.tagNameCheck(s))return!1}if(se&&!ie[s]){var r=y(e)||e.parentNode,a=v(e)||e.childNodes;if(a&&r)for(var i=a.length-1;i>=0;--i)r.insertBefore(f(a[i],!0),b(e))}return Ee(e),!0}return e instanceof l&&!function(e){var t=y(e);t&&t.tagName||(t={namespaceURI:he,tagName:"template"});var o=pN(e.tagName),n=pN(t.tagName);return e.namespaceURI===pe?t.namespaceURI===he?"svg"===o:t.namespaceURI===ge?"svg"===o&&("annotation-xml"===n||ke[n]):Boolean(_e[o]):e.namespaceURI===ge?t.namespaceURI===he?"math"===o:t.namespaceURI===pe?"math"===o&&Ce[n]:Boolean(Te[o]):e.namespaceURI===he&&!(t.namespaceURI===pe&&!Ce[n])&&!(t.namespaceURI===ge&&!ke[n])&&!Te[o]&&(Oe[o]||!_e[o])}(e)?(Ee(e),!0):"noscript"!==s&&"noembed"!==s||!yN(/<\/no(script|embed)/i,e.innerHTML)?(K&&3===e.nodeType&&(t=e.textContent,t=fN(t,F," "),t=fN(t,I," "),e.textContent!==t&&(gN(o.removed,{element:e.cloneNode()}),e.textContent=t)),Fe("afterSanitizeElements",e,null),!1):(Ee(e),!0)},Re=function(e,t,o){if(ne&&("id"===t||"name"===t)&&(o in s||o in xe))return!1;if(X&&!$[t]&&yN(R,t));else if(q&&yN(N,t));else if(!U[t]||$[t]){if(!(Ne(e)&&(j.tagNameCheck instanceof RegExp&&yN(j.tagNameCheck,e)||j.tagNameCheck instanceof Function&&j.tagNameCheck(e))&&(j.attributeNameCheck instanceof RegExp&&yN(j.attributeNameCheck,t)||j.attributeNameCheck instanceof Function&&j.attributeNameCheck(t))||"is"===t&&j.allowCustomizedBuiltInElements&&(j.tagNameCheck instanceof RegExp&&yN(j.tagNameCheck,o)||j.tagNameCheck instanceof Function&&j.tagNameCheck(o))))return!1}else if(ue[t]);else if(yN(H,fN(o,z,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==bN(o,"data:")||!ce[e])if(Y&&!yN(V,fN(o,z,"")));else if(o)return!1;return!0},Ne=function(e){return e.indexOf("-")>0},Ve=function(e){var t,o,n,s;Fe("beforeSanitizeAttributes",e,null);var r=e.attributes;if(r){var a={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:U};for(s=r.length;s--;){var i=t=r[s],l=i.name,c=i.namespaceURI;o="value"===l?t.value:vN(t.value),n=B(l);var d=o;if(a.attrName=n,a.attrValue=o,a.keepAttr=!0,a.forceKeepAttr=void 0,Fe("uponSanitizeAttribute",e,a),o=a.attrValue,!a.forceKeepAttr)if(a.keepAttr)if(yN(/\/>/i,o))Ae(l,e);else{K&&(o=fN(o,F," "),o=fN(o,I," "));var u=B(e.nodeName);if(Re(u,n,o)){if(o!==d)try{c?e.setAttributeNS(c,l,o):e.setAttribute(l,o)}catch(t){Ae(l,e)}}else Ae(l,e)}else Ae(l,e)}Fe("afterSanitizeAttributes",e,null)}},ze=function e(t){var o,n=De(t);for(Fe("beforeSanitizeShadowDOM",t,null);o=n.nextNode();)Fe("uponSanitizeShadowNode",o,null),Ie(o)||(o.content instanceof r&&e(o.content),Ve(o));Fe("afterSanitizeShadowDOM",t,null)};return o.sanitize=function(e,s){var a,l,c,d,u;if((be=!e)&&(e="\x3c!--\x3e"),"string"!=typeof e&&!Be(e)){if("function"!=typeof e.toString)throw xN("toString is not a function");if("string"!=typeof(e=e.toString()))throw xN("dirty is not a string, aborting")}if(!o.isSupported){if("object"===XR(t.toStaticHTML)||"function"==typeof t.toStaticHTML){if("string"==typeof e)return t.toStaticHTML(e);if(Be(e))return t.toStaticHTML(e.outerHTML)}return e}if(Z||Se(s),o.removed=[],"string"==typeof e&&(re=!1),re){if(e.nodeName){var m=B(e.nodeName);if(!L[m]||G[m])throw xN("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof i)1===(l=(a=Me("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType&&"BODY"===l.nodeName||"HTML"===l.nodeName?a=l:a.appendChild(l);else{if(!ee&&!K&&!J&&-1===e.indexOf("<"))return w&&oe?w.createHTML(e):e;if(!(a=Me(e)))return ee?null:oe?S:""}a&&Q&&Ee(a.firstChild);for(var g=De(re?e:a);c=g.nextNode();)3===c.nodeType&&c===d||Ie(c)||(c.content instanceof r&&ze(c.content),Ve(c),d=c);if(d=null,re)return e;if(ee){if(te)for(u=_.call(a.ownerDocument);a.firstChild;)u.appendChild(a.firstChild);else u=a;return U.shadowroot&&(u=E.call(n,u,!0)),u}var p=J?a.outerHTML:a.innerHTML;return J&&L["!doctype"]&&a.ownerDocument&&a.ownerDocument.doctype&&a.ownerDocument.doctype.name&&yN(WN,a.ownerDocument.doctype.name)&&(p="<!DOCTYPE "+a.ownerDocument.doctype.name+">\n"+p),K&&(p=fN(p,F," "),p=fN(p,I," ")),w&&oe?w.createHTML(p):p},o.setConfig=function(e){Se(e),Z=!0},o.clearConfig=function(){ye=null,Z=!1},o.isValidAttribute=function(e,t,o){ye||Se({});var n=B(e),s=B(t);return Re(n,s,o)},o.addHook=function(e,t){"function"==typeof t&&(M[e]=M[e]||[],gN(M[e],t))},o.removeHook=function(e){if(M[e])return mN(M[e])},o.removeHooks=function(e){M[e]&&(M[e]=[])},o.removeAllHooks=function(){M={}},o}();const $N=e=>GN().sanitize(e),qN=lf.deviceType.isTouch(),XN=(e,t)=>({dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[e,t]}),YN=(e,t)=>LF.parts.close(Wh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:e,buttonBehaviours:kl([cS.config({})])})),KN=()=>LF.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}}),JN=(e,t)=>LF.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:_A(`<p>${$N(t.translate(e))}</p>`)}]}]}),ZN=e=>LF.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:e}),QN=(e,t)=>[oS.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:e}),oS.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})],eV=e=>{const t="tox-dialog",o=t+"-wrap",n=o+"__backdrop",s=t+"__disable-scroll";return LF.sketch({lazySink:e.lazySink,onEscape:t=>(e.onEscape(t),A.some(!0)),useTabstopAt:e=>!XC(e),firstTabstop:e.firstTabstop,dom:{tag:"div",classes:[t].concat(e.extraClasses),styles:{position:"relative",...e.extraStyles}},components:[e.header,e.body,...e.footer.toArray()],parts:{blocker:{dom:_A(`<div class="${o}"></div>`),components:[{dom:{tag:"div",classes:qN?[n,n+"--opaque"]:[n]}}]}},dragBlockClass:o,modalBehaviours:kl([oh.config({}),Jp("dialog-events",e.dialogEvents.concat([Yr(Xs(),((e,t)=>{OA.isBlocked(e)||Pp.focusIn(e)})),Ur(_r(),((e,t)=>{e.getSystem().broadcastOn([eO],{newFocus:t.event.newFocus})}))])),Jp("scroll-lock",[Kr((()=>{La(xt(),s)})),Jr((()=>{Pa(xt(),s)}))]),...e.extraBehaviours]),eventOrder:{[ur()]:["dialog-events"],[Sr()]:["scroll-lock","dialog-events","alloy.base.behaviour"],[kr()]:["alloy.base.behaviour","dialog-events","scroll-lock"],...e.eventOrder}})},tV=e=>Wh.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":e.translate("Close"),title:e.translate("Close")}},buttonBehaviours:kl([cS.config({})]),components:[ef("close",{tag:"span",classes:["tox-icon"]},e.icons)],action:e=>{Fr(e,bS)}}),oV=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__title"],attributes:{...o.map((e=>({id:e}))).getOr({})}},components:[],behaviours:kl([TR.config({channel:`${KC}-${t}`,initialData:e,renderComponents:e=>[ti(n.translate(e.title))]})])}),nV=()=>({dom:_A('<div class="tox-dialog__draghandle"></div>')}),sV=(e,t,o)=>((e,t,o)=>{const n=LF.parts.title(oV(e,t,A.none(),o)),s=LF.parts.draghandle(nV()),r=LF.parts.close(tV(o)),a=[n].concat(e.draggable?[s]:[]).concat([r]);return oS.sketch({dom:_A('<div class="tox-dialog__header"></div>'),components:a})})({title:o.shared.providers.translate(e),draggable:o.dialog.isDraggableModal()},t,o.shared.providers),rV=(e,t,o,n)=>({dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":o.translate(e)},styles:{left:"0px",right:"0px",bottom:"0px",top:`${n.getOr(0)}px`,position:"absolute"}},behaviours:t,components:[{dom:_A('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}),aV=(e,t,o)=>({onClose:()=>o.closeWindow(),onBlock:o=>{const n=pi(e().element,".tox-dialog__header").map((e=>Wt(e)));LF.setBusy(e(),((e,s)=>rV(o.message,s,t,n)))},onUnblock:()=>{LF.setIdle(e())}}),iV=(e,t,o,n)=>ri(eV({...e,firstTabstop:1,lazySink:n.shared.getSink,extraBehaviours:[TR.config({channel:`${YC}-${e.id}`,updateState:(e,t)=>A.some(t),initialData:t}),VC({}),...e.extraBehaviours],onEscape:e=>{Fr(e,bS)},dialogEvents:o,eventOrder:{[dr()]:[TR.name(),Al.name()],[Sr()]:["scroll-lock",TR.name(),"messages","dialog-events","alloy.base.behaviour"],[kr()]:["alloy.base.behaviour","dialog-events","messages",TR.name(),"scroll-lock"]}})),lV=(e,t={})=>H(e,(e=>"menu"===e.type?(e=>{const o=H(e.items,(e=>{const o=be(t,e.name).getOr(Es(!1));return{...e,storage:o}}));return{...e,items:o}})(e):e)),cV=e=>j(e,((e,t)=>"menu"===t.type?j(t.items,((e,t)=>(e[t.name]=t.storage,e)),e):e),{}),dV=(e,t)=>[$r(Xs(),qC),e(fS,((e,o,n,s)=>{Il(ht(s.element)).fold(b,Bl),t.onClose(),o.onClose()})),e(bS,((e,t,o,n)=>{t.onCancel(e),Fr(n,fS)})),Ur(wS,((e,o)=>t.onUnblock())),Ur(xS,((e,o)=>t.onBlock(o.event)))],uV=(e,t,o)=>{const n=(t,o)=>Ur(t,((t,n)=>{s(t,((s,r)=>{o(e(),s,n.event,t)}))})),s=(e,t)=>{TR.getState(e).get().each((o=>{t(o.internalDialog,e)}))};return[...dV(n,t),n(yS,((e,t)=>t.onSubmit(e))),n(hS,((e,t,o)=>{t.onChange(e,{name:o.name})})),n(vS,((e,t,n,s)=>{const r=()=>s.getSystem().isConnected()?Pp.focusIn(s):void 0,a=e=>Tt(e,"disabled")||_t(e,"aria-disabled").exists((e=>"true"===e)),i=ht(s.element),l=Il(i);t.onAction(e,{name:n.name,value:n.value}),Il(i).fold(r,(e=>{a(e)||l.exists((t=>Qe(e,t)&&a(t)))?r():o().toOptional().filter((t=>!Qe(t.element,e))).each(r)}))})),n(SS,((e,t,o)=>{t.onTabChange(e,{newTabName:o.name,oldTabName:o.oldName})})),Jr((t=>{const o=e();pu.setValue(t,o.getData())}))]},mV=(e,t)=>{const o=t.map((e=>e.footerButtons)).getOr([]),n=P(o,(e=>"start"===e.align)),s=(e,t)=>oS.sketch({dom:{tag:"div",classes:[`tox-dialog__footer-${e}`]},components:H(t,(e=>e.memento.asSpec()))});return[s("start",n.pass),s("end",n.fail)]},gV=(e,t,o)=>({dom:_A('<div class="tox-dialog__footer"></div>'),components:[],behaviours:kl([TR.config({channel:`${ZC}-${t}`,initialData:e,updateState:(e,t)=>{const n=H(t.buttons,(e=>{const t=jh(((e,t)=>B_(e,e.type,t))(e,o));return{name:e.name,align:e.align,memento:t}}));return A.some({lookupByName:t=>((e,t,o)=>G(t,(e=>e.name===o)).bind((t=>t.memento.getOpt(e))))(e,n,t),footerButtons:n})},renderComponents:mV})])}),pV=(e,t,o)=>LF.parts.footer(gV(e,t,o)),hV=(e,t)=>{if(e.getRoot().getSystem().isConnected()){const o=wm.getCurrent(e.getFormWrapper()).getOr(e.getFormWrapper());return CC.getField(o,t).orThunk((()=>{const o=e.getFooter().bind((e=>TR.getState(e).get()));return o.bind((e=>e.lookupByName(t)))}))}return A.none()},fV=(e,t,o)=>{const n=t=>{const o=e.getRoot();o.getSystem().isConnected()&&t(o)},s={getData:()=>{const t=e.getRoot(),n=t.getSystem().isConnected()?e.getFormWrapper():t;return{...pu.getValue(n),...ce(o,(e=>e.get()))}},setData:t=>{n((n=>{const r=s.getData(),a=fn(r,t),i=((e,t)=>{const o=e.getRoot();return TR.getState(o).get().map((e=>Xn(qn("data",e.dataValidator,t)))).getOr(t)})(e,a),l=e.getFormWrapper();pu.setValue(l,i),le(o,((e,t)=>{ve(a,t)&&e.set(a[t])}))}))},setEnabled:(t,o)=>{hV(e,t).each(o?Rm.enable:Rm.disable)},focus:t=>{hV(e,t).each(oh.focus)},block:e=>{if(!r(e))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n((t=>{Ir(t,xS,{message:e})}))},unblock:()=>{n((e=>{Fr(e,wS)}))},showTab:t=>{n((o=>{const n=e.getBody();TR.getState(n).get().exists((e=>e.isTabPanel()))&&wm.getCurrent(n).each((e=>{LR.showTab(e,t)}))}))},redial:r=>{n((n=>{const a=e.getId(),i=t(r),l=lV(i.internalDialog.buttons,o);n.getSystem().broadcastOn([`${YC}-${a}`],i),n.getSystem().broadcastOn([`${KC}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${JC}-${a}`],i.internalDialog),n.getSystem().broadcastOn([`${ZC}-${a}`],{...i.internalDialog,buttons:l}),s.setData(i.initialData)}))},close:()=>{n((e=>{Fr(e,fS)}))},toggleFullscreen:e.toggleFullscreen};return s},bV=(e,t,o,n=!1)=>{const s=la("dialog"),r=la("dialog-label"),a=la("dialog-content"),i=e.internalDialog,l="medium"===i.size?A.some("tox-dialog--width-md"):A.none(),c=jh(((e,t,o,n)=>oS.sketch({dom:_A('<div class="tox-dialog__header"></div>'),components:[oV(e,t,A.some(o),n),nV(),tV(n)],containerBehaviours:kl([OF.config({mode:"mouse",blockerClass:"blocker",getTarget:e=>hi(e,'[role="dialog"]').getOrDie(),snaps:{getSnapPoints:()=>[],leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))({title:i.title,draggable:!0},s,r,o.shared.providers)),d=jh(((e,t,o,n,s)=>qR(e,t,A.some(o),n,s))({body:i.body,initialData:i.initialData},s,a,o,n)),u=lV(i.buttons),m=cV(u),g=Ce(0!==u.length,jh(((e,t,o)=>gV(e,t,o))({buttons:u},s,o))),p=uV((()=>b),{onBlock:e=>{OA.block(f,((t,n)=>{const s=c.getOpt(f).map((e=>Wt(e.element)));return rV(e.message,n,o.shared.providers,s)}))},onUnblock:()=>{OA.unblock(f)},onClose:()=>t.closeWindow()},o.shared.getSink),h="tox-dialog-inline",f=ri({dom:{tag:"div",classes:["tox-dialog",h,...l.toArray()],attributes:{role:"dialog","aria-labelledby":r}},eventOrder:{[dr()]:[TR.name(),Al.name()],[ur()]:["execute-on-form"],[Sr()]:["reflecting","execute-on-form"]},behaviours:kl([Pp.config({mode:"cyclic",onEscape:e=>(Fr(e,fS),A.some(!0)),useTabstopAt:e=>!XC(e)&&("button"!==Ue(e)||"disabled"!==Ot(e,"disabled")),firstTabstop:1}),TR.config({channel:`${YC}-${s}`,updateState:(e,t)=>A.some(t),initialData:e}),oh.config({}),Jp("execute-on-form",p.concat([Yr(Xs(),((e,t)=>{Pp.focusIn(e)})),Ur(_r(),((e,t)=>{e.getSystem().broadcastOn([eO],{newFocus:t.event.newFocus})}))])),OA.config({getRoot:()=>A.some(f)}),Kp.config({}),VC({})]),components:[c.asSpec(),d.asSpec(),...g.map((e=>e.asSpec())).toArray()]}),b=fV({getId:x(s),getRoot:x(f),getFooter:()=>g.map((e=>e.get(f))),getBody:()=>d.get(f),getFormWrapper:()=>{const e=d.get(f);return wm.getCurrent(e).getOr(e)},toggleFullscreen:()=>{const e="tox-dialog--fullscreen",t=Ve(f.element.dom);Ga(t,[e])?(ja(t,[e]),Wa(t,[h])):(ja(t,[h]),Wa(t,[e]))}},t.redial,m);return{dialog:f,instanceApi:b}};var vV=tinymce.util.Tools.resolve("tinymce.util.URI");const yV=["insertContent","setContent","execCommand","close","block","unblock"],xV=e=>a(e)&&-1!==yV.indexOf(e.mceAction),wV=(e,t,o,n)=>{const s=la("dialog"),i=sV(e.title,s,n),l=(e=>{const t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[GC(A.none(),{dom:{tag:"iframe",attributes:{src:e.url}},behaviours:kl([cS.config({}),oh.config({})])})]}],behaviours:kl([Pp.config({mode:"acyclic",useTabstopAt:C(XC)})])};return LF.parts.body(t)})(e),c=e.buttons.bind((e=>0===e.length?A.none():A.some(pV({buttons:e},s,n)))),u=((e,t)=>{const o=(e,t)=>Ur(e,((e,o)=>{n(e,((n,s)=>{t(x,n,o.event,e)}))})),n=(e,t)=>{TR.getState(e).get().each((o=>{t(o,e)}))};return[...dV(o,t),o(vS,((e,t,o)=>{t.onAction(e,{name:o.name})}))]})(0,aV((()=>y),n.shared.providers,t)),m={...e.height.fold((()=>({})),(e=>({height:e+"px","max-height":e+"px"}))),...e.width.fold((()=>({})),(e=>({width:e+"px","max-width":e+"px"})))},p=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],h=new vV(e.url,{base_uri:new vV(window.location.href)}),f=`${h.protocol}://${h.host}${h.port?":"+h.port:""}`,b=Zl(),v=[Jp("messages",[Kr((()=>{const t=tc(Ve(window),"message",(t=>{if(h.isSameOrigin(new vV(t.raw.origin))){const n=t.raw.data;xV(n)?((e,t,o)=>{switch(o.mceAction){case"insertContent":e.insertContent(o.content);break;case"setContent":e.setContent(o.content);break;case"execCommand":const n=!!d(o.ui)&&o.ui;e.execCommand(o.cmd,n,o.value);break;case"close":t.close();break;case"block":t.block(o.message);break;case"unblock":t.unblock()}})(o,x,n):(e=>!xV(e)&&a(e)&&ve(e,"mceAction"))(n)&&e.onMessage(x,n)}}));b.set(t)})),Jr(b.clear)]),Al.config({channels:{[QC]:{onReceive:(e,t)=>{pi(e.element,"iframe").each((e=>{const o=e.dom.contentWindow;g(o)&&o.postMessage(t,f)}))}}}})],y=iV({id:s,header:i,body:l,footer:c,extraClasses:p,extraBehaviours:v,extraStyles:m},e,u,n),x=(e=>{const t=t=>{e.getSystem().isConnected()&&t(e)};return{block:e=>{if(!r(e))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t((t=>{Ir(t,xS,{message:e})}))},unblock:()=>{t((e=>{Fr(e,wS)}))},close:()=>{t((e=>{Fr(e,fS)}))},sendMessage:e=>{t((t=>{t.getSystem().broadcastOn([QC],e)}))}}})(y);return{dialog:y,instanceApi:x}},SV=(e,t)=>Xn(qn("data",t,e)),kV=e=>$w(e,".tox-alert-dialog")||$w(e,".tox-confirm-dialog"),CV=(e,t,o)=>t&&o?[]:[$E.config({contextual:{lazyContext:()=>A.some(Jo(Ve(e.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"],lazyViewport:t=>jw(e,t.element).map((e=>({bounds:Gw(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))).getOrThunk((()=>({bounds:en(),optScrollEnv:A.none()})))})],OV=e=>{const t=e.editor,o=ib(t),n=(e=>{const t=e.shared;return{open:(o,n)=>{const s=()=>{LF.hide(l),n()},r=jh(B_({name:"close-alert",text:"OK",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"cancel",e)),a=KN(),i=YN(s,t.providers),l=ri(eV({lazySink:()=>t.getSink(),header:XN(a,i),body:JN(o,t.providers),footer:A.some(ZN(QN([],[r.asSpec()]))),onEscape:s,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Ur(bS,s)],eventOrder:{}}));LF.show(l);const c=r.get(l);oh.focus(c)}}})(e.backstages.dialog),s=(e=>{const t=e.shared;return{open:(o,n)=>{const s=e=>{LF.hide(c),n(e)},r=jh(B_({name:"yes",text:"Yes",primary:!0,buttonType:A.some("primary"),align:"end",enabled:!0,icon:A.none()},"submit",e)),a=B_({name:"no",text:"No",primary:!1,buttonType:A.some("secondary"),align:"end",enabled:!0,icon:A.none()},"cancel",e),i=KN(),l=YN((()=>s(!1)),t.providers),c=ri(eV({lazySink:()=>t.getSink(),header:XN(i,l),body:JN(o,t.providers),footer:A.some(ZN(QN([],[a,r.asSpec()]))),onEscape:()=>s(!1),extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[Ur(bS,(()=>s(!1))),Ur(yS,(()=>s(!0)))],eventOrder:{}}));LF.show(c);const d=r.get(c);oh.focus(d)}}})(e.backstages.dialog),r=(t,o)=>kR.open(((t,n,s)=>{const r=n,a=((e,t,o)=>{const n=la("dialog"),s=e.internalDialog,r=sV(s.title,n,o),a=((e,t,o)=>{const n=qR(e,t,A.none(),o,!1);return LF.parts.body(n)})({body:s.body,initialData:s.initialData},n,o),i=lV(s.buttons),l=cV(i),c=Ce(0!==i.length,pV({buttons:i},n,o)),d=uV((()=>p),aV((()=>m),o.shared.providers,t),o.shared.getSink),u=(e=>{switch(e){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}})(s.size),m=iV({id:n,header:r,body:a,footer:c,extraClasses:u,extraBehaviours:[],extraStyles:{}},e,d,o),g={getId:x(n),getRoot:x(m),getBody:()=>LF.getBody(m),getFooter:()=>LF.getFooter(m),getFormWrapper:()=>{const e=LF.getBody(m);return wm.getCurrent(e).getOr(e)},toggleFullscreen:()=>{const e="tox-dialog--fullscreen",t=Ve(m.element.dom);Ua(t,e)?(Pa(t,e),Wa(t,u)):(ja(t,u),La(t,e))}},p=fV(g,t.redial,l);return{dialog:m,instanceApi:p}})({dataValidator:s,initialData:r,internalDialog:t},{redial:kR.redial,closeWindow:()=>{LF.hide(a.dialog),o(a.instanceApi)}},e.backstages.dialog);return LF.show(a.dialog),a.instanceApi.setData(r),a.instanceApi}),t),a=(n,s,r,a)=>kR.open(((n,i,l)=>{const c=SV(i,l),d=Ql(),u=e.backstages.popup.shared.header.isPositionedAtTop(),m=()=>d.on((e=>{Ph.reposition(e),$E.refresh(e)})),g=bV({dataValidator:l,initialData:c,internalDialog:n},{redial:kR.redial,closeWindow:()=>{d.on(Ph.hide),t.off("ResizeEditor",m),d.clear(),r(g.instanceApi)}},e.backstages.popup,a.ariaAttrs),p=ri(Ph.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:a.persistent?{event:"doNotDismissYet"}:{},...u?{}:{fireRepositionEventInstead:{}},inlineBehaviours:kl([Jp("window-manager-inline-events",[Ur(Cr(),((e,t)=>{Fr(g.dialog,bS)}))]),...CV(t,o,u)]),isExtraPart:(e,t)=>kV(t)}));return d.set(p),Ph.showWithinBounds(p,ai(g.dialog),{anchor:s},(()=>{const e=t.inline?xt():Ve(t.getContainer()),o=Jo(e);return A.some(o)})),o&&u||($E.refresh(p),t.on("ResizeEditor",m)),g.instanceApi.setData(c),Pp.focusIn(g.dialog),g.instanceApi}),n),i=(o,n,s,r)=>kR.open(((o,a,i)=>{const l=SV(a,i),c=Ql(),d=e.backstages.popup.shared.header.isPositionedAtTop(),u=()=>c.on((e=>{Ph.reposition(e),$E.refresh(e)})),m=bV({dataValidator:i,initialData:l,internalDialog:o},{redial:kR.redial,closeWindow:()=>{c.on(Ph.hide),t.off("ResizeEditor ScrollWindow ElementScroll",u),c.clear(),s(m.instanceApi)}},e.backstages.popup,r.ariaAttrs),g=ri(Ph.sketch({lazySink:e.backstages.popup.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:r.persistent?{event:"doNotDismissYet"}:{},...d?{}:{fireRepositionEventInstead:{}},inlineBehaviours:kl([Jp("window-manager-inline-events",[Ur(Cr(),((e,t)=>{Fr(m.dialog,bS)}))]),$E.config({contextual:{lazyContext:()=>A.some(Jo(Ve(t.getContentAreaContainer()))),fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top","bottom"],lazyViewport:e=>jw(t,e.element).map((e=>({bounds:Gw(e),optScrollEnv:A.some({currentScrollTop:e.element.dom.scrollTop,scrollElmTop:Xt(e.element).top})}))).getOrThunk((()=>({bounds:en(),optScrollEnv:A.none()})))})]),isExtraPart:(e,t)=>kV(t)}));return c.set(g),Ph.showWithinBounds(g,ai(m.dialog),{anchor:n},(()=>e.backstages.popup.shared.getSink().toOptional().bind((e=>{const o=jw(t,e.element).map((e=>Gw(e))).getOr(en()),n=Jo(Ve(t.getContentAreaContainer())),s=Qo(n,o);return A.some(Ko(s.x,s.y,s.width,s.height-15))})))),$E.refresh(g),t.on("ResizeEditor ScrollWindow ElementScroll",u),m.instanceApi.setData(l),Pp.focusIn(m.dialog),m.instanceApi}),o);return{open:(t,o,n)=>{if(!u(o)){if("toolbar"===o.inline)return a(t,e.backstages.popup.shared.anchors.inlineDialog(),n,o);if("bottom"===o.inline)return i(t,e.backstages.popup.shared.anchors.inlineBottomDialog(),n,o);if("cursor"===o.inline)return a(t,e.backstages.popup.shared.anchors.cursor(),n,o)}return r(t,n)},openUrl:(o,n)=>((o,n)=>kR.openUrl((o=>{const s=wV(o,{closeWindow:()=>{LF.hide(s.dialog),n(s.instanceApi)}},t,e.backstages.dialog);return LF.show(s.dialog),s.instanceApi}),o))(o,n),alert:(e,t)=>{n.open(e,t)},close:e=>{e.close()},confirm:(e,t)=>{s.open(e,t)}}};tn.add("silver",(e=>{(e=>{uf(e),(e=>{const t=e.options.register,o=e=>f(e,r)?{value:Bx(e),valid:!0}:{valid:!1,message:"Must be an array of strings."},n=e=>h(e)&&e>0?{value:e,valid:!0}:{valid:!1,message:"Must be a positive number."};t("color_map",{processor:o,default:["#BFEDD2","Light Green","#FBEEB8","Light Yellow","#F8CAC6","Light Red","#ECCAFA","Light Purple","#C2E0F4","Light Blue","#2DC26B","Green","#F1C40F","Yellow","#E03E2D","Red","#B96AD9","Purple","#3598DB","Blue","#169179","Dark Turquoise","#E67E23","Orange","#BA372A","Dark Red","#843FA1","Dark Purple","#236FA1","Dark Blue","#ECF0F1","Light Gray","#CED4D9","Medium Gray","#95A5A6","Gray","#7E8C8D","Dark Gray","#34495E","Navy Blue","#000000","Black","#ffffff","White"]}),t("color_map_background",{processor:o}),t("color_map_foreground",{processor:o}),t("color_cols",{processor:n,default:Nx(e)}),t("color_cols_foreground",{processor:n,default:Vx(e,Mx)}),t("color_cols_background",{processor:n,default:Vx(e,Dx)}),t("custom_colors",{processor:"boolean",default:!0}),t("color_default_foreground",{processor:"string",default:Ix}),t("color_default_background",{processor:"string",default:Ix})})(e),(e=>{const t=e.options.register;t("contextmenu_avoid_overlap",{processor:"string",default:""}),t("contextmenu_never_use_native",{processor:"boolean",default:!1}),t("contextmenu",{processor:e=>!1===e?{value:[],valid:!0}:r(e)||f(e,r)?{value:yB(e),valid:!0}:{valid:!1,message:"Must be false or a string."},default:"link linkchecker image editimage table spellchecker configurepermanentpen"})})(e)})(e);let t=()=>en();const{dialogs:o,popups:n,renderUI:s}=NF(e,{getPopupSinkBounds:()=>t()});Lw(e,n.backstage.shared);const a=OV({editor:e,backstages:{popup:n.backstage,dialog:o.backstage}});return{renderUI:async()=>{const o=await s();return jw(e,n.getMothership().element).each((e=>{t=()=>Gw(e)})),o},getWindowManagerImpl:x(a),getNotificationManagerImpl:()=>((e,t,o)=>{const n=t.backstage.shared,s=()=>{const t=Jo(Ve(e.getContentAreaContainer())),o=en(),n=Yi(o.x,t.x,t.right),s=Yi(o.y,t.y,t.bottom),r=Math.max(t.right,o.right),a=Math.max(t.bottom,o.bottom);return A.some(Ko(n,s,r-n,a-s))};return{open:(t,r)=>{const a=()=>{r(),Ph.hide(l)},i=ri(of.sketch({text:t.text,level:R(["success","error","warning","warn","info"],t.type)?t.type:void 0,progress:!0===t.progressBar,icon:t.icon,closeButton:t.closeButton,onAction:a,iconProvider:n.providers.icons,translationProvider:n.providers.translate})),l=ri(Ph.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:n.getSink,fireDismissalEventInstead:{},...n.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}}}));o.add(l),h(t.timeout)&&t.timeout>0&&Uh.setEditorTimeout(e,(()=>{a()}),t.timeout);const c={close:a,reposition:()=>{const t=ai(i),o={maxHeightFunction:cc()},r=e.notificationManager.getNotifications();if(r[0]===c){const e={...n.anchors.banner(),overrides:o};Ph.showWithinBounds(l,t,{anchor:e},s)}else I(r,c).each((e=>{const n=r[e-1].getEl(),a={type:"node",root:xt(),node:A.some(Ve(n)),overrides:o,layouts:{onRtl:()=>[cl],onLtr:()=>[cl]}};Ph.showWithinBounds(l,t,{anchor:a},s)}))},text:e=>{of.updateText(i,e)},settings:t,getEl:()=>i.element.dom,progressBar:{value:e=>{of.updateProgress(i,e)}}};return c},close:e=>{e.close()},getArgs:e=>e.settings}})(e,{backstage:n.backstage},n.getMothership())}}))}();