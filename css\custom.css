/* ----------------------------------------------------------------
	Custom CSS

	Add all your Custom Styled CSS here for New Styles or
	Overwriting Default Theme Styles for Better Handling Updates
-----------------------------------------------------------------*/
body {
  overflow-x: hidden !important;
}

/* ------------------------- fainance css--------------------------------------- */
.dotted-bg img {
  padding-bottom: 60px;
  width: 900px;
}

.color-2,
.h-color-2:hover {
  color: #fff !important;
}

.fbox-icon a i,
.fbox-icon a i:hover {
  color: #1541B0 !important;
}

.services-grid .feature-box {
  padding: 2rem;
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  border-radius: 0.5rem;
  border: 1px solid #267df4;
}

.services-grid .feature-box:hover {
  background-size: cover;
}

.hover-button {
  display: block;
  margin-top: 20px;
  font-weight: 600;
}

.dark .hover-button {
  color: #fff;
}

.services-grid .feature-box {
  background-color: #d4e5fd;
}

.dark .services-grid .feature-box .fbox-icon i {
  background-color: rgba(255, 255, 255, 0.15);
}

.services-grid .feature-box,
.services-grid .feature-box .fbox-icon,
.services-grid .feature-box .fbox-content,
.hover-button {
  transition: all 0.4s ease, border-color 0s ease;
}

.heading-block h3 {
  font-size: 2.1rem;
  line-height: 1.3 !important;
}

@media (min-width: 992px) {
  .services-grid .feature-box {
    padding: 3rem;
    background-size: 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .services-grid .feature-box:hover {
    transform: translateY(-6px);
    border-color: #264653;
  }

  .services-grid .feature-box:not(:hover) .hover-button {
    opacity: 0;
    transform: translateY(-5px);
  }

  .services-grid .feature-box .fbox-icon,
  .services-grid .feature-box .fbox-content {
    transform: translateY(20px);
  }

  .services-grid .feature-box:hover .fbox-icon,
  .services-grid .feature-box:hover .fbox-content {
    transform: translateY(0px);
  }

  .dark .services-grid .feature-box:hover .fbox-icon i {
    background-color: #fff;
  }
}

@media (max-width: 991.98px) {
  .services-grid .feature-box .fbox-icon i {
    background-color: #fff;
  }
}

/* -------------------------hero cards coworking css------------------------------------------- */
@media (min-width: 992px) {
  .hero-features {
    transform: translateY(-50%);
    /* padding: 50px 30px; */
    max-width: 100%;
    /* margin-left: auto; */
    border-left: 4px solid rgba(0, 0, 0, 0.2);
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
}

/* -------------------------furniture css--------------------------- */
.device-video-wrap {
  padding-top: 150px;
}

/* ----------------------------------------------------------------
	Canvas: Furniture
-----------------------------------------------------------------*/

:root {
  --cnvs-themecolor: #193532;
  --cnvs-themecolor-rgb: 25, 53, 50;
  --cnvs-themecolor-light: #ebf1f0;

  --cnvs-body-font: "hoss-round-wide", sans-serif !important;
  --cnvs-primary-font: "hoss-round-wide", sans-serif !important;
}

body:not(.dark),
h1,
h2,
h3,
h4,
h5,
h6,
p,
.fbox-content p {
  /* color: var(--cnvs-themecolor); */
}

/* .bg-color-light,
.bg-color-light .svg-underline::after {
  background-color: var(--cnvs-themecolor-light) !important;
} */

/* ---- Primary Menu ---- */
.is-expanded-menu .primary-menu:not(.on-click) .menu-item:not(.mega-menu) .sub-menu-container,
.is-expanded-menu .primary-menu:not(.on-click) .mega-menu-content {
  margin-top: -10px;
  border-top-width: 1px !important;
}

.is-expanded-menu .primary-menu:not(.on-click) .menu-item:hover>.sub-menu-container,
.is-expanded-menu .primary-menu:not(.on-click) .menu-item:hover>.mega-menu-content {
  margin-top: 0px;
}

.is-expanded-menu .sub-menu-container .menu-item>.menu-link {
  padding-left: 18px;
  padding-right: 18px;
  letter-spacing: 0px;
  font-size: 0.875rem;
  text-transform: none;
}

.is-expanded-menu .sub-menu-container.mega-menu-column:not(:first-child) {
  border-left: 0;
}

.is-expanded-menu .sub-menu-container:not(.mega-menu-column) .menu-item>.menu-link {
  text-transform: none;
  letter-spacing: 0;
  font-weight: 400 !important;
  font-size: 0.875rem;
}

.is-expanded-menu .sub-menu-container .menu-item>.menu-link div {
  position: relative;
  display: flex;
  align-items: center;
}

.is-expanded-menu .sub-menu-container .menu-link div>.sub-menu-indicator,
.is-expanded-menu.side-header .primary-menu:not(.on-click) .menu-link .sub-menu-indicator {
  position: relative;
  top: auto;
  right: auto;
  transform: none;
}

.is-expanded-menu .mega-menu-content .sub-menu-container.mega-menu-dropdown {
  top: 100% !important;
  left: 0;
  padding: 0;
}

.is-expanded-menu .sub-menu-container .menu-link div>i.icon-caret-down {
  position: relative;
  display: inline-block;
  margin-left: 1px;
  margin-top: -1px;
  transform: rotate(0deg);
}

.is-expanded-menu .primary-menu>.menu-container>.menu-item>.menu-link {
  position: relative;
}

/* Upper 992px Devices */
@media (min-width: 992px) {
  .shop-quick-view-ajax {
    max-width: 1320px;
    overflow-y: scroll;
    height: 100vh;
  }

  .content-sticky {
    position: -webkit-sticky !important;
    position: sticky !important;
    top: 40px;
    height: 100%;
  }
}

/* Upper 1200px Devices */
@media (min-width: 1200px) {
  .slider-element:not(.bg-color) {
    background-image: linear-gradient(to bottom,
        #375754 0%,
        var(--cnvs-themecolor) 84%,
        var(--cnvs-themecolor-light) 84%);
  }

  .hero-image {
    margin-right: 30px;
    width: 65%;
  }

  .slider-element>.container {
    position: absolute;
    left: 50%;
    top: 0;
    transform: translateX(-50%);
  }
}

.fbox-content p {
  line-height: 1.5 !important;
}

.section-title-color {
  color: #1541B0 !important;
}


.item-categories h5 {
  position: absolute;
  top: auto;
  left: 10px;
  bottom: 0;
  padding: 12px 22px;
}

/* Toggle */
.single-product .qv-toogle a {
  font-size: 1.15rem;
  transition: font-weight 0.2s;
}

.single-product .qv-toogle a:not(.collapsed) {
  font-weight: 500;
}

.single-product .qv-toogle p {
  font-size: 0.925rem;
}

.single-product .qv-toogle a.collapsed i:nth-child(2),
.single-product .qv-toogle a:not(.collapsed) i:nth-child(1) {
  display: none;
}

.quantity .plus,
.quantity .minus,
.quantity .qty {
  width: 46px;
  height: 46px;
  line-height: 46px;
  background-color: var(--cnvs-themecolor-light);
  color: var(--cnvs-themecolor);
  font-weight: 500;
}

.dark .quantity .plus,
.dark .quantity .minus,
.dark .quantity .qty {
  background-color: transparent !important;
  border: 1px solid #aaa;
  color: #fff;
}

.dark .quantity .qty {
  margin: 0 -1px;
}

.quantity .qty:out-of-range {
  border-color: red !important;
  z-index: 1;
  color: red;
}

.quantity .qty+.plus~.stock-in,
.quantity .qty+.plus~.stock-out {
  display: none;
  position: absolute;
  left: 0;
  bottom: -42px;
  font-size: 0.925rem;
  opacity: 0.9;
  text-transform: uppercase;
  letter-spacing: 1px;
  word-wrap: break-word;
  min-width: 500px;
}

.quantity .qty:in-range+.plus~.stock-in,
.quantity .qty:out-of-range+.plus~.stock-out {
  display: block;
}

.quantity .qty:out-of-range+.plus~.stock-out {
  opacity: 0.6;
}

@media (max-width: 767.98px) {

  .bg-overlay .bg-overlay-content,
  .bg-overlay .bg-overlay-content:not(.animated),
  .bg-overlay .bg-overlay-content.animated {
    opacity: 1 !important;
    -webkit-animation-name: fadeIn !important;
    animation-name: fadeIn !important;
  }
}

/* Ajax Modal - Quick View */
.mfp-wrap>.mfp-close {
  display: none;
}

.single-product .mfp-close {
  background-image: url("../images/cross.svg");
  background-size: 24px 24px;
  background-position: center center;
  background-repeat: no-repeat;
  opacity: 0.9;
}

.is-expanded-menu .mega-menu-column .sub-menu-indicator {
  display: none !important;
}

.is-expanded-menu .mega-menu:not(.mega-menu-small) .mega-menu-column {
  padding: 0;
}

/* Sort Dropdown */
.sortbuttons .button {
  min-width: 170px;
  background-color: #f5f5f5;
}

.sortbuttons>.button.show {
  background-color: var(--cnvs-themecolor);
  color: #fff;
}

.sortbuttons .dropdown-menu {
  margin-top: -3px !important;
  width: 100%;
}

.sortbuttons .dropdown-menu .dropdown-item {
  padding: 0.5rem 1.25rem;
  border-bottom: 1px solid #eee;
  font-size: 0.925rem;
}

.sortbuttons .dropdown-toggle::after {
  content: "\e7a7";
  font-family: "font-icons";
  border: 0;
  vertical-align: middle;
  margin: 0 0 0 6px;
}

/* Testimonials Carousel
-----------------------------------------------------------------*/
.testimonials-carousel .owl-stage {
  padding: 2rem 0 1.5rem;
}

.testimonials-carousel .owl-item {
  opacity: 0.6;
  transition: transform 0.3s ease;
  transform: scale(0.8);
}

.testimonials-carousel .owl-item.active.center {
  opacity: 1;
  transform: scale(1);
}

.testimonial {
  border: 0;
  box-shadow: 0 0 35px rgba(140, 152, 164, 0.2);
  border-radius: 0.25rem;
  padding: 25px;
}

.testimonial-image {
  width: 5rem !important;
  height: auto !important;
  margin: -2.5rem auto 0;
}

.widget_links li:hover a {
  color: #fff !important;
}

/* ------------------footer form---------------------------------------- */
/* Error message styling */
/* Error message styling */
.error-message {
  color: red;
  font-size: 12px;
  display: none;
}

/* Disable the button by default */
button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

button {
  background-color: #267df4;
  color: #fff;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

button:hover:not(:disabled) {
  background-color: #267df4;
}

.checkbox-wrapper {
  margin: 15px 0;
}

.product-title h4 a {
  font-size: 16px !important;
  line-height: 22px !important;
}

.product-title h4 {
  line-height: 22px !important;
}

.agree-text {
  font-size: 14px;
  line-height: 20px !important;
}

/* ===================================================
  Disclaimer and Company Introduction Styles
  ==================================================== */

/* Disclaimer Section Styling */
.disclaimer-section {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.disclaimer-section:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* Company Introduction Styling */
.company-intro {
  padding: 2rem 0;
}

.company-intro h2 {
  background: linear-gradient(135deg, #1541B0, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.company-intro .lead {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #495057;
}

.company-intro p {
  color: #6c757d;
  line-height: 1.6;
}

.company-cta .btn {
  transition: all 0.3s ease;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.company-cta .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.company-image img {
  transition: transform 0.3s ease;
  border: 3px solid #fff;
}

.company-image:hover img {
  transform: scale(1.02);
}

/* ===================================================
  Hero Bottom Rounded Elements Styles
  ==================================================== */

/* Hero Rounded Elements Container */
.hero-rounded-elements {
  z-index: 10;
  background: linear-gradient(to top, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 50%, transparent 100%);
}

/* Individual Rounded Element */
.rounded-element {
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  min-height: 70px;
}

.rounded-element:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  border-color: rgba(76, 59, 146, 0.2);
}

/* Element Icon Styling */
.element-icon i {
  background: linear-gradient(135deg, #4c3b92, #667eea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
}

.rounded-element:hover .element-icon i {
  transform: scale(1.1);
}

/* Element Content */
.element-content h6 {
  font-size: 0.9rem;
  margin-bottom: 2px;
}

.element-content small {
  font-size: 0.75rem;
  line-height: 1.2;
}

/* Responsive Design */
@media (max-width: 991.98px) {
  .hero-rounded-elements {
    position: relative !important;
    margin-top: 2rem;
    background: rgba(255, 255, 255, 0.95);
    padding: 1rem 0;
  }

  .rounded-element {
    margin-bottom: 0.5rem;
  }
}

@media (max-width: 767.98px) {
  .hero-rounded-elements .col-sm-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }

  .element-content h6 {
    font-size: 0.8rem;
  }

  .element-content small {
    font-size: 0.7rem;
  }

  .rounded-element {
    padding: 0.75rem !important;
    min-height: 60px;
  }
}

/* ===================================================
  New Hero Section Styles
  ==================================================== */

/* Hero Section Animations */
@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hero Section Styles */
.hero-section {
  position: relative;
  overflow: hidden;
}

.hero-content {
  animation: fadeInUp 1s ease-out;
}

.hero-title {
  line-height: 1.2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-badge .badge {
  font-size: 0.875rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.hero-main-img {
  transition: transform 0.3s ease;
  filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.2));
}

.hero-main-img:hover {
  transform: perspective(1000px) rotateY(-8deg) rotateX(8deg) scale(1.02);
}

.hero-floating-card {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.min-vh-75 {
  min-height: 75vh;
}

/* Responsive adjustments for hero */
@media (max-width: 991.98px) {
  .hero-section {
    min-height: 70vh !important;
    padding: 3rem 0 !important;
  }

  .hero-title {
    font-size: 2.5rem !important;
  }

  .hero-floating-card {
    position: relative !important;
    margin: 1rem 0 !important;
    left: auto !important;
    right: auto !important;
    top: auto !important;
    bottom: auto !important;
  }

  .hero-main-img {
    transform: none !important;
  }

  .hero-main-img:hover {
    transform: scale(1.02) !important;
  }
}

@media (max-width: 767.98px) {
  .hero-title {
    font-size: 2rem !important;
  }

  .hero-cta {
    justify-content: center;
  }

  .hero-cta .btn {
    width: 100%;
    margin-bottom: 0.5rem;
  }
}

/* ===================================================
  Print-Friendly Styles
  ==================================================== */

@media print {

  /* Hide non-essential elements during printing */
  header,
  .header,
  #header,
  nav,
  .navigation,
  .primary-menu,
  .menu,
  .navbar,
  .hero-cta,
  .btn,
  button,
  .floating-card,
  .hero-floating-card,
  .hero-bg-elements,
  .hero-wave,
  footer,
  .footer,
  #footer,
  .sidebar,
  .advertisement,
  .ads,
  .social-media,
  .share-buttons,
  .comments,
  .related-posts,
  .pagination,
  .search-form,
  .newsletter,
  .popup,
  .modal,
  .overlay,
  .back-to-top,
  .scroll-to-top,
  .video,
  iframe,
  .embed,
  .carousel,
  .slider,
  .testimonials-carousel,
  .hero-shape-1,
  .hero-shape-2,
  .hero-shape-3 {
    display: none !important;
  }

  /* Reset page styles for print */
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }

  /* Page setup */
  @page {
    margin: 1in;
    size: A4;
  }

  body {
    font-family: "Times New Roman", serif !important;
    font-size: 12pt !important;
    line-height: 1.5 !important;
    color: black !important;
    background: white !important;
  }

  /* Typography for print */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: black !important;
    page-break-after: avoid;
    font-weight: bold !important;
  }

  h1 {
    font-size: 24pt !important;
    margin-bottom: 12pt !important;
  }

  h2 {
    font-size: 20pt !important;
    margin-bottom: 10pt !important;
  }

  h3 {
    font-size: 16pt !important;
    margin-bottom: 8pt !important;
  }

  h4,
  h5,
  h6 {
    font-size: 14pt !important;
    margin-bottom: 6pt !important;
  }

  p {
    font-size: 12pt !important;
    line-height: 1.5 !important;
    margin-bottom: 12pt !important;
    orphans: 3;
    widows: 3;
  }

  /* Links */
  a {
    color: black !important;
    text-decoration: underline !important;
  }

  a[href]:after {
    content: " (" attr(href) ")";
    font-size: 10pt;
    color: #666 !important;
  }

  /* Images */
  img {
    max-width: 100% !important;
    height: auto !important;
    page-break-inside: avoid;
    border: 1px solid #ddd !important;
    padding: 4pt !important;
  }

  /* Tables */
  table {
    border-collapse: collapse !important;
    width: 100% !important;
    margin-bottom: 12pt !important;
  }

  th,
  td {
    border: 1px solid #ddd !important;
    padding: 8pt !important;
    text-align: left !important;
  }

  th {
    background-color: #f5f5f5 !important;
    font-weight: bold !important;
  }

  /* Lists */
  ul,
  ol {
    margin-bottom: 12pt !important;
    padding-left: 20pt !important;
  }

  li {
    margin-bottom: 4pt !important;
  }

  /* Page breaks */
  .page-break {
    page-break-before: always;
  }

  .no-page-break {
    page-break-inside: avoid;
  }

  /* Content containers */
  .container,
  .content,
  .main-content,
  .article,
  .post-content {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    float: none !important;
  }

  /* Product information for print */
  .product,
  .product-desc,
  .product-title,
  .product-price {
    display: block !important;
    margin-bottom: 8pt !important;
  }

  .product-title h4 {
    font-size: 14pt !important;
    margin-bottom: 4pt !important;
  }

  .product-price {
    font-weight: bold !important;
    font-size: 12pt !important;
  }

  /* Services and features */
  .feature-box,
  .service-item {
    margin-bottom: 12pt !important;
    page-break-inside: avoid;
  }

  .fbox-content h3,
  .fbox-content h4 {
    margin-bottom: 6pt !important;
  }

  /* Hide decorative elements */
  .bg-overlay,
  .overlay,
  .gradient,
  .shadow,
  .rounded,
  .border-radius {
    background: none !important;
    box-shadow: none !important;
    border-radius: 0 !important;
  }

  /* Ensure text is readable */
  .text-white,
  .text-light,
  .text-muted {
    color: black !important;
  }

  /* Print-specific utilities */
  .print-only {
    display: block !important;
  }

  .no-print {
    display: none !important;
  }

  /* Company info for print header */
  .print-header {
    display: block !important;
    text-align: center;
    margin-bottom: 20pt !important;
    border-bottom: 2pt solid black !important;
    padding-bottom: 10pt !important;
  }

  .print-header h1 {
    font-size: 20pt !important;
    margin-bottom: 4pt !important;
  }

  .print-header p {
    font-size: 10pt !important;
    margin-bottom: 2pt !important;
  }
}

/* ===================================================
  Services Section Styles
  ==================================================== */

/* Services Navigation Tabs */
.services-nav .nav-link {
  border: 2px solid transparent;
  border-radius: 50px;
  padding: 12px 20px;
  font-weight: 600;
  color: #6c757d;
  background: #f8f9fa;
  transition: all 0.3s ease;
  margin: 0 5px;
}

.services-nav .nav-link:hover {
  background: #e9ecef;
  color: #495057;
  transform: translateY(-2px);
}

.services-nav .nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.services-nav .nav-link i {
  font-size: 1.1em;
}

/* Services Content */
.services-content {
  margin-top: 2rem;
}

.service-content h3 {
  color: #2c3e50;
  position: relative;
}

.service-content h3:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
}

.service-features .feature-item {
  padding: 10px 0;
}

.service-features .feature-item i {
  font-size: 1.2em;
}

.service-features h6 {
  color: #2c3e50;
  margin-bottom: 2px;
}

.service-cta .btn {
  border-radius: 50px;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.service-cta .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.service-image img {
  transition: transform 0.3s ease;
}

.service-image img:hover {
  transform: scale(1.05);
}

/* Section styling */
.section-title-color {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Responsive adjustments for services */
@media (max-width: 991.98px) {
  .services-nav .nav-link {
    padding: 10px 15px;
    margin: 2px;
    font-size: 0.9rem;
  }

  .service-content {
    text-align: center;
    margin-bottom: 2rem;
  }

  .service-content h3:after {
    left: 50%;
    transform: translateX(-50%);
  }
}

@media (max-width: 767.98px) {
  .services-nav {
    flex-direction: column;
  }

  .services-nav .nav-link {
    margin: 5px 0;
    text-align: center;
  }

  .service-cta .btn {
    width: 100%;
    margin-bottom: 10px;
  }
}

/* ===================================================
  responsive css
  ==================================================== */
@media (max-width: 912px) {
  .hero-features {
    /* padding: 20px; */
  }

  .hero-text-div {
    text-align: center !important;
  }

  .hero-list {
    display: flex;
  }

  .hero-list .row {
    padding-left: 30px;
  }

  .hero-list .header-misc {
    order: 0 !important;
  }

  .dotted-bg img {
    padding-bottom: 0px;
    margin-bottom: 0 !important;
  }

  /* --------------product------------------------------- */
  #productid {
    padding-top: 0 !important;
    margin-top: 0 !important;
    text-align: center;
  }

  /* ---------------------------------------------- */
  .cards-content h4,
  .cards-content p {
    color: #fff !important;
  }

  /* ----------------------------------------------- */
  #contactid .container {
    padding: 10px !important;
  }

  .contact-content {
    padding-top: 0 !important;
  }

  /* ------------------------------------------------ */
  .widget_links li {
    justify-content: left !important;
  }

  #contact-form {
    padding-top: 40px;
  }

  #footer .footer-widgets-wrap {
    padding: 30px 0 0;
  }

  #copyrights .copyright-div2 {
    text-align: left !important;
  }
}

/* %%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%% */
@media (width: 1024px) {
  #content .container {
    padding: 20px !important;
  }
}

@media (width:540px) {

  .product .product-image>a,
  .product .product-image .slide a,
  .product .product-image img {
    height: 240px;
  }
}

/* --------------------privacy page ------------------------------- */
.privacy-header {
  width: 100%;
  /* height: 100%; */
  padding-top: 130px;
  padding-bottom: 120px;
  background: linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.5)), url('../images/privacypage.jpg');
  background-repeat: repeat;
  background-position: center center;
  background-size: cover;
}

.privacy-header h2 {
  color: #ffffff;
  margin-bottom: 0;
  font-size: 40px;
  font-weight: 600;
}

.parsley-error-message {
  color: red;
  font-size: 0.9em;
}

.is-invalid {
  border-color: red;
}

#toast-container {
  position: fixed;
  z-index: 999999;
  pointer-events: none;
  top: 20px;
  /* Adjust based on where you want it vertically */
  right: 40px;
  /* This will position it on the left side */
  width: auto;
  /* Allow it to adjust based on content */
}

.toast {
  padding: 16px;
  border-radius: 5px;
  color: white;
  margin-bottom: 10px;
  position: relative;
  min-width: 250px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Success toast - green */
.toast-success {
  background-color: green;
}

/* Error toast - red */
.toast-error {
  background-color: red;
}

/* Common close button styling */
.toast-close-button {
  position: absolute;
  top: 5px;
  right: 5px;
  background: transparent;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
}

/* Toast progress bar */
.toast-progress {
  height: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  position: absolute;
  bottom: 0;
  left: 0;
}

.form-control {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.parsley-error {
  border-color: #e74c3c;
}

.parsley-error-message {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 5px;
  display: block;
}

.checkbox-wrapper {
  margin-top: 10px;
}

.agree-text {
  font-size: 0.875rem;
  margin-top: 5px;
}

.form-scroll {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
}


.form-scroll::-webkit-scrollbar {
  width: 8px;
}

.form-scroll::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.form-scroll::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.modal-background {
  background-image: url('../images/modal1.png') !important;
  background-size: cover;
  min-height: 619px;
  background-repeat: no-repeat;
  background-position: center right;
}

.grecaptcha-badge {
  width: 256px;
  height: 60px;
  display: block;
  transition: right 0.3s ease 0s;
  position: fixed;
  bottom: 14px;
  box-shadow: gray 0px 0px 5px;
  border-radius: 2px;
  overflow: hidden;
  left: -186px !important;
}

.rc-anchor-logo-portrait {
  margin: 0px !important;

}

.form-control:focus {
  box-shadow: none !important;
}