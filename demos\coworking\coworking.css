/* ----------------------------------------------------------------
	Canvas: CoWorking
-----------------------------------------------------------------*/

:root {
	--cnvs-themecolor: #267DF4;
	--cnvs-themecolor-rgb: 38, 125, 244;
	--cnvs-body-font: 'Poppins', sans-serif;
	--cnvs-primary-font: 'Poppins', sans-serif;
	--cnvs-secondary-font: 'Poppins', serif;
}

.ls--2 { letter-spacing: -2px; }

.bg-theme-light,
.section {
	background-color: rgba(var(--cnvs-themecolor-rgb, 38, 125, 244), 0.05);
}

.menu-container > .menu-item > .menu-link {
	font-size: 15px;
	padding-left: 20px;
	padding-right: 20px;
	font-weight: 400;
	text-transform: capitalize;
	letter-spacing: 0;
}

.top-phone {
	background-color: var(--cnvs-themecolor, #267DF4);
	color: #FFF;
	padding: 10px 14px;
	border-radius: 3px;
}

.top-phone i {
	position: relative;
	top: 2px;
}

.top-phone span {
	font-weight: 400;
	margin-left: 4px;
}

.hero-features {
	transform: translateY(-20%);
	/* padding: 25px; */
	border-radius: 6px;
}

.hero-features i {
	font-size: 24px;
	line-height: 1.4;
}

.hero-features h5 {
	font-size: 15px;
	line-height: 1.6;
}

.after-dots:not(:first-child)::after {
	content: '';
	position: absolute;
	top: 50%;
	left: 0%;
	width: 10px;
	height: 10px;
	border-radius: 50%;
	font-size: 20px;
	background-color: #000;
	margin-top: -5px;
	margin-left: -5px;
}

.before-heading {
	display: inline-block;
	font-size: 12px;
	font-style: normal;
	background-color: rgba(var(--cnvs-themecolor-rgb, 38, 125, 244), .1);
	color: var(--cnvs-themecolor, #267DF4);
	padding: 3px 8px;
	border-radius: 2px;
	font-weight: 600;
}

.border-light { border-color: #EEE !important; }

.card { border-radius: 8px; }
.card-img-top {
	border-top-left-radius: calc(8px - 1px);
	border-top-right-radius: calc(8px - 1px);
}

#price-carousel .owl-stage {
	display: flex;
	flex-wrap: wrap;
	align-items: flex-end;
}

.price-list .card-body { padding: 20px 30px !important; }

.price-list .price-title {
	font-size: 48px;
	font-weight: 600;
	color: var(--cnvs-themecolor, #267DF4);
}

.price-list .price-title small {
	position: relative;
	font-size: 16px;
	color: #AAA;
	top: -4px;
	margin-left: 6px;
}

.price-list .list-group-item:first-child,
.price-list .list-group-item:last-child { border-bottom: 0; }

.card-img-overlay * { position: relative; }

.card-img-overlay::before {
	content: '';
	position: absolute;
	display: block;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(4,4,4,.45);
	border-top-left-radius: 0.25rem;
	border-top-right-radius: 0.25rem;
}

.owl-carousel .owl-dots .owl-dot {
	width: 20px;
	height: 5px;
	border-radius: 4px;
	transition: all .3s ease-out;
	opacity: .3;
}

.owl-carousel .owl-dots .owl-dot.active { width: 45px; }

#price-carousel .owl-item {
	opacity: 0;
	transform: translateY(20px);
	transition: all .4s .1s ease-out;
}

#price-carousel .owl-item.active {
	opacity: 1;
	transform: translateY(0);
}

#image-carousel { overflow: visible; }

#image-carousel .carousel-column {
	display: flex;
	flex-direction: column;
	flex: 1 0;
	margin: 0;
	padding: 0;
	list-style: none;
}
#image-carousel .column-1 { padding-right: 12px; }
#image-carousel .column-2 { padding-left: 12px; }

#image-carousel .carousel-column-container {
	display: flex;
	flex-direction: row;
}

#image-carousel .carousel-grid-item {
	display: flex;
	flex: 1;
	margin: 0;
	padding: 0;
}

#image-carousel .carousel-grid-item .item {
	flex: 1;
	position: relative;
	width: 100%;
	min-height: 150px;
	margin-bottom: 24px;
	background-repeat: no-repeat;
	background-position: center center;
	background-size: cover;
	border-radius: 5px;
	transition: all .2s ease-out;
}

.button:hover,
.top-phone:hover,
#image-carousel .carousel-grid-item:hover a.item {
	box-shadow: 0 5px 8px rgba(0, 0, 0, 0.2);
	transform: translateY(-3px);
	opacity: .95;
	color: #FFF;
}

#image-carousel .carousel-grid-item .item blockquote { z-index: 1; }

.img-overlay::after {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border-radius: 5px;
	background-color: rgba(var(--cnvs-themecolor-rgb, 38, 125, 244), 0.8);
}

.testimonials .container::before,
.testimonials .container::after {
	content: "\F6B0";
	font-family: 'bootstrap-icons';
	position: absolute;
	top: -38px;
	left: 20px;
	font-size: 80px;
	color: rgba(var(--cnvs-themecolor-rgb, 38, 125, 244), 0.2);
}

.testimonials .container::after {
	content: "\e7ae";
	top: auto;
	left: auto;
	right: 20px;
	bottom: -45px;
}

.testimonials .testi-content p {
	font-style: normal;
	color: #000;
}

.mfp-iframe-holder .mfp-close,
.testi-stars i {
	background-color: var(--cnvs-themecolor, #267DF4);
	font-size: 14px;
	color: #FFF;
	padding: 1px 5px;
	border-radius: 2px;
}

.fbox-plain.fbox-sm {
	margin-bottom: 40px;
}

.fbox-icon { top: 3px; }

.fbox-plain.fbox-sm h3 {
	text-transform: none;
	font-weight: 600;
	font-size: 1.125rem;
	letter-spacing: 0;
}

.top-phone,
.button { transition: all .2s ease-out; }

.button.button-border {
	border-color: var(--cnvs-themecolor, #267DF4);
	color: var(--cnvs-themecolor, #267DF4);
}

.play-video i {
	position: absolute;
	padding-left: 5px;
	top: 50%;
	left: 50%;
	width: 80px;
	height: 80px;
	line-height: 81px;
	border-radius: 50%;
	z-index: 1;
	background-color: var(--cnvs-themecolor, #267DF4);
	text-align: center;
	font-size: 24px;
	color: #FFF;
	margin-top: -40px;
	margin-left: -40px;
	transition: transform .3s ease-out;
	-webkit-backface-visibility: hidden;
}

.play-video:hover i {
	-webkit-transform: scale(1.1);
	-ms-transform: scale(1.1);
	-o-transform: scale(1.1);
	transform: scale(1.1);
}

.mfp-container { position: fixed; }
.mfp-iframe-holder .mfp-close {
	width: 40px;
	text-align: center;
	font-size: 24px;
	top: -20px;
	right: -20px;
}
.mfp-iframe-holder { padding: 20px; }
.mfp-iframe-holder .mfp-content {
	max-width: 100%;
	height: 100%;
}

#locations-carousel .owl-stage-outer { padding-bottom: 40px  }

#locations-carousel .owl-item.active .card {
	opacity: 1;
	transform: translateY(0px);
}

#locations-carousel.owl-carousel .owl-nav [class*=owl-] {
	opacity: 1;
	background-color: #FFF;
	color: var(--cnvs-themecolor, #267DF4);
}

#locations-carousel.owl-carousel .owl-dots {
	position: relative;
	background: #FFF;
	width: 150px;
	padding: 10px;
	margin: 0 auto;
	bottom: 20px;
	border-radius: 4px;
	box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

#locations-carousel.owl-carousel .owl-dots .owl-dot { margin: 0 4px; }

.entry-content p {
	display: -webkit-box;
	max-width: 100%;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	color: #666;
}

#footer .list-unstyled li a {
	opacity: .7;
	color: #FFF;
	font-weight: 300;
}

ul.list-numbers li {
	list-style: decimal inside;
	margin-bottom: 20px;
}

#locations-carousel .card {
	position: relative;
	right: auto;
	margin: 0 auto;
	bottom: 0;
	margin-top: -30px;
	padding: 20px;
}

.footer-img { margin-top: -90px; }

/* Responsive Device less than 768px (.device-sm <)
-----------------------------------------------------------------*/
@media (max-width: 767.98px) {
	.footer-img { margin-top: 0; }
}

/* Responsive Device more than 992px (.device-md >)
-----------------------------------------------------------------*/
@media (min-width: 992px) {

	.hero-features {
		transform: translateY(-10%);
		/* padding: 40px 30px; */
		max-width: 100%;
		margin-left: auto;
		border-left: 4px solid rgba(0, 0, 0, .2);
		border-top-right-radius: 0px;
		border-bottom-right-radius: 0px;
	}

	.sub-menu-container .menu-item > .menu-link {
		padding-left: 20px;
		padding-right: 20px;
	}

	#locations-carousel .card {
		opacity: 0;
		position: absolute;
		right: 40px;
		bottom: -30px;
		padding: 20px 30px;
		width: 100%;
		max-width: 320px;
		border: 0;
		border-radius: 4px;
		transform: translateY(20px);
		transition: all .4s .1s ease-out;
	}

	.heading-block h2 {
		font-size: 42px;
		letter-spacing: -1px;
	}

	#locations-carousel.owl-carousel .owl-dots {
		position: absolute;
		top: auto;
		left: 50%;
		bottom: 45px;
		margin-left: -75px;
	}

}

#page-menu {
	height: 0;
	overflow: hidden;
	-webkit-backface-visibility: hidden;
}

#page-menu #page-menu-wrap {
	opacity: 0;
	right: 0;
	width: auto;
	background-color: transparent;
	box-shadow: none !important;
	pointer-events: none;
	-webkit-transition: all .4s ease;
	-o-transition: all .4s ease;
	transition: all .4s ease;
	-webkit-transform: translate3d(0,-44px,0);
	-o-transform: translate3d(0,-44px,0);
	transform: translate3d(0,-44px,0);
	transform-style: preserve-3d;
	-webkit-backface-visibility: hidden;
}

#page-menu.sticky-page-menu { overflow: visible; }

#page-menu.sticky-page-menu #page-menu-wrap {
	opacity: 1;
	pointer-events: auto;
	-webkit-transform: translate3d(0,0,0);
	-o-transform: translate3d(0,0,0);
	transform: translate3d(0,0,0);
}

.list-numbers {
	counter-reset: item;
}

ol.list-numbers li {
	font-size: 18px;
	list-style-type: none;
	margin-top: 1.25rem;
	font-family: var(--cnvs-primary-font);
	font-weight: 400;
}

ol.list-numbers li::before {
	content: counter(item) ". ";
	counter-increment: item;
	margin-right: 7px;
	color: var(--cnvs-themecolor, #267DF4);
	font-weight: 700;
}