<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInitd03bda99e9c2020adfb84190593780f8
{
    public static $prefixLengthsPsr4 = array (
        'P' => 
        array (
            '<PERSON><PERSON><PERSON>ailer\\PHPMailer\\' => 20,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'PHPMailer\\PHPMailer\\' => 
        array (
            0 => __DIR__ . '/..' . '/phpmailer/phpmailer/src',
        ),
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInitd03bda99e9c2020adfb84190593780f8::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInitd03bda99e9c2020adfb84190593780f8::$prefixDirsPsr4;

        }, null, ClassLoader::class);
    }
}
