<!-- Footer
		============================================= -->
<footer id="footer" class="bg-dark border-0 text-white">
    <div class="container">

        <!-- Footer Widgets
				============================================= -->
        <div class="footer-widgets-wrap">

            <div class="row justify-content-between">
                <div class="col-lg-5 col-12 mb-5 mb-lg-0">
                    <div class="widget">

                        <div class="fw-semibold font-primary  ls-3 h2 text-uppercase mb-4 text-white">
                            <!-- Logo
						============================================= -->
                            <div id="logo" class="mx-0 me-lg-5 order-2 order-lg-1">
                                <a href="<?php echo $base_url; ?>index.php">
                                    <img class="logo-default mx-0" src="<?php echo $base_url; ?>images/footer_logo.png"
                                        alt="Canvas Logo" height="45" width="210">
                                    <img class="logo-dark mx-0" src="<?php echo $base_url; ?>images/footer_logo.png"
                                        alt="Canvas Logo" height="45" width="210">
                                </a>
                            </div><!-- #logo end -->
                        </div>
                        <p class="mb-4 op-07">INNFINITIX ONLINE SERVICES LLP is a fast flourishing company that perks an
                            exceptional skill of lead generation using competitive Tele-Calling, Advanced Sales
                            Strategies and Digital Marketing methods to strengthen the impact and achieve desired
                            demands of our clients.</p>

                        <!-- <a href="#" class="social-icon si-small bg-light h-bg-facebook">
                            <i class="fa-brands fa-facebook-f"></i>
                            <i class="fa-brands fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-icon si-small bg-light h-bg-delicious">
                            <i class="fa-brands fa-delicious"></i>
                            <i class="fa-brands fa-delicious"></i>
                        </a>
                        <a href="#" class="social-icon si-small bg-light h-bg-paypal">
                            <i class="fa-brands fa-paypal"></i>
                            <i class="fa-brands fa-paypal"></i>
                        </a>
                        <a href="#" class="social-icon si-small bg-light h-bg-waze">
                            <i class="fa-brands fa-waze"></i>
                            <i class="fa-brands fa-waze"></i>
                        </a> -->

                    </div>
                </div>
                <div class="col-lg-7 col-12 d-flex justify-content-end">

                    <div class="row w-100">
                        <div class="col-lg-4 col-6">
                            <ul class="widget_links widget-li-noicon text-white px-4">
                                <li class="mb-2 fw-medium text-white"><a href="#homeid">Home</a></li>
                                
                                <li class="mb-2 fw-medium"><a href="#productid">Products</a></li>
                                <li class="mb-2 fw-medium"><a href="#serviceid">Services</a></li>
                                <li class="mb-2 fw-medium"><a href="#whatwedoid">Our
                                        Expertise</a></li>
                                <li class="mb-2 fw-medium"><a href="#testimonialsid">Testimonials</a></li>
                            </ul>
                        </div>


                        <div class="col-lg-8 col-12">

                            <form id="contact-form">
                                <div class="row">
                                    <div class="col-12 form-group mb-4">
                                        <label for="email2">Email <small>*</small></label>
                                        <input type="email" id="email2" name="email2"
                                            class="required email form-control">
                                        <span class="error-message" id="email2-error"></span>
                                    </div>

                                    <div class="col-12 form-group mb-4">
                                    <div class="checkbox-wrapper" style="display: flex; align-items: center;">
                                        <label for="agree2" style="color: red; margin-right: 5px;">*</label> <!-- Asterisk added here -->
                                        <input type="checkbox" id="agree2" name="agree2">
                                        <label for="agree2" class="ps-3">I agree to receive communications from Innfinitix</label>
                                    </div>

                                        <span class="agree-text">By clicking submit below, you consent to allow
                                            Innfinitix to send
                                            communications and to store and process the personal information
                                            submitted above. Read our <a href="https://innfinitix.com/PrivacyPolicy.php" target="_blank" class="text-primary">Privacy Policy</a> here.</span>
                                        <span class="error-message" id="checkbox2-error"></span>
                                    </div>

                                    <div class="col-12 form-group mb-0">
                                        <button class="btn btn-dark bg-color px-4 py-2 rounded-pill border-0"
                                            type="submit" id="second-button" disabled>
                                            <i class="bi-envelope color-2 ms-1"></i> Submit
                                        </button>
                                    </div>
                                </div>

                            </form>
                        </div>
                    </div>

                </div>
            </div>

        </div><!-- .footer-widgets-wrap end -->

    </div>

    <!-- Copyrights
			============================================= -->
    <div id="copyrights" class="bg-dark text-white">
        <div class="container">

            <div class="row justify-content-between">

                <div class="col-lg-10 col-12 copyright-div1">
                    <span class="text-white">&copy; 2024 INNFINITIX ONLINE SERVICES LLP. All Rights Reserved. The
                        information in this website/document is for promotional purposes only and not contractual. No
                        part of this website/document may be reproduced or transmitted in any form or by any means
                        without the prior written permission of INNFINITIX ONLINE SERVICES LLP</span>

                </div>

                <div class="col-lg-2 col-12 text-end copyright-div2">
                    <div class="copyright-links text-white">
                        <a href="https://innfinitix.com/TermAndCondition.php" target="_blank">Terms of Use</a> / 
                        <a href="https://innfinitix.com/PrivacyPolicy.php" target="_blank" class="text-white">Privacy Policy</a>
                    </div>
                </div>
                <span class="text-white py-2" style="font-size:12px"> INNFINITIX ONLINE SERVICES LLP is an independent
                    printer support
                    company, unaffiliated with any manufacturers. All brand names, trademarks, and logos on our site
                    belong to their respective owners and are used for reference only. Our expert technicians
                    provide
                    support and repair services for various printers, but we do not sell or endorse any specific
                    brand.
                    By using our services, you agree that INNFINITIX ONLINE SERVICES LLP is not responsible for
                    warranties or guarantees from original manufacturers. For official support and warranties,
                    contact
                    the manufacturers directly.
                
                </span>
                <span class=" py-2">
                  <strong>Information collected: </strong> <br>
                  <strong>Webforms: </strong>Innfinitix.com uses webforms forms on this site. These forms require users to give contact information 
                    [First and last name/ Email address/ Phone Number/ Printer Model]
                    Contact information from the registration form is used to send material relating to the Service for which it was collected and may be shared with our advertising partners or other third parties for advertising purposes.  
                    If you complete an intake form, we collect the following personal information:  
                    [First and last name/ Email address/ Phone Number/ Printer Model]
                    </span>
                    <span>
                    <strong>Analytics:</strong> Innfinitix.com servers collect the following analytics: <br>
                        • Anonymized conversion data through customer action button<br>
                   <strong> Third Party Data Collection: </strong>In addition, our website utilizes Microsoft Advertising, and we created our website using Smart Pages. Microsoft will collect and share the following information with [Company Name] in accordance with their Privacy Statement at <a href="https://privacy.microsoft.com/en-us/privacystatement" target="_blank" class="text-primary">Microsoft Privacy Statement – Microsoft privacy.</a>   <br>
                        • conversion data if you click a customer action button from an ad<br>
                        • personal data associated with the action button, including: [First and last name/ Email address/ Phone Number/ Printer Model]<br>
                    </span>
                    <span class="py-2">
                    Microsoft will use the above information in accordance with their Privacy Statement at <a href="https://privacy.microsoft.com/en-us/privacystatement" target="_blank" class="text-primary">Microsoft Privacy Statement – Microsoft privacy</a> for the following purposes:  <br>
                        • to show you ads that are more personalized<br>
                        • to improve their advertising products and services  <br>
                        • Smart Page website optimization and troubleshooting<br>
                    Microsoft account users can manage their ad settings and opt-out of personalized advertising at:  <a href="https://privacy.microsoft.com/en-us/privacystatement" target="_blank" class="text-primary">Microsoft account | Privacy</a>  <br> 
                    </span>

            </div>

        </div>
    </div><!-- #copyrights end -->
</footer><!-- #footer end -->

</div><!-- #wrapper end -->

<!-- Go To Top
	============================================= -->
<!-- <div id="gotoTop" class="uil uil-angle-double-up bg-color h-bg-color-2 shadow rounded-circle"></div> -->

<!-- JavaScripts
	============================================= -->
<!-- <script src="../js/plugins.min.js"></script> -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script src="https://www.google.com/recaptcha/api.js?render=6LfpA1QqAAAAADYTcIaWr8o1BpZvcGWJM7iQg-IN"></script>
<!-- Parsley.js -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/parsley.js/2.9.2/parsley.min.js"></script>

<!-- Toastr.js for notifications -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>


<script src="<?php echo $base_url; ?>js/plugins.min.js"></script>
<script src="<?php echo $base_url; ?>js/functions.bundle.js"></script>
<!-- jQuery -->
<script src="//code.jivosite.com/widget/9j4AhHGSKq" async></script>
<script>
jQuery(document).ready(function() {
    var lastScrollTop = 0;
    window.addEventListener("scroll", function(event) {
        var st = jQuery(this).scrollTop();
        if (st > lastScrollTop) {
            jQuery('#header.sticky-on-scrollup').removeClass('show-sticky-onscroll'); // Down Scroll
        } else {
            jQuery('#header.sticky-on-scrollup').addClass('show-sticky-onscroll'); // Up Scroll
        }
        lastScrollTop = st;
    });

    jQuery('.services-grid .feature-box').hover(
        function() {
            jQuery(this).addClass("dark");
        },
        function() {
            jQuery(this).removeClass("dark");
        }
    );
});
</script>

<script>
const nameInput = document.getElementById('name');
const email1Input = document.getElementById('email1');
const messageInput = document.getElementById('message');
const agree1Checkbox = document.getElementById('agree1');
const email2Input = document.getElementById('email2');
const agree2Checkbox = document.getElementById('agree2');
const firstButton = document.getElementById('first-button');
const secondButton = document.getElementById('second-button');
const nameError = document.getElementById('name-error');
const email1Error = document.getElementById('email1-error');
const messageError = document.getElementById('message-error');
const checkbox1Error = document.getElementById('checkbox1-error');
const email2Error = document.getElementById('email2-error');
const checkbox2Error = document.getElementById('checkbox2-error');

function validateForm() {
    let isValid = true;

    // Section 1 validation
    // Name validation
    if (!nameInput.value) {
        nameError.textContent = "Please complete this required field.";
        nameError.style.display = 'block';
        isValid = false;
    } else {
        nameError.style.display = 'none';
    }

    // Email validation
    if (!email1Input.value || !email1Input.checkValidity()) {
        email1Error.textContent = "Please enter a valid email address.";
        email1Error.style.display = 'block';
        isValid = false;
    } else {
        email1Error.style.display = 'none';
    }

    // Message validation
    if (!messageInput.value) {
        messageError.textContent = "Please complete this required field.";
        messageError.style.display = 'block';
        isValid = false;
    } else {
        messageError.style.display = 'none';
    }

    // Checkbox validation
    if (!agree1Checkbox.checked) {
        checkbox1Error.textContent = "Please agree to receive communications.";
        checkbox1Error.style.display = 'block';
        isValid = false;
    } else {
        checkbox1Error.style.display = 'none';
    }

    // Enable/disable first button
    firstButton.disabled = !(nameInput.value && email1Input.value && messageInput.value && agree1Checkbox.checked &&
        isValid);

    // Section 2 validation
    // Email validation
    if (!email2Input.value || !email2Input.checkValidity()) {
        email2Error.textContent = "Please enter a valid email address.";
        email2Error.style.display = 'block';
        isValid = false;
    } else {
        email2Error.style.display = 'none';
    }

    // Checkbox validation
    if (!agree2Checkbox.checked) {
        checkbox2Error.textContent = "Please agree to receive communications.";
        checkbox2Error.style.display = 'block';
        isValid = false;
    } else {
        checkbox2Error.style.display = 'none';
    }

    // Enable/disable second button
    secondButton.disabled = !(email2Input.value && agree2Checkbox.checked && isValid);
}

// Check on input change for Section 1
nameInput.addEventListener('input', validateForm);
email1Input.addEventListener('input', validateForm);
messageInput.addEventListener('input', validateForm);
agree1Checkbox.addEventListener('change', validateForm);

// Check on input change for Section 2
email2Input.addEventListener('input', validateForm);
agree2Checkbox.addEventListener('change', validateForm);

// On form submit
document.getElementById('combined-form').addEventListener('submit', function(event) {
    // You can add custom logic here to handle submissions from different buttons if needed
});
</script>
<script>
    // Configure Toastr options
    toastr.options = {
        closeButton: true,
        debug: false,
        newestOnTop: false,
        progressBar: true,
        positionClass: "toast-top-right",
        preventDuplicates: true,
        showDuration: "300",
        hideDuration: "1000",
        timeOut: "5000",
        extendedTimeOut: "1000",
        showEasing: "swing",
        hideEasing: "linear",
        showMethod: "fadeIn",
        hideMethod: "fadeOut"
    };
    $(document).ready(function () {
    // Handle form submission
    $('#subscribe-form').on('submit', function (e) {
        e.preventDefault(); 

        if ($('#subscribe-form').parsley().isValid()) {
            $.ajax({
                url: 'send_email.php', 
                type: 'POST',
                dataType: 'json', 
                data: $(this).serialize(),
                success: function (response) {
                    if (response.success) {
                        
                        window.location.href = 'thankyou.php';
                    } else {
                        toastr.error(response.message || 'An error occurred. Please try again.', 'Error');
                    }
                },
                error: function () {
                    toastr.error('An error occurred while submitting the form. Please try again.', 'Error');
                }
            });
        } else {
            toastr.error('Please complete all required fields correctly.', 'Validation Error');
        }
    });


    // Reset form when modal is closed
    $('#myModal').on('hidden.bs.modal', function () {
        $('#subscribe-form')[0].reset();  // Reset the form
        $('#subscribe-form').parsley().reset(); // Reset Parsley validation messages
    });
});


</script>
<script>
    $(document).ready(function () {
    $('#submit-button').on('click', function (e) {
        e.preventDefault(); // Prevent the default form submission

        // Check if form is valid
        if ($('#template-contactform')[0].checkValidity()) {
            // If valid, perform the AJAX call
            $.ajax({
                url: 'contact_email.php', // PHP file to process the form
                type: 'POST',
                dataType: 'json',
                data: $('#template-contactform').serialize(), // Serialize form data
                beforeSend: function () {
                    // Show the loading spinner or disable the submit button
                    $('.form-process').fadeIn();
                    $('#submit-button').prop('disabled', true);
                },
                success: function (response) {
                    $('.form-process').fadeOut(); // Hide the loading spinner
                    $('#submit-button').prop('disabled', false); // Enable the submit button

                    if (response.success) {
                        // Redirect to thank you page instead of showing the toastr message
                        window.location.href = 'thankyou.php';
                    } else {
                        toastr.error(response.message, 'Error');
                    }
                },
                error: function () {
                    $('.form-process').fadeOut(); // Hide the loading spinner
                    $('#submit-button').prop('disabled', false); // Enable the submit button
                    toastr.error('An error occurred while submitting the form. Please try again.', 'Error');
                }
            });
        } else {
            toastr.error('Please fill in all required fields correctly.', 'Validation Error');
        }
    });
});

    $(document).ready(function () {
        // Initialize Parsley validation
        $('#subscribe-form').parsley();
    });

</script>

</body>

</html>